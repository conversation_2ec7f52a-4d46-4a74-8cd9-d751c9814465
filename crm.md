好的，我将为你设计一个基于 Vue + FastAPI + PostgreSQL 的公司级 CRM 系统架构，支持阿里云部署、SaaS 多租户、角色权限控制、API 对外开放以及 OSS 文件存储和版本管理需求，并包括适配开发人员的数据库表设计和结构文档。
我完成后会提供详细的架构建议、性能注意事项、数据库构型方案以及开发文档结构，供你的技术团队直接参考。


## 系统架构设计与部署方案

\*\*架构概述：\*\*采用前后端分离模式，前端使用 Vue.js 构建 SPA（单页应用），后端由 FastAPI 提供 API 服务。整体采用 SaaS 多租户架构，即一个后端实例服务多个租户但保证数据隔离。为了提高可扩展性与可靠性，建议按业务模块拆分为若干微服务（或模块）并容器化部署，每个微服务都是一个独立的 FastAPI 应用，通过网关聚合对外提供统一 API。前端通过 HTTPs 调用后端提供的 RESTful 或 GraphQL 接口，与后端解耦部署，保证应用的扩展性和灵活性。

\*\*微服务划分建议：\*\*根据业务功能将系统划分多个子服务，典型分层如下：

* \*\*认证与租户服务(Auth & Tenant Service)：\*\*负责多租户管理、用户账户、角色权限等。该服务处理用户登录/认证（JWT/OAuth2）、租户隔离逻辑，在认证成功后签发附带租户ID和角色信息的JWT令牌。
* \*\*CRM核心服务(CRM Service)：\*\*主要业务功能所在，包括客户管理、申请(Application)流程、任务管理、文书撰写进度、网申进度、Offer管理等模块。针对不同租户，该服务通过租户ID过滤数据，实现数据隔离（可由中间件自动添加租户过滤或使用数据库机制确保隔离）。
* \*\*文件管理服务(File Service)：\*\*与阿里云OSS对接的独立服务，用于文件上传、下载、版本管理。也可实现为CRM服务的一个子模块，但独立服务有助于解耦文件处理（尤其是大量文件传输时减小对核心业务服务的影响）。文件服务处理OSS签名URL生成、回调通知等，方便前端直接与OSS交互。

上述服务可通过**API网关或Nginx反向代理**对外统一暴露接口路径，实现统一域名路由和负载均衡。API网关还可用于实现接口鉴权、流量控制等安全策略。各服务之间通过REST API或RPC通信，必要时可以使用消息队列（如处理异步任务、通知等）。

\*\*FastAPI部署：\*\*在阿里云上可采用容器化部署方案。使用Docker将各FastAPI服务封装为镜像，并部署到阿里云容器服务（ACK）或弹性容器实例。推荐使用Uvicorn+Gunicorn作为ASGI服务器运行FastAPI应用，利用多workers提升并发处理能力。例如，通过Gunicorn启动多个Uvicorn worker进程，让单个容器利用多核CPU。然后利用阿里云负载均衡（SLB）将流量分发到多实例的FastAPI服务，实现水平扩展和高可用。部署拓扑上，建议在前端与后端之间使用负载均衡+反向代理：例如Nginx或API网关挂载在SLB后，将请求按路径转发到不同服务（如`/api/auth`到Auth服务，`/api/crm`到CRM服务等）。所有服务应启用HTTPS，使用阿里云SSL证书服务配置证书，保证数据传输安全。

**前后端联动：**前端Vue应用通过Ajax请求或GraphQL查询与后端通信。若采用**RESTful API**，后端为各模块提供清晰的REST接口（如`GET /api/customers/`获取客户列表，`POST /api/tasks/`创建任务等）。若采用**GraphQL**，可在API网关层整合各微服务的schema，提供统一的GraphQL接口，客户端按需查询数据。对于微信小程序等客户端，可以使用REST API（并配置允许小程序域名的CORS）。前端从后端获取JWT等凭证存储在安全存储（不要长期存放在localStorage以防泄露），每次请求附带在Authorization头中。前端上传文件时，可先向后端文件服务请求OSS签名直传URL，然后直接PUT文件至OSS，这样大文件不经过后端，以提高效率。总之，前端所有操作均通过后端开放的API完成，不直接访问数据库，实现了前后端松耦合和协同。

## 数据库架构与表设计

**数据库选型与模式：**采用PostgreSQL (阿里云RDS) 作为主要数据库。多租户架构下，强烈建议**单数据库+逻辑隔离**模式，即所有租户共享一个数据库实例，各业务表通过`tenant_id`字段区分归属租户。这种**字段隔离**方式相比为每租户单独schema更易扩展和维护：schema隔离在租户和表数量增长时迁移和性能会遇到瓶颈（例如>100个租户且每个schema有上百表时，迁移操作耗时随N线性增长）。实践证明，当租户数较多时，schema隔离会导致数据库对象数量膨胀、迁移困难以及内存开销增大等问题，最终许多团队放弃schema隔离改用列（字段）隔离模式。因此本设计采用每张业务表增加`tenant_id`列，并在应用层**始终加入租户过滤**保证数据隔离。如果需要更严格的隔离，可考虑PostgreSQL的\*\*行级安全（RLS）\*\*机制：为每个租户创建对应的数据库角色，并设置RLS策略使该角色只能访问特定`tenant_id`的行。RLS可以从数据库层强制隔离租户数据，避免开发者遗漏过滤条件，同时结合视图或安全函数简化查询。

\*\*核心表结构：\*\*下面列出主要功能模块涉及的核心表及字段设计。字段类型以PostgreSQL为参考。

* **租户表（tenants）**：保存租户的基本信息。

  * `tenant_id` (UUID/serial 主键)：租户ID。
  * `name` (varchar)：租户名称（公司名等）。
  * `created_at` (timestamp)：开通时间。
  * `status` (varchar)：租户状态（active, suspended等），用于控制租户有效性。

* **用户表（users）**：系统用户账号信息。

  * `user_id` (UUID/serial 主键)：用户ID。
  * `tenant_id` (外键 references tenants)：所属租户ID，实现租户内用户归属。
  * `username` (varchar)：登录用户名。
  * `password_hash` (varchar)：密码哈希值（采用加盐bcrypt等方式存储）。
  * `email` (varchar)：用户邮箱。
  * `role` (varchar 或外键到角色表)：用户角色（如consultant顾问/manager管理员/writer文书老师等）。
  * `is_active` (boolean)：是否激活。
  * `created_at` (timestamp)：用户创建时间。
  * 索引：建议对 `(tenant_id, username)` 建唯一索引确保同租户用户名唯一。

* **角色表（roles）**：定义系统角色和权限。预置“顾问”、“管理人员”、“文书老师”等角色，支持将来扩展。

  * `role_id` (serial 主键)：角色ID。
  * `name` (varchar)：角色名称（如 "consultant", "manager", "writer"）。
  * `description` (text)：角色描述。
  * **用户-角色关系**：如果支持多角色，可增加用户角色关联表(user\_roles)：`user_id`, `role_id`组成多对多关系。简单情况下每用户单一角色则在users表直接存role。

* **客户表（customers）**：存储CRM核心的客户信息（即租户的客户，比如学生或顾客）。

  * `customer_id` (UUID/serial 主键)：客户ID。
  * `tenant_id` (外键)：所属租户。
  * `name` (varchar)：客户姓名。
  * `contact_info` (jsonb或varchar)：联系信息（电话、邮箱等，可用JSON存多种联系渠道）。
  * `status` (varchar)：客户状态（意向阶段、签约、服务中、完成等）。
  * `assigned_consultant` (外键 references users)：负责该客户的顾问用户ID。
  * `created_at` (timestamp)：录入时间。
  * 索引：按tenant\_id分区或索引，以及assigned\_consultant索引用于查询自己客户。

* **申请表（applications）**：跟踪客户的每个申请项目进展（如某客户申请某学校的记录）。

  * `app_id` (UUID/serial 主键)：申请记录ID。
  * `tenant_id` (外键)：所属租户。
  * `customer_id` (外键)：关联客户ID。
  * `target` (varchar)：申请目标名称（如学校或项目名称）。
  * `stage` (varchar)：当前申请所处阶段（如 “文书撰写中”, “网申填写中”, “已提交”, “结果已出”等）。
  * `status` (varchar)：申请状态（active/成功/失败等，或细化为Offer结果）。
  * `assigned_consultant` (外键)：负责该申请的顾问ID。
  * `assigned_writer` (外键)：负责文书的文书老师用户ID。
  * `submitted_at` (timestamp)：网申提交时间（若已提交）。
  * `result_at` (timestamp)：结果接收时间。
  * `notes` (text)：备注。
  * 索引：tenant\_id+customer\_id组合索引、按assigned人员索引。

* **文书任务表（documents）**：针对申请的文书撰写进度管理。每个申请可能包含多份文书材料（个人陈述、推荐信等），每份作为一条记录。

  * `doc_id` (UUID 主键)：文书任务ID。
  * `tenant_id`：所属租户。
  * `app_id` (外键 references applications)：所属申请。
  * `type` (varchar)：文书类型（如 "Personal Statement", "CV", "Recommendation Letter" 等）。
  * `status` (varchar)：文书状态（"未开始", "撰写中", "待审核", "已完成" 等）。
  * `latest_version_file` (外键 references materials)：当前文书的最新稿件文件ID（存储在材料表）。
  * `updated_at` (timestamp)：最后更新时间。
  * `assigned_writer` (外键 references users)：负责该文书的文书老师用户ID（可选，若不同文书不同负责人）。
  * 备注：文书内容本身可存于OSS文件，documents表主要跟踪状态和元数据。

* **任务表（tasks）**：一般任务管理，用于分配和跟踪各类待办事项（非文书的其他任务，如提醒、资料收集等）。

  * `task_id` (UUID 主键)：任务ID。
  * `tenant_id`：所属租户。
  * `customer_id` (外键)：相关客户（任务关联到某客户）。
  * `app_id` (外键, 可为空)：关联申请ID（如果此任务是某申请流程的一部分，否则为空表示客户的一般任务）。
  * `title` (varchar)：任务标题。
  * `description` (text)：任务详情描述。
  * `priority` (varchar)：优先级/紧急程度（如 "高", "中", "低"，或者颜色代码如红黄绿表示紧急程度）。
  * `status` (varchar)：任务状态（"待处理", "进行中", "已完成"等）。
  * `due_date` (date)：截止日期。
  * `assigned_to` (外键 references users)：任务负责人用户ID。
  * `created_at` (timestamp)：创建时间。
  * `completed_at` (timestamp)：完成时间。
  * \*\*颜色紧急度：\*\*可通过priority字段或单独color字段设置，如 "red"/"yellow"/"green" 来表示不同紧急级别，在前端以不同颜色标识。

* **材料表（materials）**：用于存储客户的各类材料文件信息（身份证明、成绩单、作品集等）以及申请相关附件，集成OSS存储和版本管理。

  * `file_id` (UUID 主键)：文件ID。
  * `tenant_id`：所属租户。
  * `customer_id` (外键)：所属客户。
  * `app_id` (外键，可为空)：关联申请（如果此材料特定用于某个申请）。
  * `name` (varchar)：文件名称（原始文件名或描述）。
  * `file_type` (varchar)：文件类型/类别（如护照、成绩单、照片等）。
  * `oss_key` (varchar)：文件在OSS中的对象键（路径）。
  * `version_id` (varchar，可选)：OSS上的版本ID（如果开启OSS版本控制，可记录当前版本的ID）。
  * `uploaded_by` (外键 references users)：上传者用户ID。
  * `uploaded_at` (timestamp)：上传时间。
  * \*\*版本管理：\*\*开启OSS Bucket版本控制后，OSS会为每次覆盖上传保存历史版本。materials表可不单独保存历史版本记录，只需存当前文件引用；如需显示历史版本列表，可通过OSS提供的API按`oss_key`查询历史版本。提到开启版本控制后，覆盖和删除操作都会以历史版本形式保存，可将对象恢复至任意历史版本，因此系统可以调用OSS接口获取历史版本用于版本浏览和恢复。

* **Offer表（offers）**：记录申请结果管理（Offer录取/结果信息）。

  * `offer_id` (UUID 主键)：Offer记录ID。
  * `tenant_id`：所属租户。
  * `app_id` (外键 references applications)：关联申请ID。
  * `result` (varchar)：结果类型（如 "Offer获录取", "Waiting List", "Rejected" 等）。
  * `details` (text)：Offer详情（如奖学金情况、入学条件等说明）。
  * `received_at` (timestamp)：Offer收到时间。
  * `uploaded_offer_material` (外键 references materials)：关联Offer通知书文件等（如果有PDF等上传）。
  * （注：如果Offer信息简单也可直接存于applications表的结果字段，这里独立Offers表是为了扩展性和管理集中已录取数据）。

上述是核心表，此外可能还有一些辅助表：如**活动日志表**（记录用户操作日志）、**通知表**（存储系统消息通知），**权限表**（细化到功能点的权限配置）等，视需求扩展。

\*\*字段说明与范式：\*\*所有表均包含`tenant_id`用于数据隔离，必要时在组合索引中加入tenant\_id以优化查询。主键采用UUID保证全局唯一，以避免多租户情况下不同租户记录ID冲突（各租户数据如果用序列ID，会出现不同租户间有相同ID的问题，不利于跨租户管理或数据迁移）。使用UUID还能方便文件命名等。外键关系由数据库维护完整性，并使用ON DELETE CASCADE等策略辅助清理数据。数据库范式遵循第三范式，避免冗余。此外在PostgreSQL中，可以考虑针对大表和历史数据做分区或归档，如按租户或日期做PARTITION，以提升查询和维护效率（尤其任务、日志等易增长表）。

## 多租户实现方式与权限控制设计

**多租户隔离方案：**如上所述，采用**字段（列）隔离**模式，通过`tenant_id`区分数据归属，并**在应用层确保所有查询都带上tenant条件**。可在FastAPI中间件层根据当前请求用户所属租户，自动给SQLAlchemy查询过滤tenant\_id，减少开发人员重复添加筛选的负担，避免遗漏。为了更安全，可利用PostgreSQL行级安全（RLS）策略：在每张表上启用RLS，根据当前数据库用户或设置的租户上下文策略过滤数据。一种做法是为每个租户创建对应的数据库ROLE，应用在收到请求后执行`SET ROLE`切换为租户角色，这个角色只被授予自家租户行的访问权限，从而即便开发人员遗漏过滤条件，数据库也不会返回非本租户数据。RLS方案需要额外管理数据库用户及权限，但提供了保险的隔离层。

**租户上下文传递：**用户登录后，系统会知道其所属的tenant\_id，在后续请求中通过JWT载荷或session将tenant\_id传递。比如，JWT中包含`"tid": 租户ID`，每次API调用后端提取该tid用于查询过滤。对于RESTful接口，可采用**子域名或路径表示租户**（如`tenantA.crm.com`或统一域名下`/api/{tenant_id}/...`），但由于已经有认证机制，直接在token中包含tenant信息更方便。在代码实现上，可使用依赖注入，在FastAPI的依赖中获取当前用户和tenant，从而在业务逻辑里确保数据查询/写入都绑定tenant。

**用户角色与权限：**系统采用RBAC（基于角色的访问控制）模型。也就是预定义不同角色对应的权限范围，用户被授予某角色后即拥有该角色的一组权限。设计上，**角色表**定义角色，**权限表**（optional）定义具体权限点，如“查看客户列表”、“编辑客户”、“删除任务”等操作，然后**角色-权限关联**表配置每种角色拥有哪些权限。为了简化，也可不单独建权限表，而是在代码中写死各角色能进行的操作。如果需求可能变化，建议配置化权限。FastAPI可以利用Dependency机制，在路由定义时声明所需权限，自动检查当前用户角色是否符合。例如：定义依赖`get_current_user(role: str)`，在路由依赖中传入所需最低权限，函数内部验证`current_user.role`。管理员角色通常拥有所有权限，顾问可能只能管理自己客户的数据，文书老师只能查看/编辑文书模块等。具体策略例如：

* \*\*顾问（Consultant）：\*\*可管理自己名下客户的资料、创建任务、更新客户申请进度等，但可能无权查看其他顾问的客户。
* \*\*管理人员（Manager/Admin）：\*\*租户管理员，拥有该租户内所有数据的读写权限，可分配客户、查看所有任务和申请，管理用户账号等。
* \*\*文书老师（Writer）：\*\*可以查看其负责的客户申请资料、上传编辑文书材料、更新文书进度，但可能无法修改客户的其他信息（如联系方式等）。
* \*\*扩展角色：\*\*系统预留角色表，可日后增加新角色（例如“财务”、“运营”等），通过赋予不同权限实现功能隔离。

权限控制的细粒度设计：可以在后端通过装饰器/依赖检查实现。例如对`/api/customers` GET接口，要求用户具有“查看客户”权限，对于POST/PUT接口要求“编辑客户”权限，否则返回HTTP 403禁止。前端也应据此隐藏无权操作。**数据级权限**（如顾问只能见自己客户）可通过查询时使用`WHERE customer.assigned_consultant = current_user.id`实现，或者更通用地采用租户内的组织架构/团队归属关系来限定数据范围。

\*\*API 安全策略：\*\*对外开放API时需采取多层安全措施：

* \*\*认证机制：\*\*采用基于OAuth2的JWT认证方案。用户登录时提交用户名、密码，经验证后由Auth服务颁发JWT Access Token和Refresh Token。Access Token包含用户ID、tenant\_id、角色等声明，签名后下发给前端（有效期较短，例如1小时）；Refresh Token有效期长（如7天），用于获取新Token。后端每次收到请求通过Authorization头携带的JWT验证签名和有效期，验证通过才允许访问受保护接口。JWT的优点是服务端无状态、易于扩展微服务（各服务共享公共签名密钥即可验证）。（JWT本身不加密而是签名校验，可确保token未被篡改）。
* \*\*授权与输入校验：\*\*除了JWT认证，还需对用户操作进行授权校验（如上RBAC）。同时对所有客户端输入进行严格校验（FastAPI使用Pydantic模型校验数据格式），防止SQL注入、XSS等攻击。开启FastAPI自带的OpenAPI文档可以帮助规范接口输入输出格式，并通过定义数据模型确保类型安全。
* \*\*传输安全：\*\*强制使用HTTPS，以防止中间人攻击窃听token或敏感数据。可以在阿里云上配置负载均衡的证书或在Nginx处终止TLS。小程序等接口也需配置TLS，以及在微信小程序后台配置合法域名。
* \*\*防护措施：\*\*考虑启用速率限制（Rate Limiting）防止恶意刷接口，可使用API网关自带的流控策略或在应用中基于IP/用户做限流。对于关键管理接口（如登录、重置密码），加入验证码机制防止暴力破解。所有登录尝试、重要操作应有审计日志记录。
* \*\*接口版本与生命周期：\*\*对外API最好加版本号（如`/api/v1/...`）以方便将来演进。对旧版API逐步废弃时给予向后兼容或提示。

**文件安全与访问控制：**材料文件存储在OSS上，为防止未经授权的访问，OSS访问应采用**私有Bucket**配合**临时授权**方式。即Bucket权限设置为私有，前端需要访问文件时，由后端File服务根据当前用户权限向OSS请求**签名URL**（带有时效性的访问令牌）供前端下载。上传文件时也类似，后端验证用户有上传权限后向OSS请求一个带签名的上传URL（或STS临时凭证），前端用该URL PUT文件流至OSS。这样避免将长久公开URL暴露，同时后端可以审计每次文件访问请求。开启OSS版本控制后，文件删除和覆盖都有历史版本，可在需要时提供文件恢复功能。对于病毒扫描等，可接入阿里云文件杀毒服务或在上传后放入隔离区扫描。

## 阿里云部署与性能优化建议

\*\*基础设施选择：\*\*部署在阿里云上，可以使用阿里云容器服务Kubernetes版（ACK）以获得弹性伸缩和微服务编排管理能力。也可选择弹性ECS集群+Docker自行管理容器。如果团队熟悉K8s，推荐ACK，将各FastAPI服务封装为Deployment部署，利用Horizontal Pod Autoscaler自动扩缩容。ACK还方便对接阿里云各类服务（OSS挂载、日志服务等）。对于不熟悉K8s的小团队，也可以直接采用阿里云的轻量级应用服务器或ECS实例运行Docker Compose部署服务。

\*\*负载均衡：\*\*使用阿里云SLB（Server Load Balancer）或ALB(Application Load Balancer)将流量分发到后端服务集群，实现高可用和扩展性。在K8s中可以创建LoadBalancer类型的Service，自动对接SLB。负载均衡器前端配置HTTPS证书终止TLS。可以设置基于域名或URL路径的转发规则，将不同路径路由到不同后端服务池。例如`/api/auth/*`到Auth服务，`/api/*`其余请求到主CRM服务等。如果使用API网关产品，也可以利用其自带的流量控制、A/B测试等高级功能。

\*\*OSS 集成：\*\*阿里云OSS用于存储用户上传的文件资料和文书附件。为提高访问性能和节省带宽，可开启OSS的CDN加速（阿里云CDN将静态文件缓存到各地）。在应用中通过OSS SDK（如阿里云Python SDK）进行文件上传下载管理。开启Bucket的版本控制功能以支持文件历史版本找回。例如开启版本控制后，每次对同名文件上传不会直接覆盖旧文件，而是生成新版本，旧版本可通过版本ID检索。这样满足材料的历史版本管理需求。需要注意版本控制会增加存储费用，可结合OSS生命周期管理自动清理陈旧版本。上传大文件时，可使用OSS分片上传以提高成功率。考虑开启OSS的服务端加密确保文件静态加密存储。应用服务器应妥善保存OSS凭证（推荐使用RAM子账户授权方式，限制权限仅允许访问特定Bucket）。

**数据库性能调优：**阿里云RDS for PostgreSQL提供了**性能洞察和自治服务DAS**，可用于日常性能监控和自动优化。启用慢SQL日志跟踪，定期通过DAS查看慢查询并优化索引和SQL。\*\*索引策略：\*\*针对常用查询添加索引，例如各表的tenant\_id，以及用户、客户关联的外键字段；任务表可按负责人或状态加索引；材料表按customer\_id索引方便列出某客户所有文件等。慎重添加索引，避免因为写入频繁导致开销过大，可依据查询频次调整。**连接池与并发：**Python应用使用SQLAlchemy或async驱动连接数据库时，注意设置合理的连接池大小并发请求。避免过多**空闲连接**占用内存。如果并发用户很多，可考虑在应用层或中间层使用连接池工具（如PgBouncer）以复用连接。PgBouncer作为轻量级连接池可以减少频繁建立连接的开销，提升数据库吞吐。在RDS参数中，可适当调高`max_connections`（默认100），但更推荐使用连接池减少实际连接数。还要定期维护`autovacuum`参数，确保及时清理死排和统计信息更新。

**RDS读写分离与扩展：**如果系统读流量很大，可以开通RDS的只读实例，将报表查询、列表查询等流量导向只读节点。FastAPI应用可以根据查询性质选择连主库或只读库。利用读写分离减轻主库压力。对于超大规模数据，可能需要分库分表或引入缓存，中小规模CRM通常RDS单实例足够，应充分利用其**自动扩容**能力（RDS支持存储空间阈值自动扩容）。监控方面，开启CloudMonitor对RDS的CPU、IOPS、连接数等指标报警，当负载接近瓶颈时及时扩容实例规格。

**容器化与CI/CD：**使用Docker打包应用，建议基于Python官方镜像制作，分离依赖文件。可以利用阿里云ACR容器镜像服务托管镜像，并结合Terraform或阿里云ROS编排资源。配置CI/CD流水线，每次代码变更自动运行测试、构建镜像并推送ACR，然后通过ACK滚动更新部署，实现快速迭代。利用**阿里云日志服务**收集容器日志，方便排查线上问题；使用**ARMS应用监控**对接口调用进行链路跟踪和性能分析。

\*\*其他建议：\*\*开启阿里云应用高可用服务AHAS或服务网格MSE（Microservice Engine）以便对微服务调用进行治理（熔断、限流）。对于邮件或短信通知，可使用阿里云邮件推送或短信服务，与应用通过其API集成发送Offer结果通知等。定期备份：开启RDS自动备份策略，同时重要文件也在OSS上做异地冗余（开启多AZ或跨区域备份）。安全层面，除了应用自身鉴权，也要利用阿里云WAF防火墙保护接口防御常见Web攻击和Bot。

## 数据库表结构开发文档（Markdown 格式）

以下是主要数据库表的结构定义和字段说明，供开发参考：

### tenants（租户表）

| 字段名         | 类型           | 描述           |
| ----------- | ------------ | ------------ |
| tenant\_id  | UUID (PK)    | 租户唯一标识       |
| name        | VARCHAR(100) | 租户名称         |
| status      | VARCHAR(20)  | 状态（如active等） |
| created\_at | TIMESTAMP    | 租户创建时间       |
| updated\_at | TIMESTAMP    | 更新信息时间       |

\*\*说明：\*\*记录每个租户（客户公司）的基本信息。租户之间数据隔离，通过tenant\_id关联到各业务表。

### users（用户表）

| 字段名            | 类型                       | 描述                        |
| -------------- | ------------------------ | ------------------------- |
| user\_id       | UUID (PK)                | 用户唯一标识                    |
| tenant\_id     | UUID (FK→tenants)        | 所属租户ID                    |
| username       | VARCHAR(50)              | 用户登录名（同租户内唯一）             |
| password\_hash | VARCHAR(200)             | 密码哈希                      |
| email          | VARCHAR(100)             | 邮箱                        |
| role           | VARCHAR(20) (或 FK→roles) | 用户角色，如consultant/manager等 |
| is\_active     | BOOLEAN                  | 账号是否启用                    |
| created\_at    | TIMESTAMP                | 创建时间                      |
| last\_login    | TIMESTAMP                | 上次登录时间                    |

\*\*说明：\*\*存储系统用户账号。通过tenant\_id区分所属租户。role可以是一个枚举或外键引用角色表。建议对(tenant\_id, username)建唯一索引。密码使用安全哈希存储，绝不以明文保存。

### roles（角色表）

| 字段名         | 类型          | 描述   |
| ----------- | ----------- | ---- |
| role\_id    | SERIAL (PK) | 角色ID |
| name        | VARCHAR(50) | 角色名称 |
| description | TEXT        | 角色描述 |

\*\*说明：\*\*预定义系统角色。比如顾问（Consultant）、管理人员（Manager）、文书老师（Writer）等。可扩展新的角色。实际权限由代码逻辑或权限表决定。

*(若实现多对多用户角色关系，则有user\_roles中间表： user\_id, role\_id).*

### customers（客户表）

| 字段名                  | 类型              | 描述                  |
| -------------------- | --------------- | ------------------- |
| customer\_id         | UUID (PK)       | 客户ID                |
| tenant\_id           | UUID (FK)       | 所属租户ID              |
| name                 | VARCHAR(100)    | 客户姓名                |
| contact\_info        | JSONB           | 联系方式（电话、邮箱等JSON）    |
| status               | VARCHAR(50)     | 客户状态（意向/签约/服务中/完成等） |
| assigned\_consultant | UUID (FK→users) | 负责顾问用户ID            |
| created\_at          | TIMESTAMP       | 创建时间（录入时间）          |
| updated\_at          | TIMESTAMP       | 最后更新时间              |

\*\*说明：\*\*租户的最终客户（如学生、客户企业等）信息。每个客户由某顾问负责。状态字段用于标记客户所处阶段。联系信息用JSONB存储灵活多字段（或拆分phone/email字段）。

### applications（申请表）

| 字段名                  | 类型                  | 描述                   |
| -------------------- | ------------------- | -------------------- |
| app\_id              | UUID (PK)           | 申请记录ID               |
| tenant\_id           | UUID (FK)           | 所属租户ID               |
| customer\_id         | UUID (FK→customers) | 关联客户ID               |
| target               | VARCHAR(100)        | 申请目标名称（如学校/项目名称）     |
| stage                | VARCHAR(50)         | 当前阶段（如“文书处理中”、“已提交”） |
| status               | VARCHAR(20)         | 申请状态（进行中/成功/失败 等）    |
| assigned\_consultant | UUID (FK→users)     | 顾问ID负责此申请            |
| assigned\_writer     | UUID (FK→users)     | 文书老师ID（负责该申请文书）      |
| submitted\_at        | TIMESTAMP           | 提交时间（网申提交）           |
| result               | VARCHAR(50)         | 结果（如Offer获得/拒绝等）     |
| result\_at           | TIMESTAMP           | 结果接收时间               |
| notes                | TEXT                | 备注说明                 |
| created\_at          | TIMESTAMP           | 创建时间                 |
| updated\_at          | TIMESTAMP           | 最后更新时间               |

\*\*说明：\*\*每条记录代表客户的一次申请流程。可根据stage和status跟踪进度和结果。assigned\_consultant/assigned\_writer表示参与人员。result字段简单记录结果类别，详细Offer信息存在offers表或notes。

### documents（文书任务表）

| 字段名                   | 类型                     | 描述                         |
| --------------------- | ---------------------- | -------------------------- |
| doc\_id               | UUID (PK)              | 文书任务ID                     |
| tenant\_id            | UUID (FK)              | 所属租户ID                     |
| app\_id               | UUID (FK→applications) | 所属申请ID                     |
| type                  | VARCHAR(100)           | 文书类型（如Personal Statement等） |
| status                | VARCHAR(20)            | 状态（未开始/撰写中/待审核/已完成等）       |
| latest\_version\_file | UUID (FK→materials)    | 当前最新文书文件ID                 |
| assigned\_writer      | UUID (FK→users)        | 文书负责人用户ID（文书老师）            |
| created\_at           | TIMESTAMP              | 创建时间（任务生成时间）               |
| updated\_at           | TIMESTAMP              | 最近更新时间                     |

\*\*说明：\*\*针对申请过程中的文书写作任务进行跟踪。一个申请可能有多项文书（不同类型）。latest\_version\_file引用当前最新版的文书文件（存储在OSS）。历史版本文件通过OSS版本控制获取。文书老师更新文书后可上传新版本文件并更新此引用。status用于标识文书当前所处进度。

### tasks（任务表）

| 字段名           | 类型                        | 描述                       |
| ------------- | ------------------------- | ------------------------ |
| task\_id      | UUID (PK)                 | 任务ID                     |
| tenant\_id    | UUID (FK)                 | 所属租户ID                   |
| customer\_id  | UUID (FK→customers)       | 关联客户（此任务涉及的客户）           |
| app\_id       | UUID (FK→applications) 可空 | 关联申请ID（如属于某申请的任务）        |
| title         | VARCHAR(200)              | 任务标题                     |
| description   | TEXT                      | 任务描述详细内容                 |
| priority      | VARCHAR(10)               | 优先级（如High/Medium/Low或颜色） |
| status        | VARCHAR(20)               | 任务状态（待处理/进行中/已完成等）       |
| due\_date     | DATE                      | 截止日期                     |
| assigned\_to  | UUID (FK→users)           | 指派负责人用户ID                |
| created\_at   | TIMESTAMP                 | 创建时间                     |
| completed\_at | TIMESTAMP                 | 完成时间（已完成时填写）             |

\*\*说明：\*\*通用任务待办事项表，用于跟踪除申请文书外的其他工作任务。可以是客户跟进、材料收集等。priority可通过不同值对应前端不同颜色标记紧急程度。任务可关联到某客户或某具体申请（如办理签证任务属于某申请），若app\_id为空则是客户总体相关任务。assigned\_to为具体负责执行的用户。

### materials（材料表）

| 字段名          | 类型                        | 描述                    |
| ------------ | ------------------------- | --------------------- |
| file\_id     | UUID (PK)                 | 文件ID（材料ID）            |
| tenant\_id   | UUID (FK)                 | 所属租户ID                |
| customer\_id | UUID (FK→customers)       | 关联客户ID                |
| app\_id      | UUID (FK→applications) 可空 | 关联申请ID（若此材料针对特定申请）    |
| name         | VARCHAR(200)              | 文件名称（原始文件名或描述）        |
| file\_type   | VARCHAR(50)               | 文件类型（类别），如证件照/成绩单等    |
| oss\_key     | VARCHAR(255)              | OSS对象键（唯一定位文件的路径/名称）  |
| version\_id  | VARCHAR(100)              | OSS版本ID（当前版本，如启用版本控制） |
| size         | INTEGER                   | 文件大小（字节）              |
| uploaded\_by | UUID (FK→users)           | 上传者用户ID               |
| uploaded\_at | TIMESTAMP                 | 上传时间                  |
| remarks      | TEXT                      | 备注（如版本变动说明）           |

\*\*说明：\*\*保存各类文件在OSS中的元数据。文件实际内容存储在OSS，通过oss\_key访问。若Bucket开启版本控制，version\_id保存最新版本标识。上传新版本时更新version\_id，同时旧版本OSS仍可获取。应用可通过OSS SDK查询历史版本列表实现版本管理界面。customer\_id用于按客户汇总所有资料，app\_id用于区分属于某申请流程的材料（例如某申请特有的表格）。file\_type帮助筛选文件类别。对敏感材料可记录remarks说明比如“第2版修改了xx信息”。

### offers（Offer表）

| 字段名          | 类型                     | 描述                      |
| ------------ | ---------------------- | ----------------------- |
| offer\_id    | UUID (PK)              | Offer记录ID               |
| tenant\_id   | UUID (FK)              | 所属租户ID                  |
| app\_id      | UUID (FK→applications) | 关联申请ID                  |
| result       | VARCHAR(20)            | 结果类型（Offer/Reject等）     |
| detail       | TEXT                   | 详细说明（如offer条件、备注）       |
| received\_at | TIMESTAMP              | 接收时间（获得结果的日期）           |
| file\_id     | UUID (FK→materials) 可空 | Offer相关文件ID (如录取通知书PDF) |

\*\*说明：\*\*存储申请结果的信息。一般每个application对应一条offer记录（如果有结果的话）。result字段标明结果类型，detail记录附加信息。可选地关联材料表文件ID以保存Offer通知等附件。通过offers表可以方便查询统计各租户的成功率等指标。

以上表结构均采用InnoDB（PostgreSQL默认支持事务），外键约束确保引用完整性。所有时间戳字段建议记录为UTC时间，应用展示时再按所在时区转换。开发者在实际建表时应根据PostgreSQL语法调整（如UUID需要扩展模块支持或用UUID类型）。通过以上文档，开发人员可以清晰了解各表用途和字段含义，在编写代码时据此进行ORM模型定义和业务逻辑实现。

**参考文献：**

1. Bytebase团队. *我们使用 Postgres 构建多租户 SaaS 服务时踩的坑*. CSDN博客 (2024). 多租户schema隔离的利弊分析。
2. Logto技术博客. *使用 PostgreSQL 实现多租户：透过简单的实例学习*. (2024). 单数据库下通过tenant\_id和行级安全隔离租户数据。
3. FastAPI 权限控制实践. 博客园 (2023). 推荐使用RBAC模型进行角色权限管理，动态权限控制示例。
4. 阿里云OSS文档. *开启版本控制实现数据恢复和版本管理*. (2025). OSS存储版本管理机制说明。
5. 阿里云数据库RDS文档. *性能优化建议*. (2025). 建议减少空闲连接、使用连接池提升PostgreSQL性能。
