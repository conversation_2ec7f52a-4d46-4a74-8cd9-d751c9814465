{"cells": [{"cell_type": "code", "execution_count": 2, "id": "84c9c4a1", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd"]}, {"cell_type": "markdown", "id": "f0e745f4", "metadata": {}, "source": ["### 1 数据读取与基本检查"]}, {"cell_type": "markdown", "id": "0f5f5d03", "metadata": {}, "source": ["#### 1.1 数据读取"]}, {"cell_type": "code", "execution_count": 3, "id": "3ce7a718", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原数据总数：11012条\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校中文名</th>\n", "      <th>学校英文名</th>\n", "      <th>专业中文名</th>\n", "      <th>专业英文名</th>\n", "      <th>专业大类</th>\n", "      <th>专业方向</th>\n", "      <th>所在学院</th>\n", "      <th>入学时间</th>\n", "      <th>项目时长</th>\n", "      <th>项目官网</th>\n", "      <th>...</th>\n", "      <th>课程设置</th>\n", "      <th>专业代码</th>\n", "      <th>项目学费</th>\n", "      <th>学校英文名(QS26)</th>\n", "      <th>学校排名(QS26)</th>\n", "      <th>学校所在地区</th>\n", "      <th>申请学位类型</th>\n", "      <th>绩点要求</th>\n", "      <th>年开销预估值</th>\n", "      <th>留服认证</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4770</th>\n", "      <td>伦敦大学城市学院</td>\n", "      <td>City, University of London</td>\n", "      <td>新闻、媒体与全球化文学硕士（奥胡斯大学-伦敦大学城市学院联合开设）</td>\n", "      <td>Joint MA <PERSON> Masters: Journalism, M...</td>\n", "      <td>社科</td>\n", "      <td>新闻</td>\n", "      <td>艺术与社会科学学院</td>\n", "      <td>9月</td>\n", "      <td>2年</td>\n", "      <td>https://www.city.ac.uk/prospective-students/co...</td>\n", "      <td>...</td>\n", "      <td>全球化：报告全球变化Globalisation:Reportingglobalchange全...</td>\n", "      <td>64779</td>\n", "      <td>9797欧元/年</td>\n", "      <td>City St George’s, University of London</td>\n", "      <td>310</td>\n", "      <td>英国</td>\n", "      <td>硕士</td>\n", "      <td>60.0</td>\n", "      <td>20-30万人民币</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3450</th>\n", "      <td>布里斯托大学</td>\n", "      <td>University of Bristol</td>\n", "      <td>创新与创业理学硕士</td>\n", "      <td>MSc Innovation and Entrepreneurship</td>\n", "      <td>商科</td>\n", "      <td>创业与创新</td>\n", "      <td>创新与创业中心</td>\n", "      <td>9月</td>\n", "      <td>1年</td>\n", "      <td>https://www.bristol.ac.uk/study/postgraduate/t...</td>\n", "      <td>...</td>\n", "      <td>创新的工具和方法：设计和系统思考ToolsandMethodsforInnovation:D...</td>\n", "      <td>61627</td>\n", "      <td>35300英镑/年</td>\n", "      <td>University of Bristol</td>\n", "      <td>51</td>\n", "      <td>英国</td>\n", "      <td>硕士</td>\n", "      <td>70.0</td>\n", "      <td>20-30万人民币</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8926</th>\n", "      <td>加州大学洛杉矶分校</td>\n", "      <td>University of California, Los Angeles (UCLA)</td>\n", "      <td>环境工程理学硕士</td>\n", "      <td>MS in Environmental Engineering</td>\n", "      <td>工科</td>\n", "      <td>环境工程</td>\n", "      <td>NaN</td>\n", "      <td>秋季</td>\n", "      <td>4学期</td>\n", "      <td>http://www.cee.ucla.edu/</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>73633</td>\n", "      <td>11700美元/年</td>\n", "      <td>University of California, Los Angeles (UCLA)</td>\n", "      <td>46</td>\n", "      <td>美国</td>\n", "      <td>硕士</td>\n", "      <td>70.0</td>\n", "      <td>25-35万人民币</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4257</th>\n", "      <td>拉夫堡大学</td>\n", "      <td>Loughborough University</td>\n", "      <td>工商管理硕士</td>\n", "      <td>MBA</td>\n", "      <td>商科</td>\n", "      <td>工商管理</td>\n", "      <td>商业经济学院</td>\n", "      <td>10月</td>\n", "      <td>1年</td>\n", "      <td>https://www.lboro.ac.uk/study/postgraduate/mas...</td>\n", "      <td>...</td>\n", "      <td>战略营销StrategicMarketing会计和绩效管理AccountingandPerf...</td>\n", "      <td>62889</td>\n", "      <td>32000英镑/年</td>\n", "      <td>Loughborough University</td>\n", "      <td>225</td>\n", "      <td>英国</td>\n", "      <td>硕士</td>\n", "      <td>60.0</td>\n", "      <td>20-30万人民币</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9382</th>\n", "      <td>纽卡斯尔大学（澳大利亚）</td>\n", "      <td>The University of Newcastle, Australia (UON)</td>\n", "      <td>网络安全硕士</td>\n", "      <td>Master of Cyber Security</td>\n", "      <td>工科</td>\n", "      <td>计算机</td>\n", "      <td>科学暨信息技术学院</td>\n", "      <td>2/7月</td>\n", "      <td>2年</td>\n", "      <td>https://www.newcastle.edu.au/degrees/master-cy...</td>\n", "      <td>...</td>\n", "      <td>数据安全DataSecurity安全攻击：分析和缓解策略SecurityAttacks:An...</td>\n", "      <td>74117</td>\n", "      <td>45925澳元/年</td>\n", "      <td>The University of Newcastle, Australia (UON)</td>\n", "      <td>227</td>\n", "      <td>澳大利亚</td>\n", "      <td>硕士</td>\n", "      <td>70.0</td>\n", "      <td>15-25万人民币</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 24 columns</p>\n", "</div>"], "text/plain": ["             学校中文名                                         学校英文名  \\\n", "4770      伦敦大学城市学院                    City, University of London   \n", "3450        布里斯托大学                         University of Bristol   \n", "8926     加州大学洛杉矶分校  University of California, Los Angeles (UCLA)   \n", "4257         拉夫堡大学                       Loughborough University   \n", "9382  纽卡斯尔大学（澳大利亚）  The University of Newcastle, Australia (UON)   \n", "\n", "                                  专业中文名  \\\n", "4770  新闻、媒体与全球化文学硕士（奥胡斯大学-伦敦大学城市学院联合开设）   \n", "3450                          创新与创业理学硕士   \n", "8926                           环境工程理学硕士   \n", "4257                             工商管理硕士   \n", "9382                             网络安全硕士   \n", "\n", "                                                  专业英文名 专业大类   专业方向  \\\n", "4770  Joint MA Erasmus Mu<PERSON> Masters: Journalism, M...   社科     新闻   \n", "3450                MSc Innovation and Entrepreneurship   商科  创业与创新   \n", "8926                    MS in Environmental Engineering   工科   环境工程   \n", "4257                                                MBA   商科   工商管理   \n", "9382                           Master of Cyber Security   工科    计算机   \n", "\n", "           所在学院  入学时间 项目时长                                               项目官网  \\\n", "4770  艺术与社会科学学院    9月   2年  https://www.city.ac.uk/prospective-students/co...   \n", "3450    创新与创业中心    9月   1年  https://www.bristol.ac.uk/study/postgraduate/t...   \n", "8926        NaN    秋季  4学期                           http://www.cee.ucla.edu/   \n", "4257     商业经济学院   10月   1年  https://www.lboro.ac.uk/study/postgraduate/mas...   \n", "9382  科学暨信息技术学院  2/7月   2年  https://www.newcastle.edu.au/degrees/master-cy...   \n", "\n", "      ...                                               课程设置   专业代码  \\\n", "4770  ...  全球化：报告全球变化Globalisation:Reportingglobalchange全...  64779   \n", "3450  ...  创新的工具和方法：设计和系统思考ToolsandMethodsforInnovation:D...  61627   \n", "8926  ...                                                NaN  73633   \n", "4257  ...  战略营销StrategicMarketing会计和绩效管理AccountingandPerf...  62889   \n", "9382  ...  数据安全DataSecurity安全攻击：分析和缓解策略SecurityAttacks:An...  74117   \n", "\n", "           项目学费                                   学校英文名(QS26) 学校排名(QS26)  \\\n", "4770   9797欧元/年        City St George’s, University of London        310   \n", "3450  35300英镑/年                         University of Bristol         51   \n", "8926  11700美元/年  University of California, Los Angeles (UCLA)         46   \n", "4257  32000英镑/年                       Loughborough University        225   \n", "9382  45925澳元/年  The University of Newcastle, Australia (UON)        227   \n", "\n", "      学校所在地区 申请学位类型  绩点要求     年开销预估值 留服认证  \n", "4770      英国     硕士  60.0  20-30万人民币  NaN  \n", "3450      英国     硕士  70.0  20-30万人民币  NaN  \n", "8926      美国     硕士  70.0  25-35万人民币  NaN  \n", "4257      英国     硕士  60.0  20-30万人民币  NaN  \n", "9382    澳大利亚     硕士  70.0  15-25万人民币  NaN  \n", "\n", "[5 rows x 24 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["old_program_df = pd.read_csv('项目数据/老项目数据库（noembedding）.csv')\n", "print(f'原数据总数：{len(old_program_df)}条')\n", "old_program_df.sample(n=5)"]}, {"cell_type": "code", "execution_count": 4, "id": "b00cdc2c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["新数据总数：11447条\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>program_code</th>\n", "      <th>program_name_cn</th>\n", "      <th>program_name_en</th>\n", "      <th>program_category</th>\n", "      <th>program_direction</th>\n", "      <th>faculty</th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>school_labels</th>\n", "      <th>school_ranks</th>\n", "      <th>...</th>\n", "      <th>interview_type</th>\n", "      <th>program_website</th>\n", "      <th>program_objectives</th>\n", "      <th>application_requirements</th>\n", "      <th>gpa_requirements</th>\n", "      <th>language_requirements</th>\n", "      <th>application_time</th>\n", "      <th>consultant_analysis</th>\n", "      <th>courses</th>\n", "      <th>interview_experience</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>10692</th>\n", "      <td>65117</td>\n", "      <td>应用人类学与社区发展/社区艺术文学硕士</td>\n", "      <td>MA Applied Anthropology &amp; Community Developmen...</td>\n", "      <td>社科</td>\n", "      <td>社会学与社工</td>\n", "      <td>人类学系</td>\n", "      <td>伦敦大学金史密斯学院</td>\n", "      <td><PERSON><PERSON><PERSON>, University of London</td>\n", "      <td>伦敦大学联盟&lt;br&gt;</td>\n", "      <td>2025times第80名&lt;br/&gt;</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>https://www.gold.ac.uk/pg/ma-applied-anthropol...</td>\n", "      <td>伦敦大学金史密斯学院应用人类学与社区发展/社区艺术文学硕士项目针对那些可能不需要国家青年机构...</td>\n", "      <td>具有二等荣誉学位或同等学历，需要社会科学或其他合适的学科背景，并有一些社区发展或社区艺术方面...</td>\n", "      <td>该学校针对内地院校有专属的GPA分数要求，伦敦大学金史密斯学院不区分国内院校的背景，针对中国...</td>\n", "      <td>雅思 | 总分要求: 6.5 | 小分要求: 听力: 6; 阅读: 6; 写作: 6.5; ...</td>\n", "      <td>25年秋季入学: 开放申请(2024-07-29) | 截止申请(-)&lt;br/&gt;</td>\n", "      <td>NaN</td>\n", "      <td>课程描述：&lt;br/&gt;\\n&lt;br/&gt;\\n课程列表：&lt;br/&gt;\\n当代社会问题 | Contem...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10714</th>\n", "      <td>67530</td>\n", "      <td>日本研究文学硕士</td>\n", "      <td>MA Japanese Studies</td>\n", "      <td>社科</td>\n", "      <td>国际关系</td>\n", "      <td>东亚语言与文化学院</td>\n", "      <td>伦敦大学亚非学院</td>\n", "      <td>SOA<PERSON>, University of London</td>\n", "      <td>伦敦大学联盟&lt;br&gt;</td>\n", "      <td>2025times第65名&lt;br/&gt;</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>https://www.soas.ac.uk/study/find-course/ma-ja...</td>\n", "      <td>伦敦大学亚非学院日本研究文学硕士课程是欧洲最全面的课程。学生可以选择涵盖日本所有历史时期的模...</td>\n", "      <td>不限专业</td>\n", "      <td>NaN</td>\n", "      <td>雅思 | 总分要求: 6.5 | 小分要求: 听力: 6; 阅读: 6; 写作: 6; 口语...</td>\n", "      <td>25年秋季入学: 开放申请(2024-11-01) | 截止申请(2025-07-31)&lt;br/&gt;</td>\n", "      <td>NaN</td>\n", "      <td>课程描述：&lt;br/&gt;\\n&lt;br/&gt;\\n课程列表：&lt;br/&gt;\\n日本研究论文 | Disser...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2443</th>\n", "      <td>62614</td>\n", "      <td>地中海考古学理学硕士</td>\n", "      <td>MSc Mediterranean Archaeology</td>\n", "      <td>社科</td>\n", "      <td>历史</td>\n", "      <td>历史、古典与考古学院</td>\n", "      <td>爱丁堡大学</td>\n", "      <td>The University of Edinburgh</td>\n", "      <td>罗素大学集团&lt;br&gt;科英布拉集团&lt;br&gt;欧洲研究型大学联盟&lt;br&gt;</td>\n", "      <td>2026qs第34名&lt;br/&gt;2025times第17名&lt;br/&gt;</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>https://www.ed.ac.uk/studying/postgraduate/deg...</td>\n", "      <td>地中海周边地区为考古研究提供了许多机会。地中海考古学理学硕士允许学生通过对时期、地理区域和主...</td>\n", "      <td>具有英国2:1荣誉学位，需要与申请专业相关的学科背景，偏好考古学或古典考古学，或考古学占主导...</td>\n", "      <td>该学校针对内地院校有专属的GPA分数要求，爱丁堡大学会单独设置院校名单，对内地院校划分等级，...</td>\n", "      <td>雅思 | 总分要求: 7 | 小分要求: 听力: 6; 阅读: 6; 写作: 6; 口语: ...</td>\n", "      <td>25年秋季入学: 开放申请(2024-10-01) | 截止申请(2025-07-31)&lt;br/&gt;</td>\n", "      <td>NaN</td>\n", "      <td>课程描述：&lt;br/&gt;\\n&lt;br/&gt;\\n课程列表：&lt;br/&gt;\\n考古学的研究来源和策略 | R...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2424</th>\n", "      <td>67624</td>\n", "      <td>全球战略与可持续发展理学硕士</td>\n", "      <td>MSc Global Strategy &amp; Sustainability</td>\n", "      <td>商科</td>\n", "      <td>管理</td>\n", "      <td>商学院</td>\n", "      <td>爱丁堡大学</td>\n", "      <td>The University of Edinburgh</td>\n", "      <td>罗素大学集团&lt;br&gt;科英布拉集团&lt;br&gt;欧洲研究型大学联盟&lt;br&gt;</td>\n", "      <td>2026qs第34名&lt;br/&gt;2025times第17名&lt;br/&gt;</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>https://www.ed.ac.uk/studying/postgraduate/deg...</td>\n", "      <td>爱丁堡大学全球战略与可持续发展理学硕士课程旨在：\\n为学生提供对影响私营、公共和第三部门组织...</td>\n", "      <td>具有英国一等或2:1荣誉学位或同等学历，需要商业和商务、管理、金融、会计、经济、历史、国际政...</td>\n", "      <td>该学校针对内地院校有专属的GPA分数要求，爱丁堡大学会单独设置院校名单，对内地院校划分等级，...</td>\n", "      <td>雅思 | 总分要求: 7 | 小分要求: 听力: 6; 阅读: 6; 写作: 6; 口语: ...</td>\n", "      <td>25年秋季入学: 开放申请(2024-09-23) | Round 1 截止(2024-10...</td>\n", "      <td>爱丁堡大学全球战略与可持续性专业主要围绕环境和社会可持续性，探讨组织如何面对来自全球范围的气...</td>\n", "      <td>课程描述：&lt;br/&gt;\\n一年制项目，有两个学期以及夏季学期，课程包括6门必修课，4门选修课，...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1279</th>\n", "      <td>76353</td>\n", "      <td>生物商业与生物创业理学硕士</td>\n", "      <td>MSc BioBusiness and BioEntrepreneurship</td>\n", "      <td>理科</td>\n", "      <td>生物</td>\n", "      <td>生物科学学院</td>\n", "      <td>南洋理工大学</td>\n", "      <td>Nanyang Technological University</td>\n", "      <td>环太平洋大学联盟&lt;br&gt;全球大学校长论坛&lt;br&gt;新工科教育国际联盟&lt;br&gt;</td>\n", "      <td>2026qs第12名&lt;br/&gt;</td>\n", "      <td>...</td>\n", "      <td>真人单面</td>\n", "      <td>https://www.ntu.edu.sg/education/graduate-prog...</td>\n", "      <td>在尖端技术的推动下，生命科学和医疗保健行业正处于变革性革命的边缘，人工智能、基因组学、基因编...</td>\n", "      <td>具有生命科学/生物/生物医学科学/生物工程和相关领域的理学学士（荣誉）、理学硕士或博士学位，...</td>\n", "      <td>NaN</td>\n", "      <td>雅思 | 总分要求: 7 | 小分要求: 听力: /; 阅读: /; 写作: /; 口语: ...</td>\n", "      <td>25年秋季入学: 开放申请(2025-03-15) | 截止申请(2025-05-31)&lt;br/&gt;</td>\n", "      <td>NaN</td>\n", "      <td>课程描述：&lt;br/&gt;\\n&lt;br/&gt;\\n课程列表：&lt;br/&gt;\\n生物商业与生物创业的基本要素 ...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 23 columns</p>\n", "</div>"], "text/plain": ["       program_code      program_name_cn  \\\n", "10692         65117  应用人类学与社区发展/社区艺术文学硕士   \n", "10714         67530             日本研究文学硕士   \n", "2443          62614           地中海考古学理学硕士   \n", "2424          67624       全球战略与可持续发展理学硕士   \n", "1279          76353        生物商业与生物创业理学硕士   \n", "\n", "                                         program_name_en program_category  \\\n", "10692  MA Applied Anthropology & Community Developmen...               社科   \n", "10714                                MA Japanese Studies               社科   \n", "2443                       MSc Mediterranean Archaeology               社科   \n", "2424                MSc Global Strategy & Sustainability               商科   \n", "1279             MSc BioBusiness and BioEntrepreneurship               理科   \n", "\n", "      program_direction     faculty school_name_cn  \\\n", "10692            社会学与社工        人类学系     伦敦大学金史密斯学院   \n", "10714              国际关系   东亚语言与文化学院       伦敦大学亚非学院   \n", "2443                 历史  历史、古典与考古学院          爱丁堡大学   \n", "2424                 管理         商学院          爱丁堡大学   \n", "1279                 生物      生物科学学院         南洋理工大学   \n", "\n", "                         school_name_en  \\\n", "10692  Goldsmith<PERSON>, University of London   \n", "10714        SOA<PERSON>, University of London   \n", "2443        The University of Edinburgh   \n", "2424        The University of Edinburgh   \n", "1279   Nanyang Technological University   \n", "\n", "                               school_labels  \\\n", "10692                             伦敦大学联盟<br>   \n", "10714                             伦敦大学联盟<br>   \n", "2443       罗素大学集团<br>科英布拉集团<br>欧洲研究型大学联盟<br>   \n", "2424       罗素大学集团<br>科英布拉集团<br>欧洲研究型大学联盟<br>   \n", "1279   环太平洋大学联盟<br>全球大学校长论坛<br>新工科教育国际联盟<br>   \n", "\n", "                            school_ranks  ... interview_type  \\\n", "10692                 2025times第80名<br/>  ...            NaN   \n", "10714                 2025times第65名<br/>  ...            NaN   \n", "2443   2026qs第34名<br/>2025times第17名<br/>  ...            NaN   \n", "2424   2026qs第34名<br/>2025times第17名<br/>  ...            NaN   \n", "1279                     2026qs第12名<br/>  ...           真人单面   \n", "\n", "                                         program_website  \\\n", "10692  https://www.gold.ac.uk/pg/ma-applied-anthropol...   \n", "10714  https://www.soas.ac.uk/study/find-course/ma-ja...   \n", "2443   https://www.ed.ac.uk/studying/postgraduate/deg...   \n", "2424   https://www.ed.ac.uk/studying/postgraduate/deg...   \n", "1279   https://www.ntu.edu.sg/education/graduate-prog...   \n", "\n", "                                      program_objectives  \\\n", "10692  伦敦大学金史密斯学院应用人类学与社区发展/社区艺术文学硕士项目针对那些可能不需要国家青年机构...   \n", "10714  伦敦大学亚非学院日本研究文学硕士课程是欧洲最全面的课程。学生可以选择涵盖日本所有历史时期的模...   \n", "2443   地中海周边地区为考古研究提供了许多机会。地中海考古学理学硕士允许学生通过对时期、地理区域和主...   \n", "2424   爱丁堡大学全球战略与可持续发展理学硕士课程旨在：\\n为学生提供对影响私营、公共和第三部门组织...   \n", "1279   在尖端技术的推动下，生命科学和医疗保健行业正处于变革性革命的边缘，人工智能、基因组学、基因编...   \n", "\n", "                                application_requirements  \\\n", "10692  具有二等荣誉学位或同等学历，需要社会科学或其他合适的学科背景，并有一些社区发展或社区艺术方面...   \n", "10714                                               不限专业   \n", "2443   具有英国2:1荣誉学位，需要与申请专业相关的学科背景，偏好考古学或古典考古学，或考古学占主导...   \n", "2424   具有英国一等或2:1荣誉学位或同等学历，需要商业和商务、管理、金融、会计、经济、历史、国际政...   \n", "1279   具有生命科学/生物/生物医学科学/生物工程和相关领域的理学学士（荣誉）、理学硕士或博士学位，...   \n", "\n", "                                        gpa_requirements  \\\n", "10692  该学校针对内地院校有专属的GPA分数要求，伦敦大学金史密斯学院不区分国内院校的背景，针对中国...   \n", "10714                                                NaN   \n", "2443   该学校针对内地院校有专属的GPA分数要求，爱丁堡大学会单独设置院校名单，对内地院校划分等级，...   \n", "2424   该学校针对内地院校有专属的GPA分数要求，爱丁堡大学会单独设置院校名单，对内地院校划分等级，...   \n", "1279                                                 NaN   \n", "\n", "                                   language_requirements  \\\n", "10692  雅思 | 总分要求: 6.5 | 小分要求: 听力: 6; 阅读: 6; 写作: 6.5; ...   \n", "10714  雅思 | 总分要求: 6.5 | 小分要求: 听力: 6; 阅读: 6; 写作: 6; 口语...   \n", "2443   雅思 | 总分要求: 7 | 小分要求: 听力: 6; 阅读: 6; 写作: 6; 口语: ...   \n", "2424   雅思 | 总分要求: 7 | 小分要求: 听力: 6; 阅读: 6; 写作: 6; 口语: ...   \n", "1279   雅思 | 总分要求: 7 | 小分要求: 听力: /; 阅读: /; 写作: /; 口语: ...   \n", "\n", "                                        application_time  \\\n", "10692           25年秋季入学: 开放申请(2024-07-29) | 截止申请(-)<br/>   \n", "10714  25年秋季入学: 开放申请(2024-11-01) | 截止申请(2025-07-31)<br/>   \n", "2443   25年秋季入学: 开放申请(2024-10-01) | 截止申请(2025-07-31)<br/>   \n", "2424   25年秋季入学: 开放申请(2024-09-23) | Round 1 截止(2024-10...   \n", "1279   25年秋季入学: 开放申请(2025-03-15) | 截止申请(2025-05-31)<br/>   \n", "\n", "                                     consultant_analysis  \\\n", "10692                                                NaN   \n", "10714                                                NaN   \n", "2443                                                 NaN   \n", "2424   爱丁堡大学全球战略与可持续性专业主要围绕环境和社会可持续性，探讨组织如何面对来自全球范围的气...   \n", "1279                                                 NaN   \n", "\n", "                                                 courses interview_experience  \n", "10692  课程描述：<br/>\\n<br/>\\n课程列表：<br/>\\n当代社会问题 | Contem...                  NaN  \n", "10714  课程描述：<br/>\\n<br/>\\n课程列表：<br/>\\n日本研究论文 | Disser...                  NaN  \n", "2443   课程描述：<br/>\\n<br/>\\n课程列表：<br/>\\n考古学的研究来源和策略 | R...                  NaN  \n", "2424   课程描述：<br/>\\n一年制项目，有两个学期以及夏季学期，课程包括6门必修课，4门选修课，...                  NaN  \n", "1279   课程描述：<br/>\\n<br/>\\n课程列表：<br/>\\n生物商业与生物创业的基本要素 ...                  NaN  \n", "\n", "[5 rows x 23 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["program_df = pd.read_csv('项目数据/项目数据库（原始爬取版）.csv')\n", "print(f'新数据总数：{len(program_df)}条')\n", "program_df.sample(n=5)"]}, {"cell_type": "markdown", "id": "27f6ce6f", "metadata": {}, "source": ["#### 1.2 空值检查"]}, {"cell_type": "code", "execution_count": 5, "id": "661d953b", "metadata": {}, "outputs": [{"data": {"text/plain": ["program_code                    0\n", "program_name_cn                 0\n", "program_name_en                 1\n", "program_category                0\n", "program_direction               0\n", "faculty                      2016\n", "school_name_cn                  0\n", "school_name_en                  0\n", "school_labels                  98\n", "school_ranks                  340\n", "enrollment_time                51\n", "program_duration               54\n", "program_tuition                71\n", "interview_type              10719\n", "program_website                12\n", "program_objectives           2141\n", "application_requirements      475\n", "gpa_requirements             5934\n", "language_requirements         149\n", "application_time              688\n", "consultant_analysis          8805\n", "courses                      2380\n", "interview_experience        11151\n", "dtype: int64"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 空值检查\n", "program_df.isnull().sum()"]}, {"cell_type": "code", "execution_count": 6, "id": "a6039efa", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>program_code</th>\n", "      <th>program_name_cn</th>\n", "      <th>program_name_en</th>\n", "      <th>program_category</th>\n", "      <th>program_direction</th>\n", "      <th>faculty</th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>school_labels</th>\n", "      <th>school_ranks</th>\n", "      <th>...</th>\n", "      <th>interview_type</th>\n", "      <th>program_website</th>\n", "      <th>program_objectives</th>\n", "      <th>application_requirements</th>\n", "      <th>gpa_requirements</th>\n", "      <th>language_requirements</th>\n", "      <th>application_time</th>\n", "      <th>consultant_analysis</th>\n", "      <th>courses</th>\n", "      <th>interview_experience</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1385</th>\n", "      <td>73599</td>\n", "      <td>应用物理理学硕士</td>\n", "      <td>NaN</td>\n", "      <td>理科</td>\n", "      <td>物理</td>\n", "      <td>NaN</td>\n", "      <td>康奈尔大学</td>\n", "      <td>Cornell University</td>\n", "      <td>常春藤联盟&lt;br&gt;美国大学协会&lt;br&gt;国际大学气候联盟&lt;br&gt;</td>\n", "      <td>2026qs第16名&lt;br/&gt;2025usnews第11名&lt;br/&gt;</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>https://gradschool.cornell.edu/academics/field...</td>\n", "      <td>NaN</td>\n", "      <td>申请人应具有物理学或其他物理科学或工程领域的本科准备，focus是数学和现代物理学。</td>\n", "      <td>NaN</td>\n", "      <td>托福 | 总分要求: 无要求 | 小分要求: 小分要求: L15,W20,R20,S22; ...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 23 columns</p>\n", "</div>"], "text/plain": ["      program_code program_name_cn program_name_en program_category  \\\n", "1385         73599        应用物理理学硕士             NaN               理科   \n", "\n", "     program_direction faculty school_name_cn      school_name_en  \\\n", "1385                物理     NaN          康奈尔大学  Cornell University   \n", "\n", "                        school_labels                        school_ranks  \\\n", "1385  常春藤联盟<br>美国大学协会<br>国际大学气候联盟<br>  2026qs第16名<br/>2025usnews第11名<br/>   \n", "\n", "      ... interview_type                                    program_website  \\\n", "1385  ...            NaN  https://gradschool.cornell.edu/academics/field...   \n", "\n", "     program_objectives                    application_requirements  \\\n", "1385                NaN  申请人应具有物理学或其他物理科学或工程领域的本科准备，focus是数学和现代物理学。   \n", "\n", "     gpa_requirements                              language_requirements  \\\n", "1385              NaN  托福 | 总分要求: 无要求 | 小分要求: 小分要求: L15,W20,R20,S22; ...   \n", "\n", "     application_time consultant_analysis courses interview_experience  \n", "1385              NaN                 NaN     NaN                  NaN  \n", "\n", "[1 rows x 23 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# 对于某列只有极少数空值的情况进行检查和人工补充\n", "program_df[program_df['program_name_en'].isna()]"]}, {"cell_type": "code", "execution_count": 7, "id": "1ce1b252", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>program_code</th>\n", "      <th>program_name_cn</th>\n", "      <th>program_name_en</th>\n", "      <th>program_category</th>\n", "      <th>program_direction</th>\n", "      <th>faculty</th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>school_labels</th>\n", "      <th>school_ranks</th>\n", "      <th>...</th>\n", "      <th>interview_type</th>\n", "      <th>program_website</th>\n", "      <th>program_objectives</th>\n", "      <th>application_requirements</th>\n", "      <th>gpa_requirements</th>\n", "      <th>language_requirements</th>\n", "      <th>application_time</th>\n", "      <th>consultant_analysis</th>\n", "      <th>courses</th>\n", "      <th>interview_experience</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "<p>0 rows × 23 columns</p>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [program_code, program_name_cn, program_name_en, program_category, program_direction, faculty, school_name_cn, school_name_en, school_labels, school_ranks, enrollment_time, program_duration, program_tuition, interview_type, program_website, program_objectives, application_requirements, gpa_requirements, language_requirements, application_time, consultant_analysis, courses, interview_experience]\n", "Index: []\n", "\n", "[0 rows x 23 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["program_df.loc[1385, 'program_name_en'] = 'MS in Applied Physics'\n", "program_df[program_df['program_name_en'].isna()]"]}, {"cell_type": "markdown", "id": "98411743", "metadata": {}, "source": ["#### 1.3 重复值检查"]}, {"cell_type": "code", "execution_count": 8, "id": "e30355cd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n"]}], "source": ["# 重复值检查\n", "print(program_df.duplicated().sum())\n"]}, {"cell_type": "code", "execution_count": 9, "id": "b867c7b5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["共有44组学校中文名和项目中文名都相同的项目，注意用项目英文名区分\n"]}], "source": ["# 学校中文名和项目中文名重复的项..\n", "\n", "duplicated_rows = len(program_df[program_df.duplicated(subset=['school_name_cn', 'program_name_cn'], keep=False)]) - len(program_df[program_df.duplicated(subset=['school_name_cn', 'program_name_cn'])])\n", "print(f'共有{duplicated_rows}组学校中文名和项目中文名都相同的项目，注意用项目英文名区分') \n", "# program_df[program_df.duplicated(subset=['school_name_cn', 'program_name_cn'], keep=False)]  # 共111条"]}, {"cell_type": "markdown", "id": "ab7688a9", "metadata": {}, "source": ["> 检查后发现部分是因为爬取时中文名称没爬全，比如nus “计算机工程理学硕士 - 数字化与通信技术方向” 只爬取了 “计算机工程理学硕士”...; 明尼苏达大学 “数据科学理学硕士 - 化学工程与材料科学” 只爬取了 “数据科学理学硕士”等等。后续在数据库中更改吧，通过写接口直接允许管理员从系统界面修改。"]}, {"cell_type": "code", "execution_count": 10, "id": "27d8ca32", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>program_code</th>\n", "      <th>program_name_cn</th>\n", "      <th>program_name_en</th>\n", "      <th>program_category</th>\n", "      <th>program_direction</th>\n", "      <th>faculty</th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>school_labels</th>\n", "      <th>school_ranks</th>\n", "      <th>...</th>\n", "      <th>interview_type</th>\n", "      <th>program_website</th>\n", "      <th>program_objectives</th>\n", "      <th>application_requirements</th>\n", "      <th>gpa_requirements</th>\n", "      <th>language_requirements</th>\n", "      <th>application_time</th>\n", "      <th>consultant_analysis</th>\n", "      <th>courses</th>\n", "      <th>interview_experience</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4774</th>\n", "      <td>71764</td>\n", "      <td>生物工程理学硕士</td>\n", "      <td>MS in Bioengineering</td>\n", "      <td>工科</td>\n", "      <td>生物工程</td>\n", "      <td>NaN</td>\n", "      <td>华盛顿大学</td>\n", "      <td>University of Washington</td>\n", "      <td>公立常春藤&lt;br&gt;帕克十二联盟&lt;br&gt;环太平洋大学联盟&lt;br&gt;国际大学气候联盟&lt;br&gt;</td>\n", "      <td>2026qs第81名&lt;br/&gt;2025usnews第46名&lt;br/&gt;</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>https://bioe.uw.edu/academic-programs/masters/ms/</td>\n", "      <td>NaN</td>\n", "      <td>建议有以下先修课：代数、线性代数、三角学、常微分方程、信号分析、概率论与统计学、编程、电气工...</td>\n", "      <td>NaN</td>\n", "      <td>托福 | 总分要求: 92; 小分要求: 无要求; &lt;br/&gt;雅思 | 总分要求: 7; 小...</td>\n", "      <td>23年秋季入学: 开放申请(/) | 截止申请(2023-12-01)&lt;br/&gt;</td>\n", "      <td>华盛顿大学生物工程系研究生阶段设有以下学位项目，分别是：生物工程理学硕士Master of ...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4775</th>\n", "      <td>73895</td>\n", "      <td>生物工程理学硕士</td>\n", "      <td>MS in Bioengineering</td>\n", "      <td>工科</td>\n", "      <td>生物工程</td>\n", "      <td>NaN</td>\n", "      <td>华盛顿大学</td>\n", "      <td>University of Washington</td>\n", "      <td>公立常春藤&lt;br&gt;帕克十二联盟&lt;br&gt;环太平洋大学联盟&lt;br&gt;国际大学气候联盟&lt;br&gt;</td>\n", "      <td>2026qs第81名&lt;br/&gt;2025usnews第46名&lt;br/&gt;</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>https://bioe.uw.edu/academic-programs/masters/ms/</td>\n", "      <td>NaN</td>\n", "      <td>GPA3.0；具有强大的研究技能（有至少一年干湿实验室经验者优先）；生物工程、化学工程、化学...</td>\n", "      <td>NaN</td>\n", "      <td>托福 | 总分要求: 80; 小分要求: 无要求; &lt;br/&gt;雅思 | 总分要求: 6.5;...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11341</th>\n", "      <td>72069</td>\n", "      <td>统计学理学硕士</td>\n", "      <td>MS in Statistics</td>\n", "      <td>理科</td>\n", "      <td>数学</td>\n", "      <td>NaN</td>\n", "      <td>康涅狄格大学</td>\n", "      <td>University of Connecticut</td>\n", "      <td>公立常春藤&lt;br&gt;美国竞技联盟&lt;br&gt;Universitas 21&lt;br&gt;</td>\n", "      <td>2025usnews第70名&lt;br/&gt;</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>https://statistics.uconn.edu/graduate/masters-...</td>\n", "      <td>NaN</td>\n", "      <td>1.先修课：微积分（3学期,包括1学期多元微积分）,线性代数（1学期）,统计（2学期）\\n2...</td>\n", "      <td>NaN</td>\n", "      <td>托福 | 总分要求: 79; 小分要求: 无要求; &lt;br/&gt;雅思 | 总分要求: 6.5;...</td>\n", "      <td>25年秋季入学: 开放申请(/) | 截止申请(2025-04-01)&lt;br/&gt;</td>\n", "      <td>康涅狄格大学统计学系开设了统计学MS项目，硕士课程强调应用统计学，要求学生在具体的应用领域至...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11342</th>\n", "      <td>73025</td>\n", "      <td>统计学理学硕士</td>\n", "      <td>MS in Statistics</td>\n", "      <td>理科</td>\n", "      <td>数学</td>\n", "      <td>NaN</td>\n", "      <td>康涅狄格大学</td>\n", "      <td>University of Connecticut</td>\n", "      <td>公立常春藤&lt;br&gt;美国竞技联盟&lt;br&gt;Universitas 21&lt;br&gt;</td>\n", "      <td>2025usnews第70名&lt;br/&gt;</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>https://statistics.uconn.edu/graduate/masters-...</td>\n", "      <td>NaN</td>\n", "      <td>整个本科累计平均绩点为3.0;本科最后两年的平均绩点至少为3.0;或在整个本科最后一年表现优...</td>\n", "      <td>NaN</td>\n", "      <td>托福 | 总分要求: 79 | 小分要求: 小分要求: 22; &lt;br/&gt;雅思 | 总分要求...</td>\n", "      <td>25年秋季入学: 开放申请(/) | 截止申请(2025-04-01)&lt;br/&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4 rows × 23 columns</p>\n", "</div>"], "text/plain": ["       program_code program_name_cn       program_name_en program_category  \\\n", "4774          71764        生物工程理学硕士  MS in Bioengineering               工科   \n", "4775          73895        生物工程理学硕士  MS in Bioengineering               工科   \n", "11341         72069         统计学理学硕士      MS in Statistics               理科   \n", "11342         73025         统计学理学硕士      MS in Statistics               理科   \n", "\n", "      program_direction faculty school_name_cn             school_name_en  \\\n", "4774               生物工程     NaN          华盛顿大学   University of Washington   \n", "4775               生物工程     NaN          华盛顿大学   University of Washington   \n", "11341                数学     NaN         康涅狄格大学  University of Connecticut   \n", "11342                数学     NaN         康涅狄格大学  University of Connecticut   \n", "\n", "                                     school_labels  \\\n", "4774   公立常春藤<br>帕克十二联盟<br>环太平洋大学联盟<br>国际大学气候联盟<br>   \n", "4775   公立常春藤<br>帕克十二联盟<br>环太平洋大学联盟<br>国际大学气候联盟<br>   \n", "11341        公立常春藤<br>美国竞技联盟<br>Universitas 21<br>   \n", "11342        公立常春藤<br>美国竞技联盟<br>Universitas 21<br>   \n", "\n", "                             school_ranks  ... interview_type  \\\n", "4774   2026qs第81名<br/>2025usnews第46名<br/>  ...            NaN   \n", "4775   2026qs第81名<br/>2025usnews第46名<br/>  ...            NaN   \n", "11341                 2025usnews第70名<br/>  ...            NaN   \n", "11342                 2025usnews第70名<br/>  ...            NaN   \n", "\n", "                                         program_website program_objectives  \\\n", "4774   https://bioe.uw.edu/academic-programs/masters/ms/                NaN   \n", "4775   https://bioe.uw.edu/academic-programs/masters/ms/                NaN   \n", "11341  https://statistics.uconn.edu/graduate/masters-...                NaN   \n", "11342  https://statistics.uconn.edu/graduate/masters-...                NaN   \n", "\n", "                                application_requirements gpa_requirements  \\\n", "4774   建议有以下先修课：代数、线性代数、三角学、常微分方程、信号分析、概率论与统计学、编程、电气工...              NaN   \n", "4775   GPA3.0；具有强大的研究技能（有至少一年干湿实验室经验者优先）；生物工程、化学工程、化学...              NaN   \n", "11341  1.先修课：微积分（3学期,包括1学期多元微积分）,线性代数（1学期）,统计（2学期）\\n2...              NaN   \n", "11342  整个本科累计平均绩点为3.0;本科最后两年的平均绩点至少为3.0;或在整个本科最后一年表现优...              NaN   \n", "\n", "                                   language_requirements  \\\n", "4774   托福 | 总分要求: 92; 小分要求: 无要求; <br/>雅思 | 总分要求: 7; 小...   \n", "4775   托福 | 总分要求: 80; 小分要求: 无要求; <br/>雅思 | 总分要求: 6.5;...   \n", "11341  托福 | 总分要求: 79; 小分要求: 无要求; <br/>雅思 | 总分要求: 6.5;...   \n", "11342  托福 | 总分要求: 79 | 小分要求: 小分要求: 22; <br/>雅思 | 总分要求...   \n", "\n", "                               application_time  \\\n", "4774   23年秋季入学: 开放申请(/) | 截止申请(2023-12-01)<br/>   \n", "4775                                        NaN   \n", "11341  25年秋季入学: 开放申请(/) | 截止申请(2025-04-01)<br/>   \n", "11342  25年秋季入学: 开放申请(/) | 截止申请(2025-04-01)<br/>   \n", "\n", "                                     consultant_analysis courses  \\\n", "4774   华盛顿大学生物工程系研究生阶段设有以下学位项目，分别是：生物工程理学硕士Master of ...     NaN   \n", "4775                                                 NaN     NaN   \n", "11341  康涅狄格大学统计学系开设了统计学MS项目，硕士课程强调应用统计学，要求学生在具体的应用领域至...     NaN   \n", "11342                                                NaN     NaN   \n", "\n", "      interview_experience  \n", "4774                   NaN  \n", "4775                   NaN  \n", "11341                  NaN  \n", "11342                  NaN  \n", "\n", "[4 rows x 23 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# 学校中文名，项目中文名和项目英文名都重复的项...\n", "# 下面4个check后发现应该就是单纯的录入重复，但很多字段值不同\n", "program_df[program_df.duplicated(subset=['school_name_cn', 'program_name_cn', 'program_name_en'], keep=False)]"]}, {"cell_type": "markdown", "id": "050aa7d1", "metadata": {}, "source": ["#### 1.4 中英文名一致性检查"]}, {"cell_type": "code", "execution_count": 11, "id": "9d9bd359", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["180\n", "180\n"]}], "source": ["print(len(program_df['school_name_cn'].unique()))\n", "print(len(program_df['school_name_en'].unique()))"]}, {"cell_type": "code", "execution_count": 12, "id": "a580b76e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["中文名对应多个英文名的情况:\n", "\n", "英文名对应多个中文名的情况:\n"]}], "source": ["# 检查中文名对应多个英文名的情况\n", "one_to_many_zh = program_df.groupby('school_name_cn')['school_name_en'].nunique()\n", "many_zh_names = one_to_many_zh[one_to_many_zh > 1].index.tolist()\n", "\n", "# 检查英文名对应多个中文名的情况\n", "one_to_many_en = program_df.groupby('school_name_en')['school_name_cn'].nunique()\n", "many_en_names = one_to_many_en[one_to_many_en > 1].index.tolist()\n", "\n", "print(\"中文名对应多个英文名的情况:\")\n", "for zh_name in many_zh_names:\n", "    print(f\"{zh_name}: {', '.join(program_df[program_df['school_name_cn'] == zh_name]['school_name_en'].unique())}\")\n", "\n", "print(\"\\n英文名对应多个中文名的情况:\")\n", "for en_name in many_en_names:\n", "    print(f\"{en_name}: {', '.join(program_df[program_df['school_name_en'] == en_name]['school_name_cn'].unique())}\")"]}, {"cell_type": "markdown", "id": "eab99775", "metadata": {}, "source": ["### 2 数据补充"]}, {"cell_type": "markdown", "id": "1d8c0124", "metadata": {}, "source": ["#### 2.1 QS排名数据补充"]}, {"cell_type": "markdown", "id": "3b10abd9", "metadata": {}, "source": ["> **院校名称对齐**：项目数据中的院校英文名（school_name_en）和原始qs排名中院校英文名不完全相同，直接join项目表和原始排名表会导致很多学校查找不到排名。可通过字符串模糊匹配等方法对齐英文名，得到program_schools_qs2026.csv，该csv的中/英文名能够与项目数据program_df中的中/英文完全匹配，再join两表。"]}, {"cell_type": "code", "execution_count": 13, "id": "9ed5fdf9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>qs2026</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>帝国理工学院</td>\n", "      <td>Imperial College London</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>斯坦福大学</td>\n", "      <td>Stanford University</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>牛津大学</td>\n", "      <td>University of Oxford</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>哈佛大学</td>\n", "      <td>Harvard University</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>175</th>\n", "      <td>香港树仁大学</td>\n", "      <td>Hong Kong Shue Yan University</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>176</th>\n", "      <td>马来西亚莫纳什大学</td>\n", "      <td>Monash University Malaysia</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>177</th>\n", "      <td>本特利大学</td>\n", "      <td>Bentley University</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>178</th>\n", "      <td>慕尼黑工业大学亚洲分校</td>\n", "      <td>Technical University of Munich Asia</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>179</th>\n", "      <td>香港演艺学院</td>\n", "      <td>The Hong Kong Academy for Performing Arts</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>180 rows × 3 columns</p>\n", "</div>"], "text/plain": ["    school_name_cn                             school_name_en qs2026\n", "0           麻省理工学院      Massachusetts Institute of Technology      1\n", "1           帝国理工学院                    Imperial College London      2\n", "2            斯坦福大学                        Stanford University      3\n", "3             牛津大学                       University of Oxford      4\n", "4             哈佛大学                         Harvard University      5\n", "..             ...                                        ...    ...\n", "175         香港树仁大学              Hong Kong Shue Yan University    NaN\n", "176      马来西亚莫纳什大学                 Monash University Malaysia    NaN\n", "177          本特利大学                         Bentley University    NaN\n", "178    慕尼黑工业大学亚洲分校        Technical University of Munich Asia    NaN\n", "179         香港演艺学院  The Hong Kong Academy for Performing Arts    NaN\n", "\n", "[180 rows x 3 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["program_schools_qs2026_df = pd.read_csv('院校数据/program_schools_qs2026.csv')\n", "program_schools_qs2026_df"]}, {"cell_type": "code", "execution_count": 14, "id": "160326eb", "metadata": {}, "outputs": [{"data": {"text/plain": ["set()"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# 确认项目数据中涉及的院校名都能在qs排名数据中找到\n", "set(program_df['school_name_cn'].unique()) - set(program_schools_qs2026_df['school_name_cn'].unique())"]}, {"cell_type": "code", "execution_count": 15, "id": "0af50840", "metadata": {}, "outputs": [], "source": ["# 使用merge函数进行左连接，并指定on参数为'school_name_cn'\n", "program_df = pd.merge(program_df, program_schools_qs2026_df[['school_name_cn', 'qs2026']], \n", "                     on='school_name_cn', how='left')\n", "\n", "# 将'qs2026'列重命名为'school_qs_rank'\n", "program_df.rename(columns={'qs2026': 'school_qs_rank'}, inplace=True)"]}, {"cell_type": "markdown", "id": "148dc253", "metadata": {}, "source": ["#### 2.2 学校地区数据补充"]}, {"cell_type": "code", "execution_count": 16, "id": "6784f36f", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'南安普顿大学马来西亚分校',\n", " '圣何塞州立大学',\n", " '慕尼黑工业大学亚洲分校',\n", " '香港演艺学院',\n", " '马来亚大学',\n", " '马来西亚博特拉大学',\n", " '马来西亚国立大学',\n", " '马来西亚理工大学',\n", " '马来西亚理科大学',\n", " '马来西亚莫纳什大学'}"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# 新项目数据中相比原项目数据中新增的院校\n", "set(program_df['school_name_cn'].unique()) - set(old_program_df['学校中文名'].unique())"]}, {"cell_type": "code", "execution_count": 17, "id": "4544b59d", "metadata": {}, "outputs": [{"data": {"text/plain": ["school_region\n", "英国      6643\n", "美国      2014\n", "澳大利亚    1305\n", "中国香港     702\n", "马来西亚     393\n", "新加坡      246\n", "中国澳门     144\n", "Name: count, dtype: int64"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# 手动添加这些学校的地区\n", "new_schools_region_dic = {\n", "    '南安普顿大学马来西亚分校': '马来西亚',\n", "    '圣何塞州立大学': '美国',\n", "    '慕尼黑工业大学亚洲分校': '新加坡',\n", "    '香港演艺学院': '香港',\n", "    '马来亚大学': '马来西亚',\n", "    '马来西亚博特拉大学': '马来西亚',\n", "    '马来西亚国立大学': '马来西亚',\n", "    '马来西亚理工大学': '马来西亚',\n", "    '马来西亚理科大学': '马来西亚',\n", "    '马来西亚莫纳什大学': '马来西亚'\n", "}\n", "\n", "# 学校地点字典\n", "schools_region_dic = dict(zip(old_program_df['学校中文名'], old_program_df['学校所在地区']))\n", "schools_region_dic.update(new_schools_region_dic)\n", "\n", "# 学校所在地区赋值\n", "program_df['school_region'] = program_df['school_name_cn'].map(schools_region_dic)\n", "\n", "# 名称规范\n", "program_df['school_region'] = program_df['school_region'].replace({'香港': '中国香港', '澳门': '中国澳门'})\n", "\n", "program_df['school_region'].value_counts()"]}, {"cell_type": "markdown", "id": "3d419677", "metadata": {}, "source": ["#### 2.3 其他数据补充"]}, {"cell_type": "code", "execution_count": 36, "id": "d082380d", "metadata": {}, "outputs": [{"data": {"text/plain": ["other_cost\n", "20-30万人民币    6643\n", "25-35万人民币    2014\n", "15-25万人民币    1551\n", "10-20万人民币     846\n", "5-10万人民币      393\n", "Name: count, dtype: int64"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["# 添加 生活费 列\n", "other_cost_dic = {\n", "    '英国': '20-30万人民币',\n", "    '美国': '25-35万人民币', \n", "    '澳大利亚': '15-25万人民币',\n", "    '中国香港': '10-20万人民币',\n", "    '马来西亚': '5-10万人民币',\n", "    '新加坡': '15-25万人民币',\n", "    '中国澳门': '10-20万人民币'\n", "}\n", "\n", "program_df['other_cost'] = program_df['school_region'].map(other_cost_dic)\n", "\n", "program_df['other_cost'].value_counts()"]}, {"cell_type": "code", "execution_count": 37, "id": "f1ee5162", "metadata": {}, "outputs": [], "source": ["# 添加 留服认证，embedding，时间戳 列，并设置为空值\n", "program_df['degree'] = '硕士'\n", "program_df['degree_evaluation'] = np.nan\n", "\n", "# CSV表中不添加这几个列的空值，待数据库中自动生成，不如入库时数据类型会出错（pd.read_csv 会自动将空值转换为 NaN float类型...）\n", "# program_df['embedding'] = None # 不能用 np.nan，这个是浮点型，后续数据库模型中设置为JSONB类型\n", "# program_df['create_at'] = pd.NaT\n", "# program_df['update_at'] = pd.NaT"]}, {"cell_type": "markdown", "id": "ef332afb", "metadata": {}, "source": ["#### 2.4 整理与导出"]}, {"cell_type": "code", "execution_count": 38, "id": "f3441689", "metadata": {}, "outputs": [{"data": {"text/plain": ["school_name_cn                  0\n", "school_name_en                  0\n", "school_region                   0\n", "school_labels                  98\n", "school_ranks                  340\n", "school_qs_rank                190\n", "program_code                    0\n", "program_website                12\n", "program_name_cn                 0\n", "program_name_en                 0\n", "program_category                0\n", "program_direction               0\n", "faculty                      2016\n", "enrollment_time                51\n", "program_duration               54\n", "program_tuition                71\n", "application_time              688\n", "application_requirements      475\n", "gpa_requirements             5934\n", "language_requirements         149\n", "interview_type              10719\n", "interview_experience        11151\n", "program_objectives           2141\n", "courses                      2380\n", "consultant_analysis          8805\n", "other_cost                      0\n", "degree                          0\n", "degree_evaluation           11447\n", "dtype: int64"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["new_columns = ['school_name_cn', 'school_name_en', 'school_region', 'school_labels', 'school_ranks', 'school_qs_rank',\n", "'program_code', 'program_website', 'program_name_cn', 'program_name_en', 'program_category', 'program_direction', 'faculty', 'enrollment_time', 'program_duration', 'program_tuition', \n", "'application_time', 'application_requirements', 'gpa_requirements', 'language_requirements', 'interview_type', 'interview_experience',\n", "'program_objectives', 'courses', 'consultant_analysis',\n", "'other_cost', 'degree', 'degree_evaluation']\n", "\n", "# ['embedding', 'create_at', 'update_at']\n", "\n", "# 按照指定的顺序重新排列列\n", "program_df = program_df[new_columns]\n", "\n", "program_df.isnull().sum()"]}, {"cell_type": "code", "execution_count": 40, "id": "536ed529", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'school_name_cn': 13,\n", " 'school_name_en': 67,\n", " 'school_region': 4,\n", " 'school_labels': 107,\n", " 'school_ranks': 36,\n", " 'school_qs_rank': 9,\n", " 'program_website': 250,\n", " 'program_name_cn': 44,\n", " 'program_name_en': 154,\n", " 'program_category': 2,\n", " 'program_direction': 7,\n", " 'faculty': 22,\n", " 'enrollment_time': 11,\n", " 'program_duration': 12,\n", " 'program_tuition': 47,\n", " 'application_time': 350,\n", " 'application_requirements': 1529,\n", " 'gpa_requirements': 91,\n", " 'language_requirements': 297,\n", " 'interview_type': 21,\n", " 'interview_experience': 752,\n", " 'program_objectives': 1008,\n", " 'courses': 18249,\n", " 'consultant_analysis': 2646,\n", " 'other_cost': 9,\n", " 'degree': 2}"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["# 检查字符串长度, 用于后续数据库字段长度设置\n", "def longest_string_length(df):\n", "    max_lengths = {}\n", "    for column in df.select_dtypes(exclude=[int, float]).columns:\n", "        if df[column].dtype == object or df[column].dtype.name.startswith('str'):\n", "            max_lengths[column] = int(df[column].astype(str).apply(len).max())\n", "    return max_lengths\n", "\n", "length_check_result = longest_string_length(program_df)\n", "\n", "length_check_result"]}, {"cell_type": "code", "execution_count": null, "id": "3182ba97", "metadata": {}, "outputs": [], "source": ["# program_df.to_csv('项目数据库.csv', index=False, encoding='utf-8-sig')\n", "# program_df = pd.read_csv('项目数据库.csv')\n", "# program_df.isnull().sum()"]}], "metadata": {"kernelspec": {"display_name": "tunshu_data", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}