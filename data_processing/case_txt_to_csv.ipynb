{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import re\n", "import json\n", "import pandas as pd\n", "from pathlib import Path\n", "\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from thefuzz import fuzz, process"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1 从txt文件中提取相关内容"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "所有行都已成功匹配\n"]}], "source": ["# 定义正则表达式模式\n", "pattern = re.compile(\n", "    r\"offer院校及专业：(?P<offer_school_and_program>.*?)，\"\n", "    r\"申请人姓名：(?P<student_name>.*?)，\"\n", "    r\"来自(?P<undergraduate_school>.*?)，\"\n", "    r\"专业是(?P<undergraduate_major>.*?)，\"\n", "    r\"GPA(?P<gpa>.*?)，\"\n", "    r\"申请目标地区(?P<offer_region>.*?)，\"\n", "    r\"申请学位类型(?P<offer_degree>.*?)，\"\n", "    r\"目标专业(?P<offer_major_direction>.*?)，\"\n", "    r\"语言成绩(?P<language_score>.*?)，\"\n", "    r\"主要经历(?P<key_experiences>.*)\"\n", ")\n", "\n", "# 初始化一个空列表来存储每一行的数据\n", "data = []\n", "unmatched_lines = []\n", "\n", "# 读取文件并逐行处理\n", "with open('merged_cases.txt', 'r', encoding='utf-8') as file:\n", "    for line_number, line in enumerate(file, start=1):\n", "        match = pattern.search(line)\n", "        if match:\n", "            data.append(match.groupdict())\n", "        else:\n", "            unmatched_lines.append((line_number, line.strip()))\n", "\n", "# 创建DataFrame\n", "df = pd.DataFrame(data)\n", "\n", "# 添加额外的列\n", "df['undergraduate_school_tier'] = None\n", "df['offer_school'] = None\n", "df['offer_program'] = None\n", "df['offer_program_id'] = None\n", "\n", "# 输出未匹配的行\n", "if unmatched_lines:\n", "    print(\"\\n未匹配的行:\")\n", "    for line_number, line in unmatched_lines:\n", "        print(f\"行号 {line_number}: {line}\")\n", "else:\n", "    print(\"\\n所有行都已成功匹配\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["手动修改无法匹配的案例，有四条无法匹配，可见都是语言成绩部分有异常：\n", "\n", "行号 880: ...语言成绩六级500，雅思准备中，不考主要经历...\n", "\n", "行号 2084: ...语言成绩雅思7，在考主要经历包括...\n", "\n", "行号 10544: ...语言成绩六级512，在考主要经历...\n", "\n", "行号 19258: ...语言成绩六级500，雅思准备中，不考主要经历..."]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [{"data": {"text/plain": ["(23187, 14)"]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [], "source": ["# df.to_csv('案例数据库（未清洗）.csv', index=False, encoding='utf-8-sig')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2 数据清洗\n"]}, {"cell_type": "code", "execution_count": 93, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>offer_school_and_program</th>\n", "      <th>student_name</th>\n", "      <th>undergraduate_school</th>\n", "      <th>undergraduate_major</th>\n", "      <th>gpa</th>\n", "      <th>offer_region</th>\n", "      <th>offer_degree</th>\n", "      <th>offer_major_direction</th>\n", "      <th>language_score</th>\n", "      <th>key_experiences</th>\n", "      <th>undergraduate_school_tier</th>\n", "      <th>offer_school</th>\n", "      <th>offer_program</th>\n", "      <th>offer_program_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>香港理工大学微电子技术与材料理学硕士</td>\n", "      <td>L同学</td>\n", "      <td>南方科技大学</td>\n", "      <td>微电子科学与工程</td>\n", "      <td>82.6</td>\n", "      <td>香港</td>\n", "      <td>硕士</td>\n", "      <td>微电子技术与材料</td>\n", "      <td>雅思6.0</td>\n", "      <td>富士康科技集团研发部门实习生、二级运算放大器设计、采用溶胶-凝胶法制备负载NiO的TiO2复...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>香港中文大学商业分析理学硕士</td>\n", "      <td>H同学</td>\n", "      <td>中国农业大学</td>\n", "      <td>环境工程</td>\n", "      <td>3.78</td>\n", "      <td>未明确</td>\n", "      <td>硕士</td>\n", "      <td>商业分析</td>\n", "      <td>托福94</td>\n", "      <td>基于期权某模型的投资策略研究、头部券商实习、某市大学生创新创业计划。</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>南洋理工大学人工智能理学硕士</td>\n", "      <td>Z同学</td>\n", "      <td>复旦大学</td>\n", "      <td>计算机科学与技术</td>\n", "      <td>85</td>\n", "      <td>新加坡</td>\n", "      <td>硕士</td>\n", "      <td>人工智能</td>\n", "      <td>雅思6.5</td>\n", "      <td>食堂座位管理系统、京东网安实习、KTH交换。</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  offer_school_and_program student_name undergraduate_school  \\\n", "0       香港理工大学微电子技术与材料理学硕士          L同学               南方科技大学   \n", "1           香港中文大学商业分析理学硕士          H同学               中国农业大学   \n", "2           南洋理工大学人工智能理学硕士          Z同学                 复旦大学   \n", "\n", "  undergraduate_major   gpa offer_region offer_degree offer_major_direction  \\\n", "0            微电子科学与工程  82.6           香港           硕士              微电子技术与材料   \n", "1                环境工程  3.78          未明确           硕士                  商业分析   \n", "2            计算机科学与技术    85          新加坡           硕士                  人工智能   \n", "\n", "  language_score                                    key_experiences  \\\n", "0          雅思6.0  富士康科技集团研发部门实习生、二级运算放大器设计、采用溶胶-凝胶法制备负载NiO的TiO2复...   \n", "1           托福94                 基于期权某模型的投资策略研究、头部券商实习、某市大学生创新创业计划。   \n", "2          雅思6.5                             食堂座位管理系统、京东网安实习、KTH交换。   \n", "\n", "   undergraduate_school_tier  offer_school  offer_program  offer_program_id  \n", "0                        NaN           NaN            NaN               NaN  \n", "1                        NaN           NaN            NaN               NaN  \n", "2                        NaN           NaN            NaN               NaN  "]}, "execution_count": 93, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv('案例数据库（未清洗）.csv')\n", "df.head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 空值检查"]}, {"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["每列的空值数量:\n", "offer_school_and_program         0\n", "student_name                     0\n", "undergraduate_school             0\n", "undergraduate_major              0\n", "gpa                              0\n", "offer_region                     0\n", "offer_degree                     0\n", "offer_major_direction            0\n", "language_score                  21\n", "key_experiences                  0\n", "undergraduate_school_tier    23187\n", "offer_school                 23187\n", "offer_program                23187\n", "offer_program_id             23187\n", "dtype: int64\n"]}], "source": ["# 检查每列的空值情况\n", "missing_values = df.isnull().sum()\n", "\n", "# 输出每列的空值数量\n", "print(\"每列的空值数量:\")\n", "print(missing_values)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- Tips: language_score有21个在原txt文本中显示的是NA，提取后直接在df中为\"NA\"字符串因此显示没有空值。但保存为csv再读取后，pandas会自动检测并将na变为空值，因此就有了21个空值。"]}, {"cell_type": "code", "execution_count": 95, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["每列中含有 '未明确' 、 '未提供' 、 '未提及' 的数量:\n", "\n", "未明确:\n", "  offer_school_and_program: 0\n", "  student_name: 0\n", "  undergraduate_school: 0\n", "  undergraduate_major: 60\n", "  gpa: 0\n", "  offer_region: 808\n", "  offer_degree: 15\n", "  offer_major_direction: 1\n", "  language_score: 1\n", "  key_experiences: 0\n", "  undergraduate_school_tier: 0\n", "  offer_school: 0\n", "  offer_program: 0\n", "  offer_program_id: 0\n", "\n", "未提供:\n", "  offer_school_and_program: 0\n", "  student_name: 0\n", "  undergraduate_school: 0\n", "  undergraduate_major: 1\n", "  gpa: 1\n", "  offer_region: 0\n", "  offer_degree: 0\n", "  offer_major_direction: 0\n", "  language_score: 3436\n", "  key_experiences: 0\n", "  undergraduate_school_tier: 0\n", "  offer_school: 0\n", "  offer_program: 0\n", "  offer_program_id: 0\n", "\n", "未提及:\n", "  offer_school_and_program: 0\n", "  student_name: 0\n", "  undergraduate_school: 0\n", "  undergraduate_major: 5\n", "  gpa: 1\n", "  offer_region: 100\n", "  offer_degree: 2\n", "  offer_major_direction: 0\n", "  language_score: 202\n", "  key_experiences: 0\n", "  undergraduate_school_tier: 0\n", "  offer_school: 0\n", "  offer_program: 0\n", "  offer_program_id: 0\n"]}], "source": ["# 定义要检查的值\n", "values_to_check = [\"未明确\", \"未提供\", \"未提及\"]\n", "\n", "# 初始化一个字典来存储每列中这些值的数量\n", "value_counts = {value: {} for value in values_to_check}\n", "\n", "# 遍历每一列并统计包含指定值的数量 （注意是检查包含，因为有的是“未明确”，有的是“未明确指出”，还有的是“未明确说明”）\n", "for column in df.columns:\n", "    for value in values_to_check:\n", "        count = df[column].astype(str).str.contains(value, na=False).sum()\n", "        value_counts[value][column] = count\n", "\n", "# 输出结果\n", "print(\"每列中含有 '未明确' 、 '未提供' 、 '未提及' 的数量:\")\n", "for value, counts in value_counts.items():\n", "    print(f\"\\n{value}:\")\n", "    for column, count in counts.items():\n", "        print(f\"  {column}: {count}\")"]}, {"cell_type": "code", "execution_count": 96, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["每列的空值数量:\n", "offer_school_and_program         0\n", "student_name                     0\n", "undergraduate_school             0\n", "undergraduate_major             66\n", "gpa                              2\n", "offer_region                   908\n", "offer_degree                    17\n", "offer_major_direction            1\n", "language_score                3660\n", "key_experiences                  0\n", "undergraduate_school_tier    23187\n", "offer_school                 23187\n", "offer_program                23187\n", "offer_program_id             23187\n", "dtype: int64\n"]}], "source": ["# 定义要检查的值\n", "values_to_replace = [\"未明确\", \"未提供\", \"未提及\"]\n", "\n", "# 遍历每一列并替换包含指定值的单元格为空值\n", "for column in df.columns:\n", "    for value in values_to_replace:\n", "        # 检测包含指定值的行\n", "        mask = df[column].astype(str).str.contains(value, na=False)\n", "        # 将这些行中的相应列设置为空值\n", "        df.loc[mask, column] = pd.NA\n", "\n", "# 检查每列中空值的数量以确认替换成功\n", "missing_values = df.isnull().sum()\n", "\n", "# 输出每列的空值数量\n", "print(\"每列的空值数量:\")\n", "print(missing_values)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 申请目标地区检查和处理（offer_region）"]}, {"cell_type": "code", "execution_count": 97, "metadata": {}, "outputs": [{"data": {"text/plain": ["offer_region\n", "英国              8162\n", "香港              5529\n", "新加坡             3574\n", "澳大利亚            1981\n", "中国香港            1609\n", "美国              1341\n", "<NA>             908\n", "中国澳门              69\n", "中国                10\n", "德国                 2\n", "未直接提及              1\n", "未直接提及但可推断为香港       1\n", "Name: count, dtype: int64"]}, "execution_count": 97, "metadata": {}, "output_type": "execute_result"}], "source": ["df.offer_region.value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": 98, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["offer_region\n", "英国      8162\n", "香港      7139\n", "新加坡     3576\n", "澳大利亚    1981\n", "美国      1341\n", "<NA>     909\n", "澳门        69\n", "中国        10\n", "Name: count, dtype: int64\n"]}], "source": ["# 定义要替换的映射关系\n", "region_mapping = {\n", "    \"中国香港\": \"香港\",\n", "    \"中国澳门\": \"澳门\",\n", "    \"德国\": \"新加坡\",  # 两所都是慕尼黑工业大学亚洲分校，位于新加坡\n", "    \"未直接提及\": pd.NA,\n", "    \"未直接提及但可推断为香港\": \"香港\"\n", "}\n", "\n", "# 使用 replace 方法进行替换\n", "df['offer_region'] = df['offer_region'].replace(region_mapping)\n", "\n", "# 检查 offer_region 列的值以确认替换成功\n", "print(df['offer_region'].value_counts(dropna=False))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 申请学位类型检查和处理（offer_degree ）"]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [{"data": {"text/plain": ["offer_degree\n", "硕士       23008\n", "为硕士         89\n", "法律博士        27\n", "硕士学位        17\n", "文学硕士         7\n", "博士           6\n", "理学硕士         5\n", "为法律博士        4\n", "PhD          3\n", "为研究生         1\n", "暑校           1\n", "硕士预科         1\n", "研究型硕士        1\n", "Name: count, dtype: int64"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["df.offer_degree.value_counts()"]}, {"cell_type": "code", "execution_count": 101, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["offer_degree\n", "硕士      23128\n", "法律博士       31\n", "<NA>       17\n", "博士          9\n", "暑校          1\n", "硕士预科        1\n", "Name: count, dtype: int64\n"]}], "source": ["# 定义要替换的映射关系\n", "degree_mapping = {\n", "    \"为硕士\": \"硕士\",\n", "    \"硕士学位\": \"硕士\",\n", "    \"文学硕士\": \"硕士\",\n", "    \"理学硕士\": \"硕士\",\n", "    \"为研究生\": \"硕士\",\n", "    \"研究型硕士\": \"硕士\",\n", "    \"为博士\": \"博士\",\n", "    \"为法律博士\": \"法律博士\",\n", "    \"PhD\": \"博士\"\n", "}\n", "\n", "# 使用 replace 方法进行替换\n", "df['offer_degree'] = df['offer_degree'].replace(degree_mapping)\n", "\n", "# 检查 offer_degree 列的值以确认替换成功\n", "print(df['offer_degree'].value_counts(dropna=False))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 语言成绩检查与处理（language_score）"]}, {"cell_type": "code", "execution_count": 102, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["含 'IELTS' 的行数量: 34\n", "含 'TOEFL' 的行数量: 20\n"]}], "source": ["# 检查 language_score 列中含有 \"IELTS\" 和 \"TOEFL\" 的行的数量\n", "ielts_count = df['language_score'].astype(str).str.contains(\"IELTS\", na=False).sum()\n", "toefl_count = df['language_score'].astype(str).str.contains(\"TOEFL\", na=False).sum()\n", "\n", "print(f\"含 'IELTS' 的行数量: {ielts_count}\")\n", "print(f\"含 'TOEFL' 的行数量: {toefl_count}\")"]}, {"cell_type": "code", "execution_count": 103, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["含 'IELTS' 的行数量: 0\n", "含 'TOEFL' 的行数量: 0\n"]}], "source": ["# 替换 \"IELTS\" 为 \"雅思\"\n", "df['language_score'] = df['language_score'].astype(str).str.replace(\"IELTS\", \"雅思\", regex=True)\n", "\n", "# 替换 \"TOEFL\" 为 \"托福\"\n", "df['language_score'] = df['language_score'].astype(str).str.replace(\"TOEFL\", \"托福\", regex=True)\n", "\n", "# 检查 language_score 列中含有 \"IELTS\" 和 \"TOEFL\" 的行的数量\n", "ielts_count = df['language_score'].astype(str).str.contains(\"IELTS\", na=False).sum()\n", "toefl_count = df['language_score'].astype(str).str.contains(\"TOEFL\", na=False).sum()\n", "\n", "print(f\"含 'IELTS' 的行数量: {ielts_count}\")\n", "print(f\"含 'TOEFL' 的行数量: {toefl_count}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 主要经历检查与处理（key_experiences）"]}, {"cell_type": "code", "execution_count": 104, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开头含 '包括' 的行数量: 10042\n", "处理后开头含 '包括' 的行数量: 0\n"]}], "source": ["# 检查 key_experiences 列中开头包含 \"包括\" 的行的数量\n", "includes_count = df['key_experiences'].astype(str).str.startswith(\"包括\").sum()\n", "\n", "print(f\"开头含 '包括' 的行数量: {includes_count}\")\n", "\n", "# 去掉开头的 \"包括\"\n", "df['key_experiences'] = df['key_experiences'].astype(str).str.replace(r\"^包括\", \"\", regex=True)\n", "\n", "includes_count = df['key_experiences'].astype(str).str.startswith(\"包括\").sum()\n", "\n", "print(f\"处理后开头含 '包括' 的行数量: {includes_count}\")"]}, {"cell_type": "code", "execution_count": 105, "metadata": {}, "outputs": [], "source": ["# df.to_csv('案例数据库（已清洗）.csv', index=False, encoding='utf-8-sig')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3 学校及专业名提取与匹配"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["case_df = pd.read_csv('案例数据库（已清洗）.csv')\n", "program_df = pd.read_csv('专业数据库.csv')\n", "# program_df = program_df[['学校中文名', '专业中文名']]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3.1 本科学校分级"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# 读取 tiers.json 文件\n", "with open('tiers.json', 'r', encoding='utf-8') as file:\n", "    tiers = json.load(file)\n", "\n", "# 提取 tier1 和 tier2 学校列表\n", "tier1_schools = set(tiers.get('tier1', []))\n", "tier2_schools = set(tiers.get('tier2', []))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# 定义一个函数来确定学校的层级\n", "def get_school_tier(school):\n", "    if school in tier1_schools:\n", "        return 'tier1'\n", "    elif school in tier2_schools:\n", "        return 'tier2'\n", "    else:\n", "        return 'tier3'\n", "\n", "# 应用函数到 undergraduate_school 列\n", "case_df['undergraduate_school_tier'] = case_df['undergraduate_school'].apply(get_school_tier)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3.2 学校名和专业名拆分"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["#问题是case_df中的一些学校名并未在program_df['学校中文名']中出现，因此可能有些还是无法分割。所以能否结合一下，先用完整名匹配，匹配不到再用末尾两字关键词来匹配？\n", "# 结合两种方法的优点可以大大提高分割的成功率。这种策略兼顾了准确性和灵活性：\n", "# 优先使用完整校名匹配：确保已知学校能被最准确地分割。\n", "# 使用关键词作为后备：对于未在program_df中出现的新学校，尝试用关键词（如“大学”、“学院”）进行分割。\n", "\n", "# --- 步骤 1: 准备两种匹配模式 ---\n", "\n", "# 模式A：用于完整校名匹配 (来自更稳健的方案)\n", "school_names = program_df['学校中文名'].unique()\n", "sorted_school_names = sorted(school_names, key=len, reverse=True)\n", "full_name_pattern = '|'.join(map(re.escape, sorted_school_names))\n", "\n", "# 模式B：用于末尾关键词匹配 (来自你的原始方案)\n", "# 注意：我们从 '学校中文名' 的末尾提取关键词，以涵盖更多可能性\n", "# 比如 '大学', '学院', '国）', '分校' 等\n", "keywords_from_programs = program_df['学校中文名'].str[-2:].unique()\n", "# 也可以手动添加一些通用关键词\n", "additional_keywords = []\n", "all_keywords = set(list(keywords_from_programs) + additional_keywords)\n", "suffix_keyword_pattern = '|'.join(map(re.escape, all_keywords))\n", "\n", "\n", "# --- 步骤 2: 定义两种独立的分割函数 ---\n", "\n", "def split_by_full_name(text, pattern):\n", "    \"\"\"策略一：从字符串开头匹配最长的可能校名。\"\"\"\n", "    if not pattern: # 如果模式为空，直接返回\n", "        return None, None\n", "    match = re.match(pattern, text)\n", "    if match:\n", "        offer_school = match.group(0)\n", "        offer_program = text[len(offer_school):]\n", "        return offer_school.strip(), offer_program.strip()\n", "    else:\n", "        return None, None\n", "\n", "def split_by_suffix_keyword(text, pattern):\n", "    \"\"\"策略二：根据关键词，使用最后一个匹配项作为分割点。\"\"\"\n", "    if not pattern: # 如果模式为空，直接返回\n", "        return None, None\n", "    matches = list(re.finditer(pattern, text))\n", "    if matches:\n", "        last_match = matches[-1]\n", "        school_end_index = last_match.end()\n", "        offer_school = text[:school_end_index]\n", "        offer_program = text[school_end_index:]\n", "        return offer_school.strip(), offer_program.strip()\n", "    else:\n", "        return None, None\n", "\n", "# --- 步骤 3: 定义混合策略函数 ---\n", "\n", "def split_hybrid(text, full_pattern, suffix_pattern):\n", "    \"\"\"\n", "    混合分割策略：\n", "    1. 优先尝试完整校名匹配。\n", "    2. 如果失败，则回退到末尾关键词匹配。\n", "    \"\"\"\n", "    # 优先尝试策略一\n", "    school, program = split_by_full_name(text, full_pattern)\n", "    \n", "    # 如果策略一成功（即 school 不是 None），直接返回结果\n", "    if school:\n", "        return school, program\n", "    \n", "    # 如果策略一失败，则回退到策略二\n", "    return split_by_suffix_keyword(text, suffix_pattern)\n", "\n", "\n", "# --- 步骤 4: 应用混合策略到 DataFrame ---\n", "\n", "# 使用 .apply() 和一个 lambda 函数来调用混合策略\n", "case_df[['offer_school', 'offer_program']] = case_df['offer_school_and_program'].apply(\n", "    lambda x: pd.Series(split_hybrid(x, full_name_pattern, suffix_keyword_pattern))\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["最终未能成功分割的行数: 0\n"]}], "source": ["# 找出经过两种方法仍然没有成功分割的行\n", "unmatched_rows = case_df[case_df['offer_school'].isnull()]\n", "print(f\"最终未能成功分割的行数: {len(unmatched_rows)}\")\n", "if not unmatched_rows.empty:\n", "    print(\"未能分割的行示例：\")\n", "    print(unmatched_rows)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3.3 学校名及专业名核查"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["在案例数据中但不在专业数据中的学校名称共：13个，具体名称如下：\n", "西北大学\n", "密歇根大学\n", "慕尼黑工业大学亚洲分校\n", "阿伯泰大学\n", "东北大学\n", "亚利桑那大学\n", "提赛德大学\n", "创意艺术大学\n", "克拉克大学\n", "纽约州立大学石溪分校\n", "诺丁汉特伦特大学\n", "诺森比亚大学\n", "伦敦艺术大学\n", "=======================================================\n", "\n", "在案例数据中但不在专业数据中的专业名称共：505个，具体名称如下：\n", "计算机科学与商业理学硕士\n", "传播学文学硕士（运动、健康与传播专修）\n", "组织管理理学硕士\n", "房屋及都市管理文学硕士（都市管理方向）\n", "数字经济\n", "定量金融经济学理学硕士\n", "理学硕士（2年制）\n", "国际经济与金融\n", "数字纪录片文学硕士\n", "传播学（传播与新媒体）文学硕士\n", "专业工程硕士（加速）（生物医学）\n", "工业4.0理学硕士\n", "测绘及地理资讯学理学硕士（地理资讯）\n", "传播学文学硕士人工智能与数码媒体专修\n", "可持续城市发展学理学硕士\n", "金融科技\n", "比较文学（非洲/亚洲）文学硕士\n", "Creative and Cultural Industries Management MA\n", "土力学理学硕士\n", "应用统计学\n", "生物科学理学硕士（生物科学）\n", "本硕直通车课程-金融学\n", "基础设施项目管理（工程）理学硕士\n", "材料科学与工程博士学位\n", "计算社会科学理学硕士（CUHK-Shenzhen）\n", "心理学与教育学理学硕士\n", "教育学：国际视角文学硕士\n", "会计学硕士（1.5年制）\n", "专业工程硕士（加速）（软件）\n", "电影：理论与实践文学硕士\n", "International Music Management MA/MSc\n", "聚合物、着色剂与精细化学品理学硕士\n", "软电子材料研究型硕士\n", "教育学文学硕士（领导力）\n", "传播学文学硕士传理学专修（北师大港浸会联合国际学院）\n", "金融建模与投资理学硕士\n", "航空航天技术理学硕士\n", "风险企业创业理学硕士\n", "教育咨询硕士\n", "专业工程硕士（加速）（土木）\n", "环境政策与规划\n", "海洋技术理学硕士\n", "中国政治学理学硕士\n", "公共健康学硕士（流行病学）\n", "移动通信与智能网络\n", "音乐教学文学硕士\n", "专业工程学硕士（电力）\n", "新兴媒体硕士\n", "自动化与智能系统理学硕士\n", "数字通信网络（工程）理学硕士\n", "法学院\n", "物流工程与管理理学硕士\n", "国际教育（全球化与国际发展）文学硕士\n", "市场营销\n", "康复治疗\n", "文化产业文学硕士\n", "土木工程（工程）理学硕士-结构工程方向\n", "教育研究硕士（1.5年制）\n", "媒体与国际发展文学硕士\n", "经济发展（金融与政策）理学硕士\n", "基础设施工程与管理（工程）理学硕士-基础设施项目管理方向\n", "媒体科学硕士：市场传播研究\n", "专业工程学硕士（加速）（电信）\n", "医学检验科学理学硕士\n", "经济，国家和社会：经济与商业\n", "金融学与经济计量学理学硕士\n", "气候科学与环境硕士\n", "市场营销（CUHKSZ）\n", "会计与金融经济学硕士\n", "供应链与物流分析理学硕士\n", "国际商法硕士\n", "金融学理学硕士（中文学制）\n", "结构工程工学硕士\n", "理学硕士（1.5年制）\n", "-传感器与成像系统理学硕士\n", "管理与系统理学硕士\n", "金融管理理学硕士\n", "博物馆与遗产教育国际硕士（伊拉斯莫斯世界计划）\n", "数据科学理学硕士商业统计专修\n", "交通系统管理硕士（地理信息系统）\n", "金融\n", "管理与公共政策硕士\n", "翻译硕士\n", "金融学（国际货币、金融与投资）理学硕士\n", "会计和商业分析理学硕士\n", "信息技术硕士（1.5年制）\n", "-媒体实践硕士\n", "金融学（会计与金融）\n", "教育学硕士（中文学制）\n", "数据科学与统计学理学硕士\n", "传播学社会科学硕士\n", "管理学（金融）理学硕士\n", "商业管理理学硕士（带一年职业实习）\n", "运动管理、政治与国际发展\n", "传播文学硕士\n", "新媒体管理硕士\n", "营销管理实践理学硕士\n", "公共卫生及流行病学理学硕士\n", "传播学硕士（针对社会改变的传播/公共关系与职业传播）\n", "土木工程（工程）理学硕士-岩土工程方向\n", "水利工程理学硕士\n", "传播学文学硕士互动媒体专修\n", "半导体光子学与电子学理学硕士\n", "全球政治学理学硕士\n", "教育、政策与社会理学硕士\n", "集成电路设计理学硕士\n", "Master of Public Health--Infectious Disease Epidemiology\n", "Master of Science in Bacteriology\n", "Biochemical Engineering with Industrial Management\n", "电力工程工学硕士\n", "工程科学硕士（机械工程）\n", "Pre-MSc Management\n", "全球传播与社会变革文学硕士\n", "PhD in School of Mechanical and Aerospace Engineering\n", "政治传播学\n", "-爱丁堡大学联合开设的传感器与成像系统理学硕士\n", "全球传媒管理文学硕士\n", "应用语言学硕士（2年制）\n", "运动物理治疗\n", "东亚关系理学硕士\n", "数字创新与分析理学硕士\n", "高级化学工程与材料工程理学硕士\n", "NUSCEMS Master in International Management\n", "国际关系与发展硕士\n", "数据科学与数字优化的运输理学硕士\n", "政治学与公共政策经济学硕士\n", "商务（咨询）理学硕士\n", "机械工程系博士\n", "新兴数字技术\n", "金融数学专业\n", "法学硕士（1年制）\n", "全球健康理学硕士（昆山）\n", "商业分析硕士（2年制）\n", "人力资源与组织理学硕士\n", "社会与文化人类学硕士\n", "高级机器人学理学硕士\n", "电子工程（含实习/专业研究）理学硕士\n", "-集成电路设计理学硕士\n", "健康数据分析理学硕士\n", "数字社会学\n", "应用戏剧与干预文学硕士\n", "地理空间应用专业研究硕士\n", "应用市场营销硕士/管理学硕士双学位\n", "信息技术硕士与信息技术管理硕士\n", "音乐教育：乐器和声乐教学\n", "管理学理学硕士（战略、运营与组织领导力）\n", "国际经济与金融理学硕士\n", "数据科学硕士（中文学制）\n", "生物材料硕士\n", "国际语言教育文学硕士（对外汉语教）\n", "健康政策与经济学硕士\n", "民用航空工程学理学硕士\n", "应用经济学与银行和金融市场\n", "国际商法（英文）法学硕士\n", "生物信息学理学硕士（CUHK-Shenzhen）\n", "Master of Science in Electrical & Computer Engineering（Machine Learning and Data Science）\n", "音乐硕士（1年制）\n", "健康信息学理学硕士（伦敦大学学院-曼彻斯特大学联合开设）\n", "传播学文学硕士影视与新媒体制片管理专修\n", "信息系统理学硕士（衔接课程）\n", "两年制工商管理硕士\n", "表演学音乐硕士\n", "数据科学与分析\n", "国际时尚管理硕士\n", "公司财务理学硕士\n", "项目管理理学硕士-硅谷\n", "燃料电池与氢技术理学硕士\n", "肌肉骨骼运动科学与健康\n", "教育技术、创新与思维文学硕士\n", "自动化与制造系统工学硕士\n", "MALuxuryBusinessManagement\n", "PhD Physics\n", "MS in Molecular Microbiology and Immunology\n", "科技管理硕士\n", "社会研究（当地政府与公共政策）文学硕士\n", "国际人力资源管理硕士\n", "高级化学工程（配方方向）理学硕士\n", "应用心理学理学硕士（CUHK-Shenzhen）\n", "发展心理学硕士\n", "语言学与英语语言教学文学硕士\n", "英语语言教学与应用语言学文学硕士（TESOL）\n", "社会政策与社会研究理学硕士\n", "经济、国家与社会:政治与国际经济硕士\n", "能源与环境理学硕士\n", "-商学硕士\n", "会计与信息分析理学硕士\n", "工业化学理学硕士\n", "葡萄栽培与酿酒\n", "数字系统工程理学硕士\n", "城巿设计与规划学硕士\n", "法学理论硕士\n", "MAVisualEffects\n", "可持续商业、环境与社会理学硕士\n", "MS in Cell & Molecular Biology\n", "媒体与资讯文学硕士\n", "教育研究硕士（2年制）\n", "阿拉伯语/英语翻译文学硕士\n", "MS in Biology\n", "科技创业与创新理学硕士（中文授课）\n", "金融学（金融科技与金融分析）\n", "公共政策与管理\n", "工程硕士（管理科学与工程方向）\n", "城市学社会科学硕士（CUHK-Shenzhen）\n", "基础设施工程与管理（工程）理学硕士-数字基础设施方向\n", "中国语言文学文学硕士（CUHK-Shenzhen）\n", "电信工程工学硕士\n", "电气与电子工程（工程）理学硕士-电力工程方向\n", "UMMasterofScienceinGlobalPublicHealth\n", "翻译（笔译/口译）文学硕士（CUHK-Shenzhen）\n", "语言学\n", "专业工程硕士（加速）（智能信息工程）\n", "土木与环境工学硕士\n", "公共政策硕士（2年制）\n", "土木工程（工程）理学硕士-通用方向\n", "中国与区域研究文学硕士\n", "计算机科学硕士（1.5年制）\n", "VisualEffects\n", "空间经济学理学硕士\n", "移动与个人通信理学硕士\n", "媒体与政治文学硕士\n", "教育行政哲学硕士\n", "中国商贸管理理学硕士\n", "物联网与未来网络理学硕士\n", "人力资源管理与组织行为学理学硕士\n", "信息与通信技术开发理学硕士\n", "高级化学理学硕士\n", "投资资金与管理理学硕士\n", "国际关系与公共政策社会科学硕士\n", "交通工程规划与管理\n", "传播学文学硕士-互动媒体研究专修\n", "信息科学理学硕士\n", "课程与教学哲学硕士\n", "应用环境水文地质学理学硕士\n", "应用生物分子技术理学硕士\n", "商业分析与风险管理理学硕士\n", "全球商业硕士\n", "-繁荣、创新与创业理学硕士\n", "国际货币与银行理学硕士\n", "高级化学工程（能源方向）理学硕士\n", "数据科学理学硕士商业统计专修（北师大港浸会联合国际学院）\n", "轨道、交通与物流理学硕士\n", "计算与信息系统理学硕士\n", "商业与经济发展理学硕士\n", "电气与电子工程（工程）理学硕士-通信工程方向\n", "土木工程（工程）理学硕士-环境工程方向\n", "定量经济学硕士\n", "运动人体科学\n", "工程科学硕士（1年制）\n", "电气与计算机工程理学硕士-机器学习方向\n", "生物医学工程（计算生物工程）\n", "国际传媒与传播研究文学硕士\n", "-格拉斯哥大学联合开设传感器与成像系统理学硕士\n", "科学计算与数据分析理学硕士\n", "航运法法学硕士\n", "实验经济学理学硕士\n", "能源与电力系统（工程）理学学硕士\n", "运输基础设施工程理学硕士\n", "商务资讯系统理学硕士（管理信息系统）\n", "专业工程学硕士（航空航天）\n", "高级计算机科学理学硕士（语义技术）\n", "社会文化人类学\n", "数字传播与文化\n", "高级计算机科学理学硕士（计算机安全）\n", "国际仲裁与争议解决法法学硕士\n", "管理经济学理学硕士（英文授课）\n", "电气与计算机工程博士学位\n", "哲学理学硕士（可跨申）\n", "Master of Computer Science\n", "可再生能源工程工学硕士\n", "工程技术与商业管理（工程）理学硕士\n", "会计学理学硕士（CUHK-Shenzhen）\n", "旅游、遗产与发展理学硕士\n", "应用语言学硕士（1年制）\n", "人类学和地球未来硕士\n", "音乐技术理学硕士\n", "口译文学硕士（2年制）\n", "翻译与口译学\n", "法学硕士（1.5年制）\n", "城市战略与设计理学硕士\n", "环境科学与管理\n", "对外英语教育\n", "Bristol LLM Law, Innovation and Technology\n", "材料工程与纳米技术理学硕士（CityU-DG）\n", "地理与环境工程\n", "研究方法（人类学）文学硕士\n", "对外英语教学教育理学硕士\n", "数据科学（环境分析）理学硕士\n", "社会研究方法（社会学）文学硕士\n", "计算机科学理学硕士–Align\n", "遥感与地理空间科学\n", "电气与计算机工程系\n", "国际贸易与发展硕士\n", "金融工程理学硕士（CUHK-Shenzhen）\n", "口译、笔译与跨文化传播文学硕士\n", "电气工程系\n", "科学计算与数据分析（金融科技）理学硕士\n", "老年学与老龄化理学硕士\n", "汉语言文学博士\n", "英语语言教学文学硕士\n", "信号处理理学硕士\n", "材料科学与工程工程硕士\n", "全球公民教育文学硕士\n", "数据科学硕士（2年制）\n", "计算机网络硕士\n", "公共法律法学硕士\n", "管理经济学理学硕士（中文授课）\n", "计算科学与工程\n", "消防安全工程\n", "地质力学工程工学硕士\n", "制造工程创新与管理理学硕士\n", "会计、金融与财务分析理学硕士\n", "战略沟通文学硕士\n", "计算机与信息工程理学硕士（CUHK-Shenzhen）\n", "城市与区域规划\n", "Pre-Masters for MATESOL with Applied Linguistics\n", "红鸟硕士（HKUST(GZ)）\n", "学习科学与技术\n", "康复治疗学\n", "营养、食品科学与技术理学硕士\n", "-应用经济学理学硕士&社会与经济政策理学硕士双学位\n", "制造系统与工程理学硕士\n", "大数据科学理学硕士\n", "专业工程硕士（加速）（电气）\n", "房地产规划与发展文学硕士\n", "国际商务\n", "测绘及地理资讯学理学硕士（测量）\n", "老龄学理学硕士\n", "生物技术硕士（2年制）\n", "计算机工程理学硕士-机器智能与应用方向\n", "MHS in Molecular Microbiology and Immunology\n", "国际房地产管理理学硕士\n", "应用气象学理学硕士\n", "统计学（数据科学）\n", "智能系统科技硕士\n", "国际技术管理理学硕士\n", "中国社会与文化理学硕士\n", "运输规划与工程理学硕士\n", "国际可持续发展硕士\n", "音乐产业研究\n", "社交媒体与数字传播\n", "智能系统科技\n", "粒子、弦与宇宙学理学硕士\n", "-战略传播学硕士\n", "应用数据科学和统计\n", "计算机工程理学硕士-通识方向\n", "-应用经济学理学硕士&社会与经济政策理学硕士（香港中文大学-美国西北大学）\n", "教育科学硕士-国际教学和全球领导力队列\n", "媒体、文化与日常生活文学硕士\n", "公共卫生硕士（公共卫生实践-综合初级医疗保健）\n", "国际经济与金融硕士（1.5年制）\n", "金融数学硕士（1.5年制）\n", "伊斯兰教与基督教-穆斯林关系理学硕士\n", "专业工程学硕士（电信）\n", "细胞与基因治疗理学硕士\n", "生物保护硕士\n", "应用经济学理学硕士&社会与经济政策理学硕士（香港中文大学-美国西北大学）\n", "应用数学与统计\n", "理论与应用语言学高级研究哲学硕士\n", "数量金融理学硕士\n", "跨国研究文学硕士\n", "临床药理学\n", "电子资讯工程学理学硕士（CityU-DG）\n", "营销与零售科学理学硕士（上海）\n", "物理治疗\n", "传播学文学硕士传媒管理专修\n", "信息技术硕士（2年制）\n", "劳动法与公司治理法学硕士\n", "全球商务硕士（1.5年制）\n", "技术领导力与创业理学硕士\n", "应用城市信息科学硕士\n", "暑校\n", "工程材料学（高级机械工程科学）\n", "大数据科学理学硕士（含工业经验）\n", "地理空间与测绘研究理学硕士\n", "应用经济学理学硕士&社会与经济政策理学硕士双学位（香港中文大学-美国西北大学）\n", "心理学与教育理学硕士\n", "市场学理学硕士（CUHK-Shenzhen）\n", "结构与消防安全工程理学硕士\n", "财务预测与投资理学硕士\n", "理学硕士（数学与统计学）\n", "-集成电路设计理学硕士（南洋理工大学-慕尼黑工业大学联合开设）\n", "Computer Games Technology\n", "国际商法（知识产权法与管理）\n", "计算机工程理学硕士-数字化与通信技术方向\n", "金融学（资产定价）\n", "国际旅游及会展管理理学硕士\n", "制药科学硕士（2年制）\n", "宗教与伦理哲学文学硕士（全球宗教哲学方向）\n", "语言与跨文化研究文学硕士\n", "商业分析与风险管理硕士\n", "信息管理&商业分析理学硕士（CUHK-Shenzhen）\n", "高级管理经济学理学硕士（中文授课）\n", "全球媒体产业\n", "亚洲与国际历史文学硕士双学位（伦敦政治经济学院-新加坡国立大学）\n", "经济学理学硕士（CUHK-Shenzhen）\n", "国际关系与东亚文学\n", "营销传播理学硕士\n", "传播学文学硕士传媒管理专修（北师大港浸会联合国际学院）\n", "银行与金融理学硕士(伦敦校区)\n", "房屋及都市管理文学硕士（房屋方向）\n", "大数据智能理学硕士（HKUST(GZ)）\n", "计算机视觉，机器人与机器学习理学硕士\n", "交通工程（工程）理学硕士\n", "传播学硕士（1.5年制）\n", "数字商业与数据分析\n", "水利工程与水资源管理理学硕士\n", "考古学（古典地中海）文学硕士\n", "通信工程理学硕士（CUHK-Shenzhen）\n", "职业电气工程学硕士（2年制）\n", "管理学理学硕士（运营）\n", "公共考古学文学硕士\n", "机电一体化与机器人学（工程）理学硕士\n", "（国际）经济、国家与社会：政治与国际经济文学硕士\n", "行为与经济科学（经济学）理学硕士\n", "MSc in Marketing Analytics and Insights + CEMS MIM\n", "动物科学硕士（1.5年制）\n", "数字文化与传播\n", "传播学硕士（2年制）\n", "知识产权与信息法法学硕士\n", "媒体与创意产业文学硕士\n", "国际政治经济：批评理论、问题与冲突文学硕士\n", "气候变化金融和投资\n", "国际公共关系与全球通信管理文学硕士\n", "经济学硕士（1.5年制）\n", "房地产资产管理理学硕士\n", "学习技术、设计和领导力理学硕士（教育技术领导力）\n", "信息技术\n", "计算机科学系\n", "国际经济学和金融硕士/商业硕士双学位\n", "创新与营销管理理学硕士\n", "笔译与口译硕士（1.5年制）\n", "基督教与艺术文学硕士\n", "电子商贸技术理学硕士\n", "国际商务与新兴市场理学硕士\n", "创意写作\n", "-艺术与文化遗产管理文学硕士\n", "专业工程学硕士（化学与生物分子）\n", "地理监测与公共管理理学硕士\n", "语言学与翻译系博士\n", "文化数据管理与传播硕士\n", "数据科学（统计学）理学硕士\n", "生物多样性保护与基于自然的气候解决方案理学硕士\n", "嵌入式无线系统理学硕士\n", "集成电路设计工程理学硕士\n", "社会与文化人类学\n", "STEM工商管理硕士\n", "金融（国际货币、金融与投资）理学硕士\n", "计算机科学博士\n", "Master of Science in Innovative Materials\n", "-传感器与成像系统理学硕士（爱丁堡大学-格拉斯哥大学联合开设）\n", "电信与互联网技术理学硕士\n", "电气工程与计算机科学\n", "食品科学与农业综合企业\n", "发展研究社会科学硕士\n", "数据科学和分析\n", "康复科学\n", "艺术新闻文学硕士\n", "金融学（公司金融）\n", "电子与计算机工程硕士-机器学习与数据科学\n", "机器人学与计算理学硕士\n", "-环球商业与决策分析理学硕士\n", "数字商务（电子营销）理学硕士\n", "环境领导与管理专业\n", "生物技术硕士（1.5年制）\n", "电子与计算机工程\n", "传播学文学硕士传理学专修\n", "音乐管理\n", "创造力：创新与商业战略文学硕士\n", "数据科学理学硕士（CUHK-Shenzhen）\n", "碳管理理学硕士\n", "管理学硕士-金融\n", "工业与系统工程工程硕士\n", "专业工程硕士（加速）（航空航天）\n", "软电子材料\n", "脑成像与认识神经科学理学硕士\n", "-生成式人工智能与人文科学（艺术与文化）理学硕士\n", "风险与投资管理理学硕士\n", "铁路管理与工程理学硕士\n", "道路管理与工程理学硕士\n", "数据科学（应用城市分析）\n", "应用金融学\n", "-绿色电子理学硕士\n", "全球营销管理理学硕士\n", "发育障碍康复学\n", "人工智能与机器人理学硕士（CUHK-Shenzhen）\n", "纳米科学与功能纳米材料理学硕士\n", "国际时时尚零售（创业与创新）理学硕士\n", "MA Documentary Film\n", "物流与供应链管理\n", "国际传媒商业文学硕士\n", "电气与电子工程（工程）理学硕士-常规方向\n", "国际文化政策与管理文学硕士\n", "地球和环境工程理学硕士\n", "大数据与高性能运算理学硕士\n", "国际公共关系与全球传播管理文学硕士\n", "电影电视与数码媒体艺术\n", "数字信息管理与系统创新理学硕士\n", "空间经济学与数据分析理学硕士预科\n", "Electronic Engineering\n", "中文笔译与口译文学硕士\n", "医疗机器人技术和图像引导干预硕士\n", "公共管理硕士（英文授课）\n", "高级计算机科学理学硕士（数据与知识管理）\n", "数据科学硕士（1.5年制）\n", "数据科学（计算机语言学）理学硕士\n", "=======================================================\n", "\n", "学校名在案例数据中但不在专业数据中的案例条数：61\n", "专业名在案例数据中但不在专业数据中的案例条数：1720\n"]}], "source": ["# 获取唯一的学校和专业名称\n", "case_schools_unique = set(case_df['offer_school'].unique()) #145个学校名\n", "case_programs_unique = set(case_df['offer_program'].unique()) # 2894个专业名... \n", "\n", "program_schools_unique = set(program_df['学校中文名'].unique()) # 170个学校名\n", "program_programs_unique = set(program_df['专业中文名'].unique()) # 7292个专业名\n", "\n", "# 核查学校名称\n", "missing_schools = case_schools_unique - program_schools_unique\n", "if missing_schools:\n", "    print(f\"在案例数据中但不在专业数据中的学校名称共：{len(missing_schools)}个，具体名称如下：\")\n", "    for school in missing_schools:\n", "        print(school)\n", "else:\n", "    print(\"所有案例数据中的学校名称都在专业数据中找到。\")\n", "\n", "print(\"=======================================================\")\n", "\n", "# 核查专业名称\n", "missing_programs = case_programs_unique - program_programs_unique\n", "if missing_programs:\n", "    print(f\"\\n在案例数据中但不在专业数据中的专业名称共：{len(missing_programs)}个，具体名称如下：\")\n", "    for program in missing_programs:\n", "        print(program)\n", "else:\n", "    print(f\"\\n所有案例数据中的专业名称都在专业数据中找到。\")\n", "\n", "print(\"=======================================================\")\n", "\n", "print(f'\\n学校名在案例数据中但不在专业数据中的案例条数：{len(case_df[case_df['offer_school'].isin(missing_schools)])}')\n", "print(f'专业名在案例数据中但不在专业数据中的案例条数：{len(case_df[case_df['offer_program'].isin(missing_programs)])}')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>offer_school_and_program</th>\n", "      <th>offer_school</th>\n", "      <th>offer_program</th>\n", "      <th>offer_region</th>\n", "      <th>undergraduate_school</th>\n", "      <th>gpa</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>971</th>\n", "      <td>东北大学信息系统硕士</td>\n", "      <td>东北大学</td>\n", "      <td>信息系统硕士</td>\n", "      <td>美国</td>\n", "      <td>电子科技大学</td>\n", "      <td>81.63</td>\n", "    </tr>\n", "    <tr>\n", "      <th>998</th>\n", "      <td>东北大学信息系统硕士</td>\n", "      <td>东北大学</td>\n", "      <td>信息系统硕士</td>\n", "      <td>美国</td>\n", "      <td>华东理工大学</td>\n", "      <td>3.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2395</th>\n", "      <td>东北大学企业与组织传播硕士</td>\n", "      <td>东北大学</td>\n", "      <td>企业与组织传播硕士</td>\n", "      <td>美国</td>\n", "      <td>湖北美术学院</td>\n", "      <td>3.19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2892</th>\n", "      <td>西北大学计算机工程硕士</td>\n", "      <td>西北大学</td>\n", "      <td>计算机工程硕士</td>\n", "      <td>美国</td>\n", "      <td>西安电子科技大学</td>\n", "      <td>3.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2921</th>\n", "      <td>东北大学金融硕士</td>\n", "      <td>东北大学</td>\n", "      <td>金融硕士</td>\n", "      <td>美国</td>\n", "      <td>武汉理工大学</td>\n", "      <td>3.62</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21995</th>\n", "      <td>慕尼黑工业大学亚洲分校轨道、交通与物流理学硕士</td>\n", "      <td>慕尼黑工业大学亚洲分校</td>\n", "      <td>轨道、交通与物流理学硕士</td>\n", "      <td>NaN</td>\n", "      <td>海南大学</td>\n", "      <td>3.42</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21998</th>\n", "      <td>西北大学电气工程理学硕士</td>\n", "      <td>西北大学</td>\n", "      <td>电气工程理学硕士</td>\n", "      <td>美国</td>\n", "      <td>东南大学</td>\n", "      <td>88.11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22199</th>\n", "      <td>东北大学机械工程理学硕士</td>\n", "      <td>东北大学</td>\n", "      <td>机械工程理学硕士</td>\n", "      <td>美国</td>\n", "      <td>重庆大学</td>\n", "      <td>3.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22597</th>\n", "      <td>东北大学分析学硕士</td>\n", "      <td>东北大学</td>\n", "      <td>分析学硕士</td>\n", "      <td>美国</td>\n", "      <td>伦敦大学国王学院</td>\n", "      <td>3.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22831</th>\n", "      <td>西北大学统计与数据科学理学硕士</td>\n", "      <td>西北大学</td>\n", "      <td>统计与数据科学理学硕士</td>\n", "      <td>美国</td>\n", "      <td>南京大学</td>\n", "      <td>88.2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>61 rows × 6 columns</p>\n", "</div>"], "text/plain": ["      offer_school_and_program offer_school offer_program offer_region  \\\n", "971                 东北大学信息系统硕士         东北大学        信息系统硕士           美国   \n", "998                 东北大学信息系统硕士         东北大学        信息系统硕士           美国   \n", "2395             东北大学企业与组织传播硕士         东北大学     企业与组织传播硕士           美国   \n", "2892               西北大学计算机工程硕士         西北大学       计算机工程硕士           美国   \n", "2921                  东北大学金融硕士         东北大学          金融硕士           美国   \n", "...                        ...          ...           ...          ...   \n", "21995  慕尼黑工业大学亚洲分校轨道、交通与物流理学硕士  慕尼黑工业大学亚洲分校  轨道、交通与物流理学硕士          NaN   \n", "21998             西北大学电气工程理学硕士         西北大学      电气工程理学硕士           美国   \n", "22199             东北大学机械工程理学硕士         东北大学      机械工程理学硕士           美国   \n", "22597                东北大学分析学硕士         东北大学         分析学硕士           美国   \n", "22831          西北大学统计与数据科学理学硕士         西北大学   统计与数据科学理学硕士           美国   \n", "\n", "      undergraduate_school    gpa  \n", "971                 电子科技大学  81.63  \n", "998                 华东理工大学   3.35  \n", "2395                湖北美术学院   3.19  \n", "2892              西安电子科技大学    3.8  \n", "2921                武汉理工大学   3.62  \n", "...                    ...    ...  \n", "21995                 海南大学   3.42  \n", "21998                 东南大学  88.11  \n", "22199                 重庆大学   3.65  \n", "22597             伦敦大学国王学院    3.1  \n", "22831                 南京大学   88.2  \n", "\n", "[61 rows x 6 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["missing_schools_cases_df = case_df[case_df['offer_school'].isin(missing_schools)][['offer_school_and_program', 'offer_school', 'offer_program','offer_region','undergraduate_school','gpa']]\n", "# missing_schools_cases_df.to_csv('学校名未在专业数据库中出现的案例（用于手动核查修改学校名称）.csv', index=False, encoding='utf-8-sig')\n", "missing_schools_cases_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["检查后发现就先改：东北大学（美国） 西北大学（美国）这俩吧，改了后直接61->15"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# 替换 offer_school 列中的特定值\n", "case_df['offer_school'] = case_df['offer_school'].replace({\n", "    '东北大学': '东北大学（美国）',\n", "    '西北大学': '西北大学（美国）'\n", "})"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["在案例数据中但不在专业数据中的学校名称共：11个，具体名称如下：\n", "密歇根大学\n", "慕尼黑工业大学亚洲分校\n", "阿伯泰大学\n", "亚利桑那大学\n", "提赛德大学\n", "创意艺术大学\n", "克拉克大学\n", "纽约州立大学石溪分校\n", "诺丁汉特伦特大学\n", "诺森比亚大学\n", "伦敦艺术大学\n", "=======================================================\n", "\n", "学校名在案例数据中但不在专业数据中的案例条数：15\n"]}], "source": ["# 获取唯一的学校和专业名称\n", "case_schools_unique = set(case_df['offer_school'].unique()) #145个学校名\n", "program_schools_unique = set(program_df['学校中文名'].unique()) # 170个学校名\n", "\n", "# 核查学校名称\n", "missing_schools = case_schools_unique - program_schools_unique\n", "if missing_schools:\n", "    print(f\"在案例数据中但不在专业数据中的学校名称共：{len(missing_schools)}个，具体名称如下：\")\n", "    for school in missing_schools:\n", "        print(school)\n", "else:\n", "    print(\"所有案例数据中的学校名称都在专业数据中找到。\")\n", "\n", "print(\"=======================================================\")\n", "print(f'\\n学校名在案例数据中但不在专业数据中的案例条数：{len(case_df[case_df['offer_school'].isin(missing_schools)])}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3.4 学校及专业名匹配"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>offer_school_and_program</th>\n", "      <th>student_name</th>\n", "      <th>undergraduate_school</th>\n", "      <th>undergraduate_major</th>\n", "      <th>gpa</th>\n", "      <th>offer_region</th>\n", "      <th>offer_degree</th>\n", "      <th>offer_major_direction</th>\n", "      <th>language_score</th>\n", "      <th>key_experiences</th>\n", "      <th>undergraduate_school_tier</th>\n", "      <th>offer_school</th>\n", "      <th>offer_program</th>\n", "      <th>offer_program_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>香港理工大学微电子技术与材料理学硕士</td>\n", "      <td>L同学</td>\n", "      <td>南方科技大学</td>\n", "      <td>微电子科学与工程</td>\n", "      <td>82.6</td>\n", "      <td>香港</td>\n", "      <td>硕士</td>\n", "      <td>微电子技术与材料</td>\n", "      <td>雅思6.0</td>\n", "      <td>富士康科技集团研发部门实习生、二级运算放大器设计、采用溶胶-凝胶法制备负载NiO的TiO2复...</td>\n", "      <td>tier3</td>\n", "      <td>香港理工大学</td>\n", "      <td>微电子技术与材料理学硕士</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  offer_school_and_program student_name undergraduate_school  \\\n", "0       香港理工大学微电子技术与材料理学硕士          L同学               南方科技大学   \n", "\n", "  undergraduate_major   gpa offer_region offer_degree offer_major_direction  \\\n", "0            微电子科学与工程  82.6           香港           硕士              微电子技术与材料   \n", "\n", "  language_score                                    key_experiences  \\\n", "0          雅思6.0  富士康科技集团研发部门实习生、二级运算放大器设计、采用溶胶-凝胶法制备负载NiO的TiO2复...   \n", "\n", "  undergraduate_school_tier offer_school offer_program  offer_program_id  \n", "0                     tier3       香港理工大学  微电子技术与材料理学硕士               NaN  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["case_df.head(1)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校中文名</th>\n", "      <th>学校英文名</th>\n", "      <th>专业中文名</th>\n", "      <th>专业英文名</th>\n", "      <th>专业大类</th>\n", "      <th>专业方向</th>\n", "      <th>所在学院</th>\n", "      <th>入学时间</th>\n", "      <th>项目时长</th>\n", "      <th>项目官网</th>\n", "      <th>...</th>\n", "      <th>课程设置</th>\n", "      <th>专业代码</th>\n", "      <th>项目学费</th>\n", "      <th>学校英文名(QS25)</th>\n", "      <th>学校排名(QS25)</th>\n", "      <th>学校所在地区</th>\n", "      <th>申请学位类型</th>\n", "      <th>绩点要求</th>\n", "      <th>年开销预估值</th>\n", "      <th>留服认证</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>香港科技大学</td>\n", "      <td>The Hong Kong University of Science and Techno...</td>\n", "      <td>化学与能源工程理学硕士</td>\n", "      <td>MSc Chemical and Energy Engineering</td>\n", "      <td>工科</td>\n", "      <td>化工</td>\n", "      <td>工程学院</td>\n", "      <td>2/9月</td>\n", "      <td>1年</td>\n", "      <td>https://prog-crs.hkust.edu.hk/pgprog/2025-26/m...</td>\n", "      <td>...</td>\n", "      <td>该项目分为一年全日制和两年非全日制,最低需修满30学分方可毕业，基础课程最低需修满12学分，...</td>\n", "      <td>1526</td>\n", "      <td>184800港币/年</td>\n", "      <td>The Hong Kong University of Science and Techno...</td>\n", "      <td>47</td>\n", "      <td>香港</td>\n", "      <td>硕士</td>\n", "      <td>70.0</td>\n", "      <td>15-20万人民币</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 24 columns</p>\n", "</div>"], "text/plain": ["    学校中文名                                              学校英文名        专业中文名  \\\n", "0  香港科技大学  The Hong Kong University of Science and Techno...  化学与能源工程理学硕士   \n", "\n", "                                 专业英文名 专业大类 专业方向  所在学院  入学时间 项目时长  \\\n", "0  MSc Chemical and Energy Engineering   工科   化工  工程学院  2/9月   1年   \n", "\n", "                                                项目官网  ...  \\\n", "0  https://prog-crs.hkust.edu.hk/pgprog/2025-26/m...  ...   \n", "\n", "                                                课程设置  专业代码        项目学费  \\\n", "0  该项目分为一年全日制和两年非全日制,最低需修满30学分方可毕业，基础课程最低需修满12学分，...  1526  184800港币/年   \n", "\n", "                                         学校英文名(QS25) 学校排名(QS25)  学校所在地区  \\\n", "0  The Hong Kong University of Science and Techno...         47      香港   \n", "\n", "  申请学位类型  绩点要求     年开销预估值 留服认证  \n", "0     硕士  70.0  15-20万人民币  NaN  \n", "\n", "[1 rows x 24 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["program_df.head(1)\n"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["# 添加 program_id 列到 program_df，值为 index + 1\n", "program_df['program_id'] = program_df.index + 1\n", "\n", "# 合并两个数据框以匹配 offer_school 和 offer_program\n", "merged_df = pd.merge(\n", "    case_df,\n", "    program_df[['学校中文名', '专业中文名', '专业代码', 'program_id']],\n", "    left_on=['offer_school', 'offer_program'],\n", "    right_on=['学校中文名', '专业中文名'],\n", "    how='left'\n", ")\n", "\n", "# 将匹配到的 program_id 赋值给 offer_program_id 字段\n", "merged_df['offer_program_id'] = merged_df['program_id'].astype('Int64')\n", "\n", "# 将匹配到的专业代码赋值给 offer_program_code 字段\n", "merged_df['offer_program_code'] = merged_df['专业代码'].astype('Int64')\n", "\n", "# 删除多余的列\n", "merged_df.drop(columns=['学校中文名', '专业中文名', '专业代码', 'program_id'], inplace=True)\n", "\n", "\n", "#23187 -> 23893 有对应重复的...还要检测除了最后id和code外之前全部一样的...后续清理"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>offer_school_and_program</th>\n", "      <th>student_name</th>\n", "      <th>undergraduate_school</th>\n", "      <th>undergraduate_major</th>\n", "      <th>gpa</th>\n", "      <th>offer_region</th>\n", "      <th>offer_degree</th>\n", "      <th>offer_major_direction</th>\n", "      <th>language_score</th>\n", "      <th>key_experiences</th>\n", "      <th>undergraduate_school_tier</th>\n", "      <th>offer_school</th>\n", "      <th>offer_program</th>\n", "      <th>offer_program_id</th>\n", "      <th>offer_program_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>香港理工大学微电子技术与材料理学硕士</td>\n", "      <td>L同学</td>\n", "      <td>南方科技大学</td>\n", "      <td>微电子科学与工程</td>\n", "      <td>82.6</td>\n", "      <td>香港</td>\n", "      <td>硕士</td>\n", "      <td>微电子技术与材料</td>\n", "      <td>雅思6.0</td>\n", "      <td>富士康科技集团研发部门实习生、二级运算放大器设计、采用溶胶-凝胶法制备负载NiO的TiO2复...</td>\n", "      <td>tier3</td>\n", "      <td>香港理工大学</td>\n", "      <td>微电子技术与材料理学硕士</td>\n", "      <td>7800</td>\n", "      <td>72317</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  offer_school_and_program student_name undergraduate_school  \\\n", "0       香港理工大学微电子技术与材料理学硕士          L同学               南方科技大学   \n", "\n", "  undergraduate_major   gpa offer_region offer_degree offer_major_direction  \\\n", "0            微电子科学与工程  82.6           香港           硕士              微电子技术与材料   \n", "\n", "  language_score                                    key_experiences  \\\n", "0          雅思6.0  富士康科技集团研发部门实习生、二级运算放大器设计、采用溶胶-凝胶法制备负载NiO的TiO2复...   \n", "\n", "  undergraduate_school_tier offer_school offer_program  offer_program_id  \\\n", "0                     tier3       香港理工大学  微电子技术与材料理学硕士              7800   \n", "\n", "   offer_program_code  \n", "0               72317  "]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_df.head(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3.5 gpa清理与数据类型转换"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_38628\\3898901837.py:24: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  invalid_gpa_rows['extracted_gpa'] = invalid_gpa_rows['gpa'].apply(extract_first_number)\n"]}], "source": ["# 尝试将 gpa 列转换为 float64，并捕获无法转换的行\n", "def can_convert_to_float(value):\n", "    try:\n", "        float(value)\n", "        return True\n", "    except ValueError:\n", "        return False\n", "\n", "# 定义一个函数来提取第一个数字\n", "def extract_first_number(value):\n", "    import re\n", "    match = re.search(r'(\\d+(\\.\\d+)?)', value)\n", "    if match:\n", "        return match.group(1)\n", "    return pd.NA\n", "\n", "# 创建一个布尔掩码来标识哪些行可以转换为浮点数\n", "valid_gpa_mask = merged_df['gpa'].apply(can_convert_to_float)\n", "\n", "# 获取无法转换的行\n", "invalid_gpa_rows = merged_df[~valid_gpa_mask]\n", "\n", "# 提取第一个数字并创建一个新的列\n", "invalid_gpa_rows['extracted_gpa'] = invalid_gpa_rows['gpa'].apply(extract_first_number)\n", "\n", "# 将 extracted_gpa 列的值赋给 merged_df 中对应的 gpa 列\n", "merged_df.loc[~valid_gpa_mask, 'gpa'] = invalid_gpa_rows['extracted_gpa']\n", "\n", "# 将整个 gpa 列转换为 float64 类型，允许 NaN 值\n", "merged_df['gpa'] = merged_df['gpa'].astype('Float64')"]}, {"cell_type": "code", "execution_count": 95, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["总的有效条目: 23893\n", "非百分制占比: 36.37%\n", "百分制占比: 63.62%\n"]}], "source": ["# 根据 gpa 的值来区分五分制和百分制\n", "not_percentage_scale = merged_df[merged_df['gpa'] < 10]\n", "percentage_scale = merged_df[merged_df['gpa'] >= 10]\n", "\n", "# 统计五分制和百分制的占比\n", "total_valid_entries = len(merged_df)\n", "not_percentage_count = len(not_percentage_scale)\n", "percentage_count = len(percentage_scale)\n", "\n", "not_percentage_percentage = (not_percentage_count / total_valid_entries) * 100\n", "percentage_percentage = (percentage_count / total_valid_entries) * 100\n", "\n", "print(f\"总的有效条目: {total_valid_entries}\")\n", "print(f\"非百分制占比: {not_percentage_percentage:.2f}%\")\n", "print(f\"百分制占比: {percentage_percentage:.2f}%\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# invalid_gpa_rows.to_csv('invalid_gpa_rows.csv', index=False) # 人工核验数据..."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 数据最终核验与导出"]}, {"cell_type": "code", "execution_count": 96, "metadata": {}, "outputs": [{"data": {"text/plain": ["offer_school_and_program        0\n", "student_name                    0\n", "undergraduate_school            0\n", "undergraduate_major            70\n", "gpa                             3\n", "offer_region                  927\n", "offer_degree                   17\n", "offer_major_direction           1\n", "language_score               3723\n", "key_experiences                 0\n", "undergraduate_school_tier       0\n", "offer_school                    0\n", "offer_program                   0\n", "offer_program_id             2181\n", "offer_program_code           2181\n", "dtype: int64"]}, "execution_count": 96, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_df.isnull().sum()"]}, {"cell_type": "code", "execution_count": 101, "metadata": {}, "outputs": [{"data": {"text/plain": ["offer_school_and_program      object\n", "student_name                  object\n", "undergraduate_school          object\n", "undergraduate_major           object\n", "gpa                          Float64\n", "offer_region                  object\n", "offer_degree                  object\n", "offer_major_direction         object\n", "language_score                object\n", "key_experiences               object\n", "undergraduate_school_tier     object\n", "offer_school                  object\n", "offer_program                 object\n", "offer_program_id               Int64\n", "offer_program_code             Int64\n", "dtype: object"]}, "execution_count": 101, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_df.dtypes"]}, {"cell_type": "code", "execution_count": 98, "metadata": {}, "outputs": [], "source": ["\n", "# merged_df.to_csv('案例数据库.csv', index=False, encoding='utf-8-sig')"]}, {"cell_type": "code", "execution_count": 107, "metadata": {}, "outputs": [], "source": ["# 读取CSV文件(要指定int64类型，因为存在pd.NA空值，直接导入默认变为float64)\n", "df = pd.read_csv(\n", "    '案例数据库.csv',\n", "    dtype={\n", "        'offer_program_id': 'Int64',  # 使用 Int64 允许 NaN 值\n", "        'offer_program_code': 'Int64'  # 使用 Int64 允许 NaN 值\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 108, "metadata": {}, "outputs": [{"data": {"text/plain": ["offer_school_and_program      object\n", "student_name                  object\n", "undergraduate_school          object\n", "undergraduate_major           object\n", "gpa                          float64\n", "offer_region                  object\n", "offer_degree                  object\n", "offer_major_direction         object\n", "language_score                object\n", "key_experiences               object\n", "undergraduate_school_tier     object\n", "offer_school                  object\n", "offer_program                 object\n", "offer_program_id               Int64\n", "offer_program_code             Int64\n", "dtype: object"]}, "execution_count": 108, "metadata": {}, "output_type": "execute_result"}], "source": ["df.dtypes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4 Embedding信息融合"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["case_df = pd.read_csv(\n", "    '案例数据库_noembedding.csv',\n", "    dtype={\n", "        'offer_program_id': 'Int64',  # 使用 Int64 允许 NaN 值\n", "        'offer_program_code': 'Int64'  # 使用 Int64 允许 NaN 值\n", "    }\n", ")\n", "case_embedding_df = pd.read_csv('ai_selection_cases.csv')\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>offer_school_and_program</th>\n", "      <th>student_name</th>\n", "      <th>undergraduate_school</th>\n", "      <th>undergraduate_major</th>\n", "      <th>gpa</th>\n", "      <th>offer_region</th>\n", "      <th>offer_degree</th>\n", "      <th>offer_major_direction</th>\n", "      <th>language_score</th>\n", "      <th>key_experiences</th>\n", "      <th>undergraduate_school_tier</th>\n", "      <th>offer_school</th>\n", "      <th>offer_program</th>\n", "      <th>offer_program_id</th>\n", "      <th>offer_program_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>香港理工大学微电子技术与材料理学硕士</td>\n", "      <td>L同学</td>\n", "      <td>南方科技大学</td>\n", "      <td>微电子科学与工程</td>\n", "      <td>82.6</td>\n", "      <td>香港</td>\n", "      <td>硕士</td>\n", "      <td>微电子技术与材料</td>\n", "      <td>雅思6.0</td>\n", "      <td>富士康科技集团研发部门实习生、二级运算放大器设计、采用溶胶-凝胶法制备负载NiO的TiO2复...</td>\n", "      <td>tier3</td>\n", "      <td>香港理工大学</td>\n", "      <td>微电子技术与材料理学硕士</td>\n", "      <td>7800</td>\n", "      <td>72317</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  offer_school_and_program student_name undergraduate_school  \\\n", "0       香港理工大学微电子技术与材料理学硕士          L同学               南方科技大学   \n", "\n", "  undergraduate_major   gpa offer_region offer_degree offer_major_direction  \\\n", "0            微电子科学与工程  82.6           香港           硕士              微电子技术与材料   \n", "\n", "  language_score                                    key_experiences  \\\n", "0          雅思6.0  富士康科技集团研发部门实习生、二级运算放大器设计、采用溶胶-凝胶法制备负载NiO的TiO2复...   \n", "\n", "  undergraduate_school_tier offer_school offer_program  offer_program_id  \\\n", "0                     tier3       香港理工大学  微电子技术与材料理学硕士              7800   \n", "\n", "   offer_program_code  \n", "0               72317  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["case_df.head(1)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>embedding</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>4999</td>\n", "      <td>\"[-0.06330096, 0.00256234, 0.019662814, -0.025...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5000</td>\n", "      <td>\"[-0.051500764, -0.00220469, -0.011791487, -0....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5001</td>\n", "      <td>\"[-0.051204, -0.005409486, 0.018940452, -0.015...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5002</td>\n", "      <td>\"[-0.055339947, 0.012684489, -0.019916194, -0....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5003</td>\n", "      <td>\"[-0.0567301, 0.0049415687, -0.026222795, -0.0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23888</th>\n", "      <td>18749</td>\n", "      <td>\"[-0.07056309, 0.007064115, -0.0003829648, -0....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23889</th>\n", "      <td>18750</td>\n", "      <td>\"[-0.057644736, 0.013882513, -0.0033604885, -0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23890</th>\n", "      <td>18751</td>\n", "      <td>\"[-0.085654244, -0.014472342, 0.017982278, -0....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23891</th>\n", "      <td>18753</td>\n", "      <td>\"[-0.080895096, 0.021201713, 0.00068883563, -0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23892</th>\n", "      <td>19471</td>\n", "      <td>\"[-0.085275166, -0.021881856, -0.018163688, -0...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>23893 rows × 2 columns</p>\n", "</div>"], "text/plain": ["          id                                          embedding\n", "0       4999  \"[-0.06330096, 0.00256234, 0.019662814, -0.025...\n", "1       5000  \"[-0.051500764, -0.00220469, -0.011791487, -0....\n", "2       5001  \"[-0.051204, -0.005409486, 0.018940452, -0.015...\n", "3       5002  \"[-0.055339947, 0.012684489, -0.019916194, -0....\n", "4       5003  \"[-0.0567301, 0.0049415687, -0.026222795, -0.0...\n", "...      ...                                                ...\n", "23888  18749  \"[-0.07056309, 0.007064115, -0.0003829648, -0....\n", "23889  18750  \"[-0.057644736, 0.013882513, -0.0033604885, -0...\n", "23890  18751  \"[-0.085654244, -0.014472342, 0.017982278, -0....\n", "23891  18753  \"[-0.080895096, 0.021201713, 0.00068883563, -0...\n", "23892  19471  \"[-0.085275166, -0.021881856, -0.018163688, -0...\n", "\n", "[23893 rows x 2 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["case_embedding_df"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# 去掉 embedding 列中的双引号\n", "case_embedding_df['embedding'] = case_embedding_df['embedding'].str.strip('\"')\n", "\n", "# 调整 case_embedding_df 的 id 列以匹配 program_df 的索引\n", "case_embedding_df['index'] = case_embedding_df['id'] - 1\n", "\n", "# 设置 program_df 的索引列为 'index'\n", "case_df.index.name = 'index'\n", "\n", "# 合并两个 DataFrame\n", "merged_df = pd.merge(case_df.reset_index(), case_embedding_df[['index', 'embedding']], on='index', how='left')\n", "\n", "# 删除临时添加的 index 列\n", "merged_df.drop(columns=['index'], inplace=True)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>offer_school_and_program</th>\n", "      <th>student_name</th>\n", "      <th>undergraduate_school</th>\n", "      <th>undergraduate_major</th>\n", "      <th>gpa</th>\n", "      <th>offer_region</th>\n", "      <th>offer_degree</th>\n", "      <th>offer_major_direction</th>\n", "      <th>language_score</th>\n", "      <th>key_experiences</th>\n", "      <th>undergraduate_school_tier</th>\n", "      <th>offer_school</th>\n", "      <th>offer_program</th>\n", "      <th>offer_program_id</th>\n", "      <th>offer_program_code</th>\n", "      <th>embedding</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>香港理工大学微电子技术与材料理学硕士</td>\n", "      <td>L同学</td>\n", "      <td>南方科技大学</td>\n", "      <td>微电子科学与工程</td>\n", "      <td>82.6</td>\n", "      <td>香港</td>\n", "      <td>硕士</td>\n", "      <td>微电子技术与材料</td>\n", "      <td>雅思6.0</td>\n", "      <td>富士康科技集团研发部门实习生、二级运算放大器设计、采用溶胶-凝胶法制备负载NiO的TiO2复...</td>\n", "      <td>tier3</td>\n", "      <td>香港理工大学</td>\n", "      <td>微电子技术与材料理学硕士</td>\n", "      <td>7800</td>\n", "      <td>72317</td>\n", "      <td>[-0.040704522, 0.01515306, 3.7523274e-05, -0.0...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  offer_school_and_program student_name undergraduate_school  \\\n", "0       香港理工大学微电子技术与材料理学硕士          L同学               南方科技大学   \n", "\n", "  undergraduate_major   gpa offer_region offer_degree offer_major_direction  \\\n", "0            微电子科学与工程  82.6           香港           硕士              微电子技术与材料   \n", "\n", "  language_score                                    key_experiences  \\\n", "0          雅思6.0  富士康科技集团研发部门实习生、二级运算放大器设计、采用溶胶-凝胶法制备负载NiO的TiO2复...   \n", "\n", "  undergraduate_school_tier offer_school offer_program  offer_program_id  \\\n", "0                     tier3       香港理工大学  微电子技术与材料理学硕士              7800   \n", "\n", "   offer_program_code                                          embedding  \n", "0               72317  [-0.040704522, 0.01515306, 3.7523274e-05, -0.0...  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_df.head(1)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["merged_df.to_csv(\"案例数据库.csv\", index=False, encoding='utf-8-sig')"]}], "metadata": {"kernelspec": {"display_name": "tunshu_data", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}