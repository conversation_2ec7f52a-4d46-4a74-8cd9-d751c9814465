"""AI Detection API Endpoints - AI检测API端点"""
from fastapi import APIRouter, status, HTTPException
from typing import Dict, Any

from app.ai_detection.schemas.detection import (
    DetectionRequest,
    DetectionResponse,
    BatchDetectionRequest,
    BatchDetectionResponse,
    DetectionStatistics,
    ErrorResponse
)
from app.ai_detection.core.detector import (
    detect_ai_content,
    detect_ai_content_batch,
    calculate_batch_statistics,
    get_detection_health_check
)

router = APIRouter()

@router.post(
    "/detect",
    response_model=DetectionResponse,
    status_code=status.HTTP_200_OK,
    summary="AI内容检测",
    description="检测文本中AI生成内容的比例，返回详细的分析结果包括风险等级和高亮句子",
    responses={
        200: {"description": "检测成功", "model": DetectionResponse},
        400: {"description": "请求参数错误", "model": ErrorResponse},
        502: {"description": "外部API服务不可用", "model": ErrorResponse},
        500: {"description": "服务器内部错误", "model": ErrorResponse}
    }
)
async def detect_ai_text(request: DetectionRequest) -> DetectionResponse:
    """
    检测单个文本的AI生成内容
    
    **功能说明：**
    - 检测文本中AI生成内容的百分比
    - 标识具体的AI生成句子
    - 评估文本的风险等级（低/中/高/极高）
    - 提供详细的统计信息
    
    **使用场景：**
    - 学术论文原创性检查
    - 文书作品真实性验证
    - 内容创作质量评估
    
    **注意事项：**
    - 文本长度限制：10-50000字符
    - 支持中英文混合文本
    - 长文本会自动分块处理
    """
    return await detect_ai_content(request.text)

@router.post(
    "/detect-batch",
    response_model=BatchDetectionResponse,
    status_code=status.HTTP_200_OK,
    summary="批量AI内容检测",
    description="同时检测多个文本的AI生成内容，支持最多10个文本的并发检测",
    responses={
        200: {"description": "批量检测成功", "model": BatchDetectionResponse},
        400: {"description": "请求参数错误", "model": ErrorResponse},
        502: {"description": "外部API服务不可用", "model": ErrorResponse},
        500: {"description": "服务器内部错误", "model": ErrorResponse}
    }
)
async def detect_ai_text_batch(request: BatchDetectionRequest) -> BatchDetectionResponse:
    """
    批量检测多个文本的AI生成内容
    
    **功能说明：**
    - 并发检测多个文本（最多10个）
    - 单独返回每个文本的检测结果
    - 提供批量检测的汇总统计
    - 失败的检测会标记错误信息
    
    **适用场景：**
    - 批量文档检查
    - 多份作业同时验证
    - 大量内容快速筛查
    
    **性能优化：**
    - 采用并发检测提升效率
    - 单个文本失败不影响其他文本
    - 自动统计成功/失败数量
    """
    return await detect_ai_content_batch(request.texts)

@router.post(
    "/analyze-batch",
    response_model=DetectionStatistics,
    status_code=status.HTTP_200_OK,
    summary="批量检测统计分析",
    description="对批量检测结果进行统计分析，返回汇总数据",
    responses={
        200: {"description": "统计分析成功", "model": DetectionStatistics},
        400: {"description": "请求参数错误", "model": ErrorResponse}
    }
)
async def analyze_batch_detection(request: BatchDetectionRequest) -> Dict[str, Any]:
    """
    批量检测并返回统计分析
    
    **功能说明：**
    - 执行批量检测
    - 计算平均AI百分比
    - 统计风险等级分布
    - 汇总词数和AI词数
    
    **返回数据：**
    - 检测统计信息
    - 详细的批量检测结果
    - 处理时间和性能数据
    """
    # 执行批量检测
    batch_response = await detect_ai_content_batch(request.texts)
    
    # 计算统计信息
    statistics = calculate_batch_statistics(batch_response.results)
    
    return {
        "statistics": statistics,
        "batch_results": batch_response,
        "summary": {
            "total_texts": batch_response.total_texts,
            "success_rate": round(
                (batch_response.successful_detections / batch_response.total_texts * 100), 2
            ) if batch_response.total_texts > 0 else 0.0,
            "processing_time": batch_response.processing_time
        }
    }

@router.get(
    "/health",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="服务健康检查",
    description="检查AI检测服务的运行状态和API连通性",
    responses={
        200: {"description": "服务正常"},
        503: {"description": "服务不可用"}
    }
)
async def health_check() -> Dict[str, Any]:
    """
    AI检测服务健康检查
    
    **检查内容：**
    - ZeroGPT API连通性
    - 服务响应时间
    - 系统状态信息
    
    **返回信息：**
    - 服务状态（healthy/unhealthy）
    - API可访问性
    - 响应时间统计
    - 错误信息（如有）
    """
    health_status = await get_detection_health_check()
    
    # 根据健康状态设置HTTP状态码
    if health_status["status"] == "unhealthy":
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=health_status
        )
    
    return health_status

@router.get(
    "/config",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="获取检测配置信息",
    description="返回当前AI检测服务的配置参数和限制信息"
)
async def get_detection_config() -> Dict[str, Any]:
    """
    获取AI检测服务配置信息
    
    **配置信息：**
    - 文本长度限制
    - 批量检测限制
    - 风险阈值设置
    - API超时配置
    """
    from app.ai_detection.config import (
        MAX_TEXT_LENGTH,
        CHUNK_SIZE,
        DEFAULT_TIMEOUT,
        AI_PERCENTAGE_THRESHOLD
    )
    
    return {
        "service_name": "AI Detection Service",
        "version": "1.0.0",
        "provider": "ZeroGPT",
        "limits": {
            "max_text_length": MAX_TEXT_LENGTH,
            "chunk_size": CHUNK_SIZE,
            "max_batch_size": 10,
            "api_timeout": DEFAULT_TIMEOUT
        },
        "risk_thresholds": AI_PERCENTAGE_THRESHOLD,
        "supported_features": [
            "single_text_detection",
            "batch_detection",
            "risk_level_assessment",
            "highlighted_sentences",
            "text_statistics",
            "health_monitoring"
        ]
    }

@router.get(
    "/",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="API信息",
    description="返回AI检测API的基本信息和使用说明"
)
async def get_api_info() -> Dict[str, Any]:
    """
    AI检测API基本信息
    
    **API说明：**
    - 服务简介
    - 主要功能
    - 使用指南
    - 联系信息
    """
    return {
        "service": "AI Detection API",
        "description": "基于ZeroGPT的AI文本检测服务，用于识别AI生成的内容",
        "version": "1.0.0",
        "endpoints": {
            "POST /detect": "单文本AI检测",
            "POST /detect-batch": "批量文本AI检测",
            "POST /analyze-batch": "批量检测统计分析",
            "GET /health": "服务健康检查",
            "GET /config": "获取配置信息",
            "GET /": "API信息"
        },
        "usage_guide": {
            "single_detection": "发送POST请求到/detect，包含text字段",
            "batch_detection": "发送POST请求到/detect-batch，包含texts数组",
            "health_check": "发送GET请求到/health检查服务状态"
        },
        "support": {
            "documentation": "/docs",
            "openapi_spec": "/openapi.json"
        }
    } 