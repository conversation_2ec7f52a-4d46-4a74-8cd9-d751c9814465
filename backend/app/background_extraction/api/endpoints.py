from fastapi import APIRouter, Depends, File, UploadFile, status, HTTPException
from typing import Dict, Any

from app.core.dependencies import DBSession
from app.background_extraction.core.extractor import extract_and_save_profile

router = APIRouter()

@router.post(
    "/extract-from-file",
    summary="从文件提取客户信息并创建档案",
    description="上传一个文件（如PDF, DOCX, TXT简历），系统将自动解析内容，通过AI提取结构化信息，并创建新的客户档案。",
    status_code=status.HTTP_201_CREATED
)
async def extract_from_file(
    db: DBSession,
    file: UploadFile = File(..., description="要上传的客户简历或背景文件。")
) -> Dict[str, Any]:
    """
    接收上传的文件，调用核心逻辑进行解析和处理，然后返回结果。
    这个端点取代了之前依赖Dify的多步骤流程。
    """
    if not file:
        raise HTTPException(status_code=400, detail="未提供文件。")
    
    # 调用核心逻辑函数处理文件
    # 该函数内部包含了文件解析、LLM调用、数据库保存和异常处理
    extracted_data = await extract_and_save_profile(db, file)
    
    # 返回从LLM提取并补充了client_id的完整数据
    return extracted_data
