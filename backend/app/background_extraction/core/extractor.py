from fastapi import UploadFile, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
import logging
import json_repair
import os
from datetime import datetime

from app.background_extraction.utils.file_parser import parse_file
from app.background_extraction.utils.llm import process_text_with_api_async
from app.models.client import (
    Client, Education, Academic, Work, Activity, Award, Skill, LanguageScore, Thought
)

logger = logging.getLogger(__name__)

USER_PROVIDED_PROMPT_TEMPLATE = """
## 任务
请从提供的文档中提取客户完整信息，并按JSON格式返回，JSON中需要包括如下字段的内容。相关字段如果在原始文档中有详细描述，完整提取详细描述即可，不必省略概括。如果在文档中找不到相关字段的内容，请返回null或空数组：
    1. 基本信息：
       - name: 客户姓名（必填）
       - gender: 性别（male/female/other）
       - phone: 电话号码
       - email: 电子邮箱
       - location: 所在城市
       - address: 详细地址
       - id_card: 身份证号码
       - passport: 护照号码
       - id_card_issuer: 身份证签发机构
       - id_card_validity: 身份证有效期
       - passport_issue_place: 护照签发地
       - passport_issue_date: 护照签发日期
       - passport_expiry: 护照过期日期
       - service_type: 服务类型（默认为"undergraduate"）
    2. 教育经历（education数组）：【重要】文档中可能包含多条教育经历，请全部提取并放入数组中。每项包含：
       - school: 学校名称（必填）
       - degree: 学位
       - major: 专业
       - start_date: 开始日期
       - end_date: 结束日期
       - gpa: GPA成绩
       - description: 描述
    3. 学术经历（academic数组）：【重要】文档中可能包含多条学术经历，请全部提取并放入数组中。每项包含以下内容，注意从文档中提取"description"字段的学术经历描述时要做到详细完整，不要进行省略概括：
       - title: 学术项目标题（必填）
       - type: 项目类型：毕业论文(设计）、科研项目、学科课程项目、大学生创业项目、其他等
       - date: 日期
       - description: 详细学术经历描述
    4. 工作经历（work数组）：【重要】文档中可能包含多条工作经历，请全部提取并放入数组中。每项包含以下内容，注意从文档中提取"description"字段的工作经历描述时要做到详细完整，不要进行省略概括：
       - company: 公司/单位名称（必填）
       - position: 职位
       - start_date: 开始日期
       - end_date: 结束日期
       - description: 详细工作经历描述
    5. 活动经历（activities数组）：【重要】文档中可能包含多条活动经历，请全部提取并放入数组中。每项包含以下内容，注意从文档中提取"description"字段的活动经历描述时要做到详细完整，不要进行省略概括：
       - name: 活动名称（必填）
       - role: 担任角色
       - start_date: 开始日期
       - end_date: 结束日期
       - description: 详细活动经历描述
    6. 奖项（awards数组）：【重要】文档中可能包含多个奖项，请全部提取并放入数组中。每项包含：
       - name: 奖项名称（必填）
       - level: 奖项级别
       - date: 获奖日期
       - description: 奖项描述
    7. 技能（skills数组）：【重要】文档中可能包含多项技能，请全部提取并放入数组中。每项包含：
       - type : 技能类型：如'专业技能'、'综合技能'等"
       - description: 技能描述
    8. 语言成绩（language_scores数组）：【重要】文档中可能包含多项语言成绩，请全部提取并放入数组中。每项包含：
       - type: 考试类型（toefl/ielts/gre/gmat等）（必填）
       - score: 分数（必填）
       - date: 考试日期
       - validity: 有效期

请确保提取的信息准确、完整，并严格按照上述JSON结构返回。即使只有一条记录，也要放在相应的数组中。日期格式统一使用YYYY-MM-DD。返回的JSON结构示例如下，注意检查JSON格式完整性，保证括号闭合：
'''
{{
  "name": "张三",
  "gender": "male",
  "phone": "13800138000",
  "email": "<EMAIL>",
  "location": "北京",
  "address": "北京市海淀区中关村大街1号",
  "id_card": "110101199001011234",
  "passport": "E12345678",
  "id_card_issuer": "北京市公安局海淀分局",
  "id_card_validity": "2020-01-01至2030-01-01",
  "passport_issue_place": "北京",
  "passport_issue_date": "2020-01-15",
  "passport_expiry": "2030-01-15",
  "service_type": "undergraduate",
  "education": [
    {{
      "school": "北京市第一中学",
      "major": "理科实验班",
      "degree": "高中",
      "gpa": "3.8/4.0",
      "start_date": "2018-09-01",
      "end_date": "2021-06-30",
      "description": "如：高中阶段学习成绩优异，多次获得学校奖学金，担任班长职务...."
    }},
    {{
      其他教育经历...
    }}
  ],
  "academic": [
    {{
      "title": "环保材料研究小组",
      "type": "科研项目",
      "date": "2020-10-20",
      "description": "参与学校环保材料研究小组，研究可降解塑料替代品，并在校科技节展示成果...(此处仅作格式展示，实际提取时需要详细描述）"
    }},
    {{
      其他学术经历...
    }}
  ],
  "work": [
    {{
      "company": "北京科技有限公司",
      "position": "实习生",
      "start_date": "2020-07-15",
      "end_date": "2020-08-30",
      "description": "负责公司网站内容更新和社交媒体运营，学习了基本的网站维护和内容创作技能...(此处仅作格式展示，实际提取时需要详细描述）"
    }},
    {{
      其他工作经历...
    }}   
  ],
  "activities": [
    {{
      "name": "学生会",
      "role": "文艺部副部长",
      "start_date": "2019-09-01",
      "end_date": "2021-06-30",
      "description": "负责组织校园文化活动，包括元旦晚会、校园歌手大赛等，提升了组织和协调能力...(此处仅作格式展示，实际提取时需要详细描述）"
    }},
    {{
      其他活动经历...
    }}
  ],
  "awards": [
    {{
      "name": "全国高中生物理竞赛",
      "level": "省级二等奖",
      "date": "2020-05-20",
      "description": "在全国高中生物理竞赛中表现优异，获得省级二等奖。"
    }},
    {{
      其他奖项...
    }}
  ],
  "skills": [
    {{
      "type": "专业技能",
      "description": "熟练掌握Python、R、MATLAB、SQL等编程语言"
    }},
    {{
      "type": "专业技能",
      "description": "熟练使用Office办公软件Word、Excel、PowerPoint等"
    }},
    {{
      其他技能...
    }}
  ],
  "language_scores": [
    {{
      "type": "雅思",
      "score": "7.0",
      "date": "2021-05-20",
      "validity": "2023-05-20"
    }},
    {{
      其他语言成绩...
    }}
  ]
}}
'''

## 文档
{resume_text}
"""

async def extract_and_save_profile(db: AsyncSession, file: UploadFile) -> dict:
    """
    核心业务逻辑：解析文件，调用LLM提取信息，并将结构化数据存入数据库。
    """
    # 1. 解析文件内容
    try:
        resume_text = await parse_file(file)
        if not resume_text.strip():
            raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail="文件内容为空或无法解析。")
        
        # # 保存解析结果到txt文件以供查看
        # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        # original_filename = os.path.splitext(file.filename)[0] if file.filename else "unknown"
        # output_filename = f"parsed_resume_{original_filename}_{timestamp}.txt"
        # output_path = os.path.join(os.path.dirname(__file__), output_filename)
        
        # try:
        #     with open(output_path, 'w', encoding='utf-8') as f:
        #         f.write(f"原始文件名: {file.filename}\n")
        #         f.write(f"解析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        #         f.write(f"{'='*50}\n\n")
        #         f.write(resume_text)
        #     logger.info(f"解析结果已保存到: {output_path}")
        # except Exception as save_e:
        #     logger.warning(f"保存解析结果文件失败: {save_e}")
            
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    
    # 2. 调用LLM提取信息
    prompt = USER_PROVIDED_PROMPT_TEMPLATE.format(resume_text=resume_text)
    
    logger.info("正在调用LLM进行信息提取...")
    llm_response_str = await process_text_with_api_async(prompt)
    
    if llm_response_str is None:
        raise HTTPException(status_code=status.HTTP_504_GATEWAY_TIMEOUT, detail="调用LLM服务超时或失败。")

    # 3. 解析和修复JSON
    try:
        logger.info("正在解析LLM返回的JSON数据...")
        client_data = json_repair.loads(llm_response_str)
        if not isinstance(client_data, dict):
             # 兼容返回列表的情况
            if isinstance(client_data, list) and client_data:
                client_data = client_data[0]
            else:
                raise ValueError("LLM返回的顶层结构不是一个有效的JSON对象或列表。")
    except Exception as e:
        logger.error(f"无法解析LLM返回的数据: {e}\n原始数据: {llm_response_str}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="无法解析AI模型返回的数据。")

    # 4. 将提取的数据存入数据库
    try:
        logger.info("正在将提取的数据存入数据库...")
        # 创建客户基本信息
        client_base_data = {
            "name": client_data.get("name", "未命名客户"),
            "gender": client_data.get("gender"), "phone": client_data.get("phone"),
            "email": client_data.get("email"), "location": client_data.get("location"),
            "address": client_data.get("address"), "id_card": client_data.get("id_card"),
            "passport": client_data.get("passport"), "id_card_issuer": client_data.get("id_card_issuer"),
            "id_card_validity": client_data.get("id_card_validity"),
            "passport_issue_place": client_data.get("passport_issue_place"),
            "passport_issue_date": client_data.get("passport_issue_date"),
            "passport_expiry": client_data.get("passport_expiry"),
            "service_type": client_data.get("service_type", "undergraduate"),
            "is_archived": False
        }
        client = Client(**client_base_data)
        db.add(client)
        await db.flush() # 使用flush来获取client.id

        # 批量保存关联数据
        records_to_add = []
        if education_list := client_data.get("education", []):
            for edu_data in education_list:
                records_to_add.append(Education(client_id=client.id, **edu_data))
        
        if academic_list := client_data.get("academic", []):
            for acad_data in academic_list:
                records_to_add.append(Academic(client_id=client.id, **acad_data))

        if work_list := client_data.get("work", []):
            for work_data in work_list:
                records_to_add.append(Work(client_id=client.id, **work_data))

        if activity_list := client_data.get("activities", []):
            for act_data in activity_list:
                records_to_add.append(Activity(client_id=client.id, **act_data))

        if award_list := client_data.get("awards", []):
            for award_data in award_list:
                records_to_add.append(Award(client_id=client.id, **award_data))

        if skill_list := client_data.get("skills", []):
            for skill_data in skill_list:
                records_to_add.append(Skill(client_id=client.id, **skill_data))

        if language_score_list := client_data.get("language_scores", []):
            for ls_data in language_score_list:
                records_to_add.append(LanguageScore(client_id=client.id, **ls_data))

        if thoughts_list := client_data.get("thoughts", []):
            for thought_data in thoughts_list:
                records_to_add.append(Thought(client_id=client.id, **thought_data))

        db.add_all(records_to_add)
        await db.commit()
        await db.refresh(client)
        
        logger.info(f"成功创建新客户，ID: {client.id}, Hashed ID: {client.id_hashed}")
        
        # 5. 返回结果
        # 为了保持和旧API一致，返回提取的原始数据，并附加上客户的哈希ID
        client_data["client_id"] = client.id_hashed
        return client_data

    except Exception as e:
        await db.rollback()
        logger.error(f"数据保存失败: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"保存提取数据时发生内部错误: {str(e)}")
