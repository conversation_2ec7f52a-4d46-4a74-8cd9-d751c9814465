from fastapi import FastAPI, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
import time
import logging
import sys
from contextlib import asynccontextmanager

from app.core.config import settings
from app.core.cache import close_cache
from app.api import auth, dashboard, clients
from app.ai_selection.api.router import api_router as ai_selection_router
from app.background_extraction.api.router import api_router as background_extraction_router
from app.ai_augmentation.api.router import api_router as ai_augmentation_router
from app.ai_writing.api.router import api_router as ai_writing_router
from app.ai_detection.api.router import router as ai_detection_router

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger("api")

# 记录路由信息
logger.info("正在初始化API服务...")

# 生命周期管理器
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时
    logging.info("应用启动中...")
    yield
    # 关闭时
    logging.info("应用关闭中，清理资源...")
    await close_cache()

# 创建应用实例并添加中间件
app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.PROJECT_VERSION,
    description="囤鼠科技教育平台API",
    lifespan=lifespan
)

# 注意：GZIP压缩中间件会导致流式响应（SSE）被缓冲，影响实时性
# 为了确保流式输出的实时性，我们暂时禁用GZIP压缩
# 如果需要压缩，可以在Nginx等反向代理层面处理非流式响应的压缩
# app.add_middleware(
#     GZipMiddleware,
#     minimum_size=1000,  # 只压缩大于1KB的响应
#     compresslevel=6     # 压缩级别（1-9，6是平衡点）
# )

# 添加性能监控中间件
@app.middleware("http")
async def performance_monitoring_middleware(request, call_next):
    """性能监控中间件，记录请求处理时间"""
    start_time = time.time()
    
    # 处理请求
    response = await call_next(request)
    
    # 计算处理时间
    process_time = time.time() - start_time
    
    # 添加响应头
    response.headers["X-Process-Time"] = str(process_time)
    
    # 记录慢查询（超过2秒的请求）
    if process_time > 2.0:
        logging.warning(
            f"慢请求检测: {request.method} {request.url.path} "
            f"处理时间: {process_time:.2f}秒"
        )
    
    return response

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:5173",
        "http://127.0.0.1:5173",
        "null"
    ],  # 明确指定允许的前端域名
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["X-Process-Time"]  # 暴露性能头
)

# 记录路由注册信息
logger.info("正在注册API路由...")

# 包含各模块的路由
app.include_router(auth.router, prefix="/api")
app.include_router(dashboard.router, prefix="/api")
app.include_router(clients.router, prefix="/api")

# 包含AI选校系统路由
app.include_router(ai_selection_router, prefix="/api/ai-selection")

# 包含背景提取模块路由
app.include_router(background_extraction_router, prefix="/api/background-extraction")

# 包含AI增强模块路由
app.include_router(ai_augmentation_router, prefix="/api/ai-augmentation")

# 包含AI文书写作模块路由
app.include_router(ai_writing_router, prefix="/api/ai-writing")

# 包含AI检测模块路由
app.include_router(ai_detection_router, prefix="/api")

# 记录路由注册完成
logger.info("路由注册完成，应用启动")

# 根路由
@app.get("/")
async def root():
    """
    根路由，用于测试 API 是否正常运行
    """
    return {"message": "囤鼠科技教育平台 API 服务正在运行"}

# 测试路由
@app.get("/api/test")
async def test():
    """
    测试路由，用于测试 API 是否能正常访问
    """
    return {
        "message": "API 运行正常!",
        "status": "success"
    } 