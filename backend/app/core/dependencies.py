from typing import Annotated, Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import jwt, JWTError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from pydantic import ValidationError
from datetime import datetime

from app.db.database import get_db
from app.models.user import User
from app.schemas.user import TokenPayload
from app.core.config import settings
from app.core.cache import cache, cache_user_data

# OAuth2 密码令牌URL
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="/api/auth/login"
)

# 定义依赖类型
DBSession = Annotated[AsyncSession, Depends(get_db)]
TokenDep = Annotated[str, Depends(oauth2_scheme)]

@cache_user_data(ttl=300)  # 缓存5分钟
async def get_current_user(
    db: DBSession,
    token: str = Depends(oauth2_scheme)
) -> User:
    """
    获取当前用户信息 - 添加缓存优化
    
    Args:
        db: 数据库会话
        token: JWT 令牌
        
    Returns:
        User: 当前用户对象
        
    Raises:
        HTTPException: 凭证无效或用户不存在
    """
    try:
        # 解码 JWT 令牌
        payload = jwt.decode(
            token, 
            settings.SECRET_KEY, 
            algorithms=[settings.ALGORITHM]
        )
        token_data = TokenPayload(**payload)
        
        # 检查令牌是否过期
        if token_data.exp is None or datetime.fromtimestamp(token_data.exp) < datetime.now():
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌已过期",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except (JWTError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无法验证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 检查 sub 是否存在
    if token_data.sub is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 尝试从缓存获取用户信息
    cache_key = f"user:{token_data.sub}"
    cached_user = await cache.get(cache_key)
    
    if cached_user is not None:
        # 验证缓存的用户状态
        if not cached_user.get('is_active', False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, 
                detail="账户已被禁用"
            )
        # 重建User对象
        user = User(
            id=cached_user['id'],
            username=cached_user['username'],
            email=cached_user['email'],
            nickname=cached_user.get('nickname'),
            role=cached_user.get('role', 'user'),
            is_active=cached_user['is_active']
        )
        # 设置其他属性
        user.created_at = cached_user.get('created_at')
        user.updated_at = cached_user.get('updated_at')
        user.last_login = cached_user.get('last_login')
        return user
    
    # 从数据库获取用户
    result = await db.execute(select(User).where(User.id == token_data.sub))
    user = result.scalars().first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="账户已被禁用"
        )
    
    # 缓存用户信息（缓存5分钟）
    user_cache_data = {
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'nickname': user.nickname,
        'role': user.role,
        'is_active': user.is_active,
        'created_at': user.created_at,
        'updated_at': user.updated_at,
        'last_login': user.last_login
    }
    await cache.set(cache_key, user_cache_data, ttl=300)
    
    return user

# 添加依赖类型注解
CurrentUser = Annotated[User, Depends(get_current_user)] 