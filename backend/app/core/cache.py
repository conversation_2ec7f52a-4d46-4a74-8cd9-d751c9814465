import asyncio
import time
import json
import logging
from typing import Any, Dict, Optional, Callable, Union
from functools import wraps
import hashlib

try:
    import aioredis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

from app.core.config import settings

logger = logging.getLogger(__name__)

class RedisCache:
    """
    Redis分布式缓存实现，支持异步操作和自动序列化
    """
    
    def __init__(self, redis_url: str, default_ttl: int = 300):
        self.redis_url = redis_url
        self._default_ttl = default_ttl
        self._redis: Optional[aioredis.Redis] = None
        self._connection_pool: Optional[aioredis.ConnectionPool] = None
    
    async def _ensure_connection(self) -> aioredis.Redis:
        """确保Redis连接已建立"""
        if self._redis is None or self._redis.connection_pool.connection_kwargs.get('host') is None:
            try:
                # 创建连接池以提高性能
                self._connection_pool = aioredis.ConnectionPool.from_url(
                    self.redis_url,
                    max_connections=20,
                    retry_on_timeout=True,
                    socket_keepalive=True,
                    socket_keepalive_options={},
                    health_check_interval=30
                )
                self._redis = aioredis.Redis(connection_pool=self._connection_pool)
                # 测试连接
                await self._redis.ping()
                logger.info("Redis连接已建立")
            except Exception as e:
                logger.error(f"Redis连接失败: {e}")
                # 降级到内存缓存
                return None
        return self._redis
    
    def _generate_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """生成缓存键"""
        key_data = {
            'func': func_name,
            'args': args,
            'kwargs': kwargs
        }
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return f"tunshu:cache:{hashlib.md5(key_str.encode()).hexdigest()}"
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        redis = await self._ensure_connection()
        if redis is None:
            return None
            
        try:
            value = await redis.get(key)
            if value:
                return json.loads(value)
        except Exception as e:
            logger.error(f"Redis GET错误: {e}")
        return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        redis = await self._ensure_connection()
        if redis is None:
            return False
            
        try:
            ttl = ttl or self._default_ttl
            serialized_value = json.dumps(value, default=str)
            await redis.setex(key, ttl, serialized_value)
            return True
        except Exception as e:
            logger.error(f"Redis SET错误: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        redis = await self._ensure_connection()
        if redis is None:
            return False
            
        try:
            await redis.delete(key)
            return True
        except Exception as e:
            logger.error(f"Redis DELETE错误: {e}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """按模式清除缓存"""
        redis = await self._ensure_connection()
        if redis is None:
            return 0
            
        try:
            keys = await redis.keys(pattern)
            if keys:
                return await redis.delete(*keys)
            return 0
        except Exception as e:
            logger.error(f"Redis CLEAR错误: {e}")
            return 0
    
    async def stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        redis = await self._ensure_connection()
        if redis is None:
            return {"status": "disconnected"}
            
        try:
            info = await redis.info()
            return {
                "status": "connected",
                "used_memory": info.get("used_memory", 0),
                "connected_clients": info.get("connected_clients", 0),
                "total_commands_processed": info.get("total_commands_processed", 0)
            }
        except Exception as e:
            logger.error(f"Redis STATS错误: {e}")
            return {"status": "error", "error": str(e)}

    async def close(self):
        """关闭Redis连接"""
        if self._redis:
            await self._redis.close()
        if self._connection_pool:
            await self._connection_pool.disconnect()

class FallbackMemoryCache:
    """
    内存缓存作为Redis的降级方案
    """
    
    def __init__(self, default_ttl: int = 300):
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._default_ttl = default_ttl
    
    def _generate_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """生成缓存键"""
        key_data = {
            'func': func_name,
            'args': args,
            'kwargs': kwargs
        }
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key in self._cache:
            cache_data = self._cache[key]
            if time.time() < cache_data['expires_at']:
                return cache_data['value']
            else:
                del self._cache[key]
        return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        ttl = ttl or self._default_ttl
        self._cache[key] = {
            'value': value,
            'expires_at': time.time() + ttl,
            'created_at': time.time()
        }
        return True
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        self._cache.pop(key, None)
        return True
    
    async def clear_pattern(self, pattern: str) -> int:
        """按模式清除缓存（简化版）"""
        pattern = pattern.replace('*', '')
        keys_to_delete = [k for k in self._cache.keys() if pattern in k]
        for key in keys_to_delete:
            del self._cache[key]
        return len(keys_to_delete)
    
    async def stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        now = time.time()
        active_count = sum(1 for data in self._cache.values() if now < data['expires_at'])
        return {
            'status': 'memory',
            'total_keys': len(self._cache),
            'active_keys': active_count,
            'expired_keys': len(self._cache) - active_count
        }

# 初始化缓存实例
# if REDIS_AVAILABLE:
#     cache = RedisCache(settings.REDIS_URL, settings.CACHE_EXPIRE_TIME)
# else:
#     logger.warning("Redis不可用，使用内存缓存作为降级方案")
#     cache = FallbackMemoryCache(settings.CACHE_EXPIRE_TIME)

# 根据用户请求，强制使用内存缓存
logger.warning("根据用户请求，已强制禁用Redis，当前使用内存缓存作为降级方案。")
cache = FallbackMemoryCache(settings.CACHE_EXPIRE_TIME)


def cached(ttl: int = 300, key_prefix: str = ""):
    """
    缓存装饰器，用于缓存函数结果
    
    Args:
        ttl: 缓存时间（秒）
        key_prefix: 缓存键前缀
    """
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{cache._generate_key(func.__name__, args, kwargs)}"
            
            # 尝试从缓存获取
            cached_result = await cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"缓存命中: {func.__name__}")
                return cached_result
            
            # 执行函数并缓存结果
            logger.debug(f"缓存未命中，执行函数: {func.__name__}")
            result = await func(*args, **kwargs)
            await cache.set(cache_key, result, ttl)
            return result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # 对于同步函数，我们需要在异步环境中运行缓存操作
            async def _async_sync_wrapper():
                cache_key = f"{key_prefix}:{cache._generate_key(func.__name__, args, kwargs)}"
                
                cached_result = await cache.get(cache_key)
                if cached_result is not None:
                    logger.debug(f"缓存命中: {func.__name__}")
                    return cached_result
                
                logger.debug(f"缓存未命中，执行函数: {func.__name__}")
                result = func(*args, **kwargs)
                await cache.set(cache_key, result, ttl)
                return result
            
            # 尝试在现有事件循环中运行
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果事件循环正在运行，使用 run_coroutine_threadsafe
                    import concurrent.futures
                    import threading
                    
                    def run_in_thread():
                        new_loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(new_loop)
                        try:
                            return new_loop.run_until_complete(_async_sync_wrapper())
                        finally:
                            new_loop.close()
                    
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(run_in_thread)
                        return future.result()
                else:
                    return loop.run_until_complete(_async_sync_wrapper())
            except RuntimeError:
                # 没有事件循环，创建新的
                return asyncio.run(_async_sync_wrapper())
        
        # 根据函数是否为协程选择包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

# 提供一些便捷的缓存操作函数
async def clear_cache_pattern(pattern: str = "tunshu:cache:*") -> int:
    """清空匹配模式的缓存"""
    return await cache.clear_pattern(pattern)

async def get_cache_stats() -> Dict[str, Any]:
    """获取缓存统计信息"""
    return await cache.stats()

async def close_cache():
    """关闭缓存连接"""
    if hasattr(cache, 'close'):
        await cache.close()

# 特定模块的缓存装饰器
def cache_client_data(ttl: int = 600):
    """客户数据专用缓存装饰器，缓存时间更长（10分钟）"""
    return cached(ttl=ttl, key_prefix="client_data")

def cache_ai_selection_data(ttl: int = 1800):
    """AI选校数据专用缓存装饰器，缓存时间更长（30分钟）"""
    return cached(ttl=ttl, key_prefix="ai_selection")

def cache_user_data(ttl: int = 300):
    """用户数据专用缓存装饰器（5分钟）"""
    return cached(ttl=ttl, key_prefix="user_data")

def cache_profile_summary(ttl: int = 900):
    """客户档案摘要专用缓存装饰器（15分钟）"""
    return cached(ttl=ttl, key_prefix="profile_summary") 