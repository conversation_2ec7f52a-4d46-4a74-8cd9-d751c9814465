"""
AI Writing - 客户端数据通用工具函数
"""
import logging
from typing import Dict, Any, List, Optional, Protocol, Type

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from app.models.client import Client, ClientProgram
from app.core.cache import cache_client_data, cache_profile_summary

logger = logging.getLogger(__name__)

# --- Protocols for Type Hinting ---

class ExperienceSelectionRequest(Protocol):
    """
    定义一个协议，用于表示包含所选经历ID的请求。
    CVGenerationRequest和PSGenerationRequest都实现了这个协议。
    """
    selected_education_ids: Optional[List[int]]
    selected_academic_ids: Optional[List[int]]
    selected_work_ids: Optional[List[int]]
    selected_activity_ids: Optional[List[int]]
    selected_award_ids: Optional[List[int]]
    selected_skill_ids: Optional[List[int]]
    selected_language_score_ids: Optional[List[int]]


# --- Shared Functions ---

@cache_client_data(ttl=600)  # 缓存10分钟
async def get_client_with_full_relations(
    db: AsyncSession, 
    client_id: str
) -> Optional[Client]:
    """
    获取客户及其所有关联的经历数据。
    这是一个通用函数，可被CV、PS等模块共用。
    """
    query = (
        select(Client)
        .options(
            selectinload(Client.education),
            selectinload(Client.academic),
            selectinload(Client.work),
            selectinload(Client.activities),
            selectinload(Client.awards),
            selectinload(Client.skills),
            selectinload(Client.language_scores),
            selectinload(Client.thoughts),
            selectinload(Client.client_programs).selectinload(ClientProgram.ai_selection_program)
        )
        .where(Client.id_hashed == client_id)
    )
    
    result = await db.execute(query)
    return result.scalars().first()


async def filter_client_experiences(
    client: Client,
    request: ExperienceSelectionRequest
) -> Dict[str, List[Any]]:
    """
    根据用户在请求中选择的ID列表，筛选客户的各项经历。
    这是一个通用函数，使用协议(Protocol)来适应不同的请求模型。
    
    Args:
        client: 完整的客户ORM对象 (应已预加载所有经历关系)。
        request: 遵循ExperienceSelectionRequest协议的请求对象。

    Returns:
        一个字典，包含筛选后的各项经历列表。
    """
    filtered_data = {
        'education': [], 'academic': [], 'work': [], 'activities': [],
        'awards': [], 'skills': [], 'language_scores': []
    }

    def filter_by_ids(items: List[Any], selected_ids: Optional[List[int]]) -> List[Any]:
        """通用筛选逻辑"""
        if not selected_ids:
            return []
        return [item for item in items if item.id in selected_ids]

    filtered_data['education'] = filter_by_ids(client.education, request.selected_education_ids)
    filtered_data['academic'] = filter_by_ids(client.academic, request.selected_academic_ids)
    filtered_data['work'] = filter_by_ids(client.work, request.selected_work_ids)
    filtered_data['activities'] = filter_by_ids(client.activities, request.selected_activity_ids)
    filtered_data['awards'] = filter_by_ids(client.awards, request.selected_award_ids)
    filtered_data['skills'] = filter_by_ids(client.skills, request.selected_skill_ids)
    filtered_data['language_scores'] = filter_by_ids(client.language_scores, request.selected_language_score_ids)
    
    return filtered_data


@cache_profile_summary(ttl=300)  # 缓存5分钟
async def get_all_clients_with_summaries(db: AsyncSession) -> List[Client]:
    """
    获取所有未归档客户的核心摘要信息，用于前端客户列表。
    返回完整的Client对象列表，具体摘要由调用方根据自身需求格式化。
    """
    query = (
        select(Client)
        .options(
            selectinload(Client.education),
            selectinload(Client.academic),
            selectinload(Client.work),
            selectinload(Client.activities),
            selectinload(Client.awards),
            selectinload(Client.skills),
            selectinload(Client.client_programs)
        )
        .where(Client.is_archived == False)
        .order_by(Client.id.desc())
    )
    
    result = await db.execute(query)
    return result.scalars().all() 