import logging
from typing import Dict, Any, List, Optional, AsyncGenerator
from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from datetime import datetime

from app.models.client import (
    Client, Education, Academic, Work, Activity,
    Award, Skill, LanguageScore, AIWritingCV
)
from app.ai_writing.utils.llm import generate_cv_content_stream_async
from app.ai_writing.utils.client_data import (
    get_client_with_full_relations,
    filter_client_experiences,
    get_all_clients_with_summaries
)
from app.ai_writing.schemas.cv import (
    CVGenerationRequest, 
    CVGenerationResponse,
    ClientProfileSummary,
    ClientModuleData,
    ClientBasicInfo,
    CVSaveRequest,
    CVSaveResponse
)
from app.core.cache import cache_profile_summary, cache_client_data

logger = logging.getLogger(__name__)

async def generate_cv_stream(
    db: AsyncSession,
    request: CVGenerationRequest
) -> AsyncGenerator[str, None]:
    """
    根据客户信息和选择的模块流式生成CV
    """
    # 1. 验证客户是否存在并获取完整信息
    client = await get_client_with_full_relations(db, request.client_id)
    if not client:
        # 在异步生成器中，我们不能直接抛出HTTPException，
        # 因为头部可能已经发送。我们yield一个错误信息。
        yield "Error: Client not found"
        return

    # 2. 根据用户选择筛选数据
    filtered_data = await filter_client_experiences(client, request)
    
    # 3. 构建客户数据字典
    client_data = build_client_data_dict(client, filtered_data)
    
    # 4. 调用LLM流式生成CV内容
    logger.info(f"开始为客户 {client.name} 流式生成 {request.language} CV")
    
    async for content_chunk in generate_cv_content_stream_async(
        client_data=client_data,
        language=request.language,
        additional_info=request.additional_info or ""
    ):
        yield content_chunk
    
    logger.info(f"成功为客户 {client.name} 流式生成CV")

def build_client_data_dict(
    client: Client, 
    filtered_data: Dict[str, List[Any]]
) -> Dict[str, Any]:
    """
    将客户信息和筛选后的数据转换为字典格式
    """
    return {
        'name': client.name,
        'email': client.email,
        'phone': client.phone,
        'location': client.location,
        'education': [edu.to_dict() for edu in filtered_data['education']],
        'academic': [academic.to_dict() for academic in filtered_data['academic']],
        'work': [work.to_dict() for work in filtered_data['work']],
        'activities': [activity.to_dict() for activity in filtered_data['activities']],
        'awards': [award.to_dict() for award in filtered_data['awards']],
        'skills': [skill.to_dict() for skill in filtered_data['skills']],
        'language_scores': [lang.to_dict() for lang in filtered_data['language_scores']]
    }

@cache_profile_summary(ttl=300)  # 缓存5分钟
async def get_client_profiles_summary(db: AsyncSession) -> List[ClientProfileSummary]:
    """
    获取客户档案摘要列表 - 优化版本，添加缓存
    
    返回所有未归档客户的基本信息和各模块数据数量，
    用于前端显示客户选择列表。
    """
    clients = await get_all_clients_with_summaries(db)
    
    profiles = []
    for client in clients:
        profiles.append({
            'id_hashed': client.id_hashed,
            'name': client.name,
            'phone': client.phone,
            'location': client.location,
            'education_count': len(client.education),
            'academic_count': len(client.academic),
            'work_count': len(client.work),
            'activity_count': len(client.activities),
            'award_count': len(client.awards),
            'skill_count': len(client.skills)
        })
    
    return profiles

@cache_client_data(ttl=600)  # 缓存10分钟
async def get_client_module_data(
    db: AsyncSession, 
    client_id: str
) -> Dict[str, Any]:
    """
    获取客户的详细模块数据，用于前端显示和选择
    """
    client = await get_client_with_full_relations(db, client_id)
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )
    
    return {
        'client_info': {
            'id_hashed': client.id_hashed,
            'name': client.name,
            'email': client.email,
            'phone': client.phone,
            'location': client.location
        },
        'education': [edu.to_dict() for edu in client.education],
        'academic': [academic.to_dict() for academic in client.academic],
        'work': [work.to_dict() for work in client.work],
        'activities': [activity.to_dict() for activity in client.activities],
        'awards': [award.to_dict() for award in client.awards],
        'skills': [skill.to_dict() for skill in client.skills],
        'language_scores': [lang.to_dict() for lang in client.language_scores]
    } 

async def save_cv_content(
    db: AsyncSession,
    request: CVSaveRequest
) -> CVSaveResponse:
    """
    保存CV内容到数据库
    """
    # 1. 验证客户是否存在
    client_query = select(Client).where(Client.id_hashed == request.client_id)
    result = await db.execute(client_query)
    client = result.scalar_one_or_none()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )
    
    try:
        # 始终创建新记录
        new_cv = AIWritingCV(
            client_id=client.id,
            content_markdown=request.content_markdown,
            version_name=request.version_name,
            target_major=request.target_major
        )
        db.add(new_cv)
        logger.info(f"为客户 {client.name} 创建新的CV记录，版本: {request.version_name}")
        
        await db.commit()
        
        return CVSaveResponse(
            status="success",
            message="CV内容保存成功",
            client_name=client.name,
            saved_at=datetime.now().isoformat()
        )
        
    except Exception as e:
        await db.rollback()
        logger.error(f"保存CV失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="保存CV内容失败"
        )


async def get_cv_document(
    db: AsyncSession,
    document_id: int
) -> Dict[str, Any]:
    """
    获取单个CV文档的详细信息
    """
    try:
        # 查询CV文档及其关联的客户信息
        query = select(AIWritingCV).options(
            selectinload(AIWritingCV.client)
        ).where(AIWritingCV.id == document_id)

        result = await db.execute(query)
        cv_document = result.scalar_one_or_none()

        if not cv_document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="CV文档不存在"
            )

        return {
            "id": cv_document.id,
            "version_name": cv_document.version_name,
            "target_major": cv_document.target_major,
            "content_markdown": cv_document.content_markdown,
            "client_id": cv_document.client.id_hashed,
            "client_name": cv_document.client.name,
            "created_at": cv_document.created_at.isoformat() if cv_document.created_at else None,
            "updated_at": cv_document.updated_at.isoformat() if cv_document.updated_at else None
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取CV文档失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取CV文档失败"
        )


async def get_client_by_id_hashed(
    db: AsyncSession,
    client_id: str
) -> Dict[str, Any]:
    """
    根据客户哈希ID获取客户基本信息
    """
    try:
        query = select(Client).where(Client.id_hashed == client_id)
        result = await db.execute(query)
        client = result.scalar_one_or_none()

        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="客户不存在"
            )

        return {
            "id_hashed": client.id_hashed,
            "name": client.name,
            "phone": client.phone,
            "email": client.email,
            "location": client.location,
            "created_at": client.created_at.isoformat() if client.created_at else None,
            "updated_at": client.updated_at.isoformat() if client.updated_at else None
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取客户信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取客户信息失败"
        )