from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any

class CVGenerationRequest(BaseModel):
    """CV 生成请求模型"""
    client_id: str = Field(..., description="客户哈希ID")
    language: str = Field(..., description="简历语言：'english' 或 'chinese'")
    additional_info: Optional[str] = Field(None, description="额外信息")
    
    # 模块选择 - 用户可以选择包含哪些经历
    selected_education_ids: Optional[List[int]] = Field([], description="选择的教育经历ID列表")
    selected_academic_ids: Optional[List[int]] = Field([], description="选择的学术经历ID列表")
    selected_work_ids: Optional[List[int]] = Field([], description="选择的工作经历ID列表")
    selected_activity_ids: Optional[List[int]] = Field([], description="选择的课外活动ID列表")
    selected_award_ids: Optional[List[int]] = Field([], description="选择的奖项ID列表")
    selected_skill_ids: Optional[List[int]] = Field([], description="选择的技能ID列表")
    selected_language_score_ids: Optional[List[int]] = Field([], description="选择的语言成绩ID列表")

class CVGenerationResponse(BaseModel):
    """CV 生成响应模型"""
    status: str = "success"
    cv_content: str = Field(..., description="生成的CV内容（HTML格式）")
    client_name: str = Field(..., description="客户姓名")
    language: str = Field(..., description="简历语言")

class CVSaveRequest(BaseModel):
    """CV保存请求模型"""
    client_id: str = Field(..., description="客户哈希ID")
    content_markdown: str = Field(..., description="CV的Markdown内容")
    version_name: str = Field(..., description="CV版本名称")
    target_major: Optional[str] = Field(None, description="目标专业")

class CVSaveResponse(BaseModel):
    """CV保存响应模型"""
    status: str = Field(default="success", description="保存状态")
    message: str = Field(..., description="保存结果消息")
    client_name: str = Field(..., description="客户姓名")
    saved_at: str = Field(..., description="保存时间")

class ClientProfileSummary(BaseModel):
    """客户档案摘要模型（用于前端选择）"""
    id_hashed: str = Field(..., description="客户哈希ID")
    name: str = Field(..., description="客户姓名")
    phone: Optional[str] = Field(None, description="电话")
    location: Optional[str] = Field(None, description="位置")
    education_count: int = Field(0, description="教育经历数量")
    academic_count: int = Field(0, description="学术经历数量")
    work_count: int = Field(0, description="工作经历数量")
    activity_count: int = Field(0, description="课外活动数量")
    award_count: int = Field(0, description="奖项数量")
    skill_count: int = Field(0, description="技能数量")

class ClientBasicInfo(BaseModel):
    """客户基本信息模型"""
    name: str = Field(..., description="客户姓名")
    email: Optional[str] = Field(None, description="邮箱")
    phone: Optional[str] = Field(None, description="电话")
    location: Optional[str] = Field(None, description="位置")

class ClientModuleData(BaseModel):
    """客户模块数据模型（用于前端显示和选择）"""
    client_info: ClientBasicInfo = Field(..., description="客户基本信息")
    education: List[Dict[str, Any]] = Field([], description="教育经历列表")
    academic: List[Dict[str, Any]] = Field([], description="学术经历列表")
    work: List[Dict[str, Any]] = Field([], description="工作经历列表")
    activities: List[Dict[str, Any]] = Field([], description="课外活动列表")
    awards: List[Dict[str, Any]] = Field([], description="奖项列表")
    skills: List[Dict[str, Any]] = Field([], description="技能列表")
    language_scores: List[Dict[str, Any]] = Field([], description="语言成绩列表") 