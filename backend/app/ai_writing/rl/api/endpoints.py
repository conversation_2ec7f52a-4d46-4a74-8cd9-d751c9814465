from fastapi import APIRouter, Depends, status, UploadFile, File
from fastapi.responses import StreamingResponse
from typing import Dict, Any

from app.core.dependencies import DBSession
from app.ai_writing.rl.core.generator import (
    parse_uploaded_rl_template,
    generate_rl_stream,
    save_rl_content,
    get_rl_document,
    get_client_by_id_hashed_rl
)
from app.ai_writing.schemas.rl import (
    RLParseResponse,
    RLGenerationRequest,
    RLSaveRequest,
    RLSaveResponse
)

router = APIRouter()

@router.post(
    "/parse",
    response_model=RLParseResponse,
    status_code=status.HTTP_200_OK,
    summary="解析推荐信模板文件",
    description="上传包含推荐人信息的.docx文件，解析并返回结构化的JSON数据。"
)
async def parse_rl_template_endpoint(
    file: UploadFile = File(..., description="要解析的 .docx 格式的推荐人信息收集表格")
):
    """
    解析上传的推荐信模板文件。
    """
    parsed_data = await parse_uploaded_rl_template(file)
    return RLParseResponse(
        file_name=file.filename,
        parsed_data=parsed_data
    )

@router.post(
    "/generate",
    response_class=StreamingResponse,
    status_code=status.HTTP_200_OK,
    summary="流式生成RL/推荐信",
    description="根据客户档案和解析后的推荐人信息，流式生成专业的推荐信。"
)
async def generate_rl_stream_endpoint(
    request: RLGenerationRequest,
    db: DBSession
) -> StreamingResponse:
    """
    流式生成RL/推荐信。
    """
    rl_generator = generate_rl_stream(db, request)
    return StreamingResponse(rl_generator, media_type="text/markdown") 

@router.post(
    "/save",
    response_model=RLSaveResponse,
    status_code=status.HTTP_200_OK,
    summary="保存RL内容",
    description="将RL的Markdown内容保存到数据库"
)
async def save_rl_endpoint(
    request: RLSaveRequest,
    db: DBSession
) -> RLSaveResponse:
    """
    保存RL内容
    
    将用户编辑后的RL Markdown内容保存到数据库中。
    """
    result = await save_rl_content(db, request)
    return result


@router.get(
    "/documents/{document_id}",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="获取RL文档详情",
    description="根据文档ID获取RL文档的详细信息，包括内容和版本信息"
)
async def get_rl_document_endpoint(
    document_id: int,
    db: DBSession
) -> Dict[str, Any]:
    """
    获取RL文档详情

    根据文档ID获取RL文档的详细信息，用于编辑模式加载。
    """
    document = await get_rl_document(db, document_id)
    return document


@router.get(
    "/clients/{client_id}",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="获取客户基本信息",
    description="根据客户哈希ID获取客户的基本信息"
)
async def get_client_info_endpoint(
    client_id: str,
    db: DBSession
) -> Dict[str, Any]:
    """
    获取客户基本信息

    根据客户哈希ID获取客户的基本信息，用于文书写作页面的客户选择。
    """
    client = await get_client_by_id_hashed_rl(db, client_id)
    return client