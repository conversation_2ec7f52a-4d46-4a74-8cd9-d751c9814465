from fastapi import APIRouter, Depends, status, HTTPException, UploadFile, File
from fastapi.responses import StreamingResponse
from typing import List, Dict, Any, AsyncGenerator

from app.core.dependencies import DBSession
from app.ai_writing.ps.core.generator import (
    generate_ps_stream,
    get_client_profiles_summary,
    get_client_programs_data,
    get_client_module_data,
    save_ps_content,
    match_cv_for_ps
)
from app.ai_writing.ps.utils.file_parser import parse_ps_document
from app.ai_writing.schemas.ps import (
    PSGenerationRequest,
    ClientProfileSummary,
    ClientProgramsData,
    ClientModuleData,
    PSParseResponse,
    PSSaveRequest,
    PSSaveResponse,
    CVMatchRequest,
    CVMatchResponse
)

router = APIRouter()

@router.post(
    "/generate",
    response_class=StreamingResponse,
    status_code=status.HTTP_200_OK,
    summary="流式生成PS/个人陈述",
    description="根据客户档案信息和申请目标流式生成专业的PS/个人陈述"
)
async def generate_ps_stream_endpoint(
    request: PSGenerationRequest,
    db: DBSession
) -> StreamingResponse:
    """
    流式生成PS/个人陈述
    
    根据客户信息和申请目标流式生成专业的个人陈述。
    """
    ps_generator = generate_ps_stream(db, request)
    return StreamingResponse(ps_generator, media_type="text/markdown")

@router.get(
    "/clients",
    response_model=List[ClientProfileSummary],
    status_code=status.HTTP_200_OK,
    summary="获取客户档案列表",
    description="获取所有客户的档案摘要信息，用于前端选择"
)
async def get_client_profiles(db: DBSession) -> List[ClientProfileSummary]:
    """
    获取客户档案列表
    
    返回所有未归档客户的基本信息和各模块数据数量，
    用于前端显示客户选择列表。
    """
    profiles = await get_client_profiles_summary(db)
    return [ClientProfileSummary(**profile) for profile in profiles]

@router.get(
    "/clients/{client_id}/programs",
    response_model=ClientProgramsData,
    status_code=status.HTTP_200_OK,
    summary="获取客户定校书数据",
    description="获取指定客户的定校书数据，用于前端显示申请目标选项"
)
async def get_client_programs(
    client_id: str,
    db: DBSession
) -> ClientProgramsData:
    """
    获取客户定校书数据
    
    根据客户ID获取该客户的定校书数据，包括申请院校、学位、专业选项，
    用于前端显示和用户选择申请目标。
    """
    programs_data = await get_client_programs_data(db, client_id)
    return ClientProgramsData(**programs_data)

@router.get(
    "/clients/{client_id}/modules",
    response_model=ClientModuleData,
    status_code=status.HTTP_200_OK,
    summary="获取客户详细模块数据",
    description="获取指定客户的所有模块详细数据，用于前端显示和选择经历"
)
async def get_client_modules(
    client_id: str,
    db: DBSession
) -> ClientModuleData:
    """
    获取客户详细模块数据

    根据客户ID获取该客户的所有教育经历、工作经历、学术经历等详细信息，
    用于前端显示和用户选择特定的经历条目。
    """
    module_data = await get_client_module_data(db, client_id)
    return ClientModuleData(**module_data)

@router.post(
    "/parse",
    response_model=PSParseResponse,
    status_code=status.HTTP_200_OK,
    summary="解析PS相关文档",
    description="上传包含申请动机和职业规划信息的.docx文件，解析并返回结构化的JSON数据。"
)
async def parse_ps_document_endpoint(
    file: UploadFile = File(..., description="包含申请动机和职业规划信息的.docx文件")
) -> PSParseResponse:
    """
    解析PS相关文档

    上传.docx文件，使用LLM解析其中的申请动机和职业规划信息，
    返回结构化的数据供PS生成使用。
    """
    motivation_profile = await parse_ps_document(file)
    return PSParseResponse(
        motivation_profile=motivation_profile,
        message="文档解析成功"
    )

@router.post(
    "/save",
    response_model=PSSaveResponse,
    status_code=status.HTTP_200_OK,
    summary="保存PS内容",
    description="将PS的Markdown内容保存到数据库"
)
async def save_ps_endpoint(
    request: PSSaveRequest,
    db: DBSession
) -> PSSaveResponse:
    """
    保存PS内容

    将用户编辑后的PS Markdown内容保存到数据库中。
    target_major字段会自动组合为"院校 - 学位 - 专业"的格式。
    """
    result = await save_ps_content(db, request)
    return PSSaveResponse(**result)

@router.post(
    "/match-cv",
    response_model=CVMatchResponse,
    status_code=status.HTTP_200_OK,
    summary="匹配对应的CV简历",
    description="根据客户ID和申请目标查找匹配的CV简历，用于PS生成的数据源"
)
async def match_cv_endpoint(
    request: CVMatchRequest,
    db: DBSession
) -> CVMatchResponse:
    """
    匹配对应的CV简历

    根据客户ID和申请目标（院校、学位、专业）查找匹配的CV简历。
    PS生成将基于找到的CV内容作为数据源，而不是手动选择经历。
    """
    result = await match_cv_for_ps(db, request)
    return CVMatchResponse(**result)