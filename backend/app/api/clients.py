import asyncio
from fastapi import APIRouter, Depends, HTTPException, status, Path, Query
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload
from typing import List, Optional

from app.core.dependencies import CurrentUser, DBSession
from app.core.cache import cached, cache_client_data, cache_profile_summary
from app.models.client import (
    Client, Education, Academic, Work, Activity, Award, Skill, LanguageScore, Thought,
    BackgroundCustomModule, ThoughtCustomModule, ClientProgram
)
from app.schemas.client import (
    ClientCreate, ClientUpdate, ClientResponse, ClientDetailResponse,
    EducationCreate, EducationUpdate, EducationResponse,
    AcademicCreate, AcademicUpdate, AcademicResponse,
    WorkCreate, WorkUpdate, WorkResponse,
    ActivityCreate, ActivityUpdate, ActivityResponse,
    AwardCreate, AwardUpdate, AwardResponse,
    SkillCreate, SkillUpdate, SkillResponse,
    LanguageScoreCreate, LanguageScoreUpdate, LanguageScoreResponse,
    ThoughtCreate, ThoughtUpdate, ThoughtResponse,
    CustomModuleCreate, CustomModuleUpdate, CustomModuleResponse,
    ClientProgramCreate, ClientProgramResponse, ClientDocumentSummary
)
from app.ai_selection.db.models import AISelectionProgram
from app.models.client import AIWritingCV, AIWritingRL

# 创建路由器
router = APIRouter(prefix="/clients", tags=["客户"])

# 客户信息CRUD相关：
@router.get("/", response_model=List[ClientResponse], status_code=status.HTTP_200_OK)
@cache_profile_summary(ttl=300)  # 缓存5分钟
async def get_clients(
    db: DBSession,
    skip: int = Query(0, description="分页起始位置"),
    limit: int = Query(100, description="每页数量"),
    is_archived: Optional[bool] = Query(None, description="是否已归档（服务完成）"),
    search: Optional[str] = Query(None, description="搜索关键词（支持姓名、电话、邮箱、学生ID等多字段搜索）"),
    # current_user: CurrentUser = None,
):
    """
    获取客户列表

    Args:
        skip: 分页起始位置
        limit: 每页数量
        is_archived: 是否已归档（服务完成），None表示不筛选
        search: 搜索关键词，支持多字段模糊匹配（姓名、电话、邮箱、身份证、护照、哈希ID）
        current_user: 当前用户
        db: 数据库会话

    Returns:
        List[ClientResponse]: 客户列表数据
    """
    # 构建查询
    query = select(Client)

    # 根据归档状态筛选
    if is_archived is not None:
        query = query.where(Client.is_archived == is_archived)
        
    # 根据搜索关键词筛选 - 支持多字段搜索
    if search:
        search_term = f"%{search}%"
        # 使用 OR 条件匹配多个字段
        search_conditions = [
            Client.name.ilike(search_term),           # 姓名
            Client.phone.ilike(search_term),          # 电话
            Client.email.ilike(search_term),          # 邮箱
            Client.id_card.ilike(search_term),        # 身份证（学生ID）
            Client.passport.ilike(search_term),       # 护照（学生ID）
            Client.id_hashed.ilike(search_term),      # 哈希ID（系统学生ID）
            Client.location.ilike(search_term)        # 所在城市
        ]
        # 过滤掉None值的条件
        from sqlalchemy import or_
        query = query.where(or_(*[cond for cond in search_conditions if cond is not None]))

    # 分页
    query = query.offset(skip).limit(limit)

    # 执行查询
    result = await db.execute(query)
    clients = result.scalars().all()

    return clients

@router.post("/", response_model=ClientResponse, status_code=status.HTTP_201_CREATED)
async def create_client(
    db: DBSession,
    client_data: ClientCreate,
    current_user: CurrentUser = None,
):
    """
    创建新客户

    Args:
        client_data: 客户数据
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ClientResponse: 创建的客户信息
    """
    # 创建新客户
    client_dict = client_data.dict()
    # 确保新客户的归档状态为False
    client_dict["is_archived"] = False
    client = Client(**client_dict)

    # 如果有当前用户，设置为客户的顾问
    if current_user:
        client.user_id = current_user.id

    db.add(client)
    await db.commit()
    await db.refresh(client)

    return client

@router.get("/{client_id}/", response_model=ClientDetailResponse, status_code=status.HTTP_200_OK)
@cache_client_data(ttl=600)  # 缓存10分钟
async def get_client(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    include_modules: Optional[str] = Query(None, description="要包含的模块，逗号分隔：education,academic,work,activities,awards,skills,language_scores,thoughts,background_modules,thought_modules,client_programs"),
    # current_user: CurrentUser = None,
):
    """
    获取单个客户详情 - 优化版本，支持懒加载和按需查询

    Args:
        client_id: 客户哈希ID
        include_modules: 要包含的模块列表，支持按需加载以提高性能
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ClientDetailResponse: 客户详细信息
    """
    # 查询客户（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    # 如果客户不存在，返回404错误
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 获取客户真实ID用于关联查询
    real_client_id = client.id

    # 解析要包含的模块
    if include_modules:
        requested_modules = set(include_modules.split(','))
    else:
        # 默认包含基本模块
        requested_modules = {'education', 'academic', 'work', 'activities', 'awards', 'skills'}

    # 只查询请求的模块，实现懒加载
    query_tasks = []
    
    if 'education' in requested_modules:
        education_query = select(Education).where(Education.client_id == real_client_id)
        query_tasks.append(('education', db.execute(education_query)))
    
    if 'academic' in requested_modules:
        academic_query = select(Academic).where(Academic.client_id == real_client_id)
        query_tasks.append(('academic', db.execute(academic_query)))
    
    if 'work' in requested_modules:
        work_query = select(Work).where(Work.client_id == real_client_id)
        query_tasks.append(('work', db.execute(work_query)))
    
    if 'activities' in requested_modules:
        activity_query = select(Activity).where(Activity.client_id == real_client_id)
        query_tasks.append(('activities', db.execute(activity_query)))
    
    if 'awards' in requested_modules:
        award_query = select(Award).where(Award.client_id == real_client_id)
        query_tasks.append(('awards', db.execute(award_query)))
    
    if 'skills' in requested_modules:
        skill_query = select(Skill).where(Skill.client_id == real_client_id)
        query_tasks.append(('skills', db.execute(skill_query)))
    
    if 'language_scores' in requested_modules:
        language_score_query = select(LanguageScore).where(LanguageScore.client_id == real_client_id)
        query_tasks.append(('language_scores', db.execute(language_score_query)))
    
    if 'thoughts' in requested_modules:
        thought_query = select(Thought).where(Thought.client_id == real_client_id)
        query_tasks.append(('thoughts', db.execute(thought_query)))
    
    if 'background_modules' in requested_modules:
        background_module_query = select(BackgroundCustomModule).where(BackgroundCustomModule.client_id == real_client_id)
        query_tasks.append(('background_modules', db.execute(background_module_query)))
    
    if 'thought_modules' in requested_modules:
        thought_module_query = select(ThoughtCustomModule).where(ThoughtCustomModule.client_id == real_client_id)
        query_tasks.append(('thought_modules', db.execute(thought_module_query)))
    
    if 'client_programs' in requested_modules:
        client_program_query = select(ClientProgram).options(
            joinedload(ClientProgram.ai_selection_program)
        ).where(ClientProgram.client_id == real_client_id)
        query_tasks.append(('client_programs', db.execute(client_program_query)))

    # 并行执行选中的查询
    results = {}
    if query_tasks:
        query_results = await asyncio.gather(*[task[1] for task in query_tasks])
        for i, (module_name, _) in enumerate(query_tasks):
            results[module_name] = query_results[i].scalars().all()

    # 构建客户数据
    client_data = {
        "id_hashed": client.id_hashed,
        "name": client.name,
        "gender": client.gender,
        "phone": client.phone,
        "email": client.email,
        "location": client.location,
        "address": client.address,
        "id_card": client.id_card,
        "passport": client.passport,
        "id_card_issuer": client.id_card_issuer,
        "id_card_validity": client.id_card_validity,
        "passport_issue_place": client.passport_issue_place,
        "passport_issue_date": client.passport_issue_date,
        "passport_expiry": client.passport_expiry,
        "service_type": client.service_type,
        "user_id": client.user_id,
        "is_archived": client.is_archived,
        "created_at": client.created_at.isoformat() if client.created_at else None,
        "updated_at": client.updated_at.isoformat() if client.updated_at else None,
    }

    # 只添加请求的模块数据
    for module_name in requested_modules:
        if module_name in results:
            if module_name == 'client_programs':
                # 处理定校书数据的特殊逻辑
                client_programs_with_details = []
                school_names = []
                
                for cp in results[module_name]:
                    if cp.ai_selection_program and cp.ai_selection_program.school_name_cn:
                        school_names.append(cp.ai_selection_program.school_name_cn)
                
                # 批量查询学校logo
                school_logo_map = {}
                if school_names:
                    from app.ai_selection.db.models import AISelectionAbroadSchool
                    school_logo_query = select(
                        AISelectionAbroadSchool.school_name_cn,
                        AISelectionAbroadSchool.school_logo_url
                    ).where(AISelectionAbroadSchool.school_name_cn.in_(school_names))
                    school_logo_result = await db.execute(school_logo_query)
                    school_logo_map = {row[0]: row[1] for row in school_logo_result.fetchall()}
                
                for cp in results[module_name]:
                    cp_dict = cp.to_dict()
                    if cp.ai_selection_program:
                        school_logo_url = school_logo_map.get(cp.ai_selection_program.school_name_cn)
                        program_details = {
                            'id': cp.ai_selection_program.id,
                            'school_name_cn': cp.ai_selection_program.school_name_cn,
                            'school_name_en': cp.ai_selection_program.school_name_en,
                            'school_qs_rank': cp.ai_selection_program.school_qs_rank,
                            'school_region': cp.ai_selection_program.school_region,
                            'program_name_cn': cp.ai_selection_program.program_name_cn,
                            'program_name_en': cp.ai_selection_program.program_name_en,
                            'program_category': cp.ai_selection_program.program_category,
                            'degree': cp.ai_selection_program.degree,
                            'program_website': cp.ai_selection_program.program_website,
                            'gpa_requirements': cp.ai_selection_program.gpa_requirements,
                            'language_requirements': cp.ai_selection_program.language_requirements,
                            'application_requirements': cp.ai_selection_program.application_requirements,
                            'program_tuition': cp.ai_selection_program.program_tuition,
                            'enrollment_time': cp.ai_selection_program.enrollment_time,
                            'program_duration': cp.ai_selection_program.program_duration,
                            'school_logo_url': school_logo_url
                        }
                        cp_dict['program_details'] = program_details
                    client_programs_with_details.append(cp_dict)
                
                client_data[module_name] = client_programs_with_details
            else:
                client_data[module_name] = [item.to_dict() for item in results[module_name]]
        else:
            # 未请求的模块设为空列表
            client_data[module_name] = []

    # 使用 Pydantic 模型创建响应
    return ClientDetailResponse(**client_data)

@router.put("/{client_id}", response_model=ClientResponse, status_code=status.HTTP_200_OK)
async def update_client(
    db: DBSession,
    client_data: ClientUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    更新客户信息

    Args:
        client_data: 客户更新数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ClientResponse: 更新后的客户信息
    """
    # 查询客户（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    # 如果客户不存在，返回404错误
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 更新客户信息
    update_data = client_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(client, key, value)

    await db.commit()
    await db.refresh(client)

    return client

@router.delete("/{client_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_client(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    删除客户

    Args:
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    # 如果客户不存在，返回404错误
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 删除客户
    await db.delete(client)
    await db.commit()

    return None

@router.patch("/{client_id}/archive", response_model=ClientResponse, status_code=status.HTTP_200_OK)
async def toggle_client_archive_status(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    切换客户归档状态

    Args:
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ClientResponse: 更新后的客户信息
    """
    # 查询客户（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    # 如果客户不存在，返回404错误
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 切换归档状态
    client.is_archived = not client.is_archived

    await db.commit()
    await db.refresh(client)

    return client

# 教育经历相关API
@router.post("/{client_id}/education", response_model=EducationResponse, status_code=status.HTTP_201_CREATED)
async def add_education(
    db: DBSession,
    education_data: EducationCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加教育经历

    Args:
        education_data: 教育经历数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        EducationResponse: 创建的教育经历信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 创建教育经历（使用客户真实ID）
    education = Education(**education_data.dict(), client_id=client.id)
    db.add(education)
    await db.commit()
    await db.refresh(education)

    return education

@router.put("/{client_id}/education/{education_id}", response_model=EducationResponse, status_code=status.HTTP_200_OK)
async def update_education(
    db: DBSession,
    education_data: EducationUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    education_id: int = Path(..., description="教育经历ID"),
    # current_user: CurrentUser = None,
):
    """
    更新教育经历

    Args:
        education_data: 教育经历更新数据
        client_id: 客户哈希ID
        education_id: 教育经历ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        EducationResponse: 更新后的教育经历信息
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询教育经历是否存在（使用客户真实ID）
    education_query = select(Education).where(
        (Education.id == education_id) &
        (Education.client_id == client.id)
    )
    education_result = await db.execute(education_query)
    education = education_result.scalars().first()

    if not education:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="教育经历不存在或不属于该客户"
        )

    # 更新教育经历信息
    update_data = education_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(education, key, value)

    await db.commit()
    await db.refresh(education)

    return education

@router.delete("/{client_id}/education/{education_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_education(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    education_id: int = Path(..., description="教育经历ID"),
    # current_user: CurrentUser = None,
):
    """
    删除教育经历

    Args:
        client_id: 客户哈希ID
        education_id: 教育经历ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询教育经历是否存在（使用客户真实ID）
    education_query = select(Education).where(
        (Education.id == education_id) &
        (Education.client_id == client.id)
    )
    education_result = await db.execute(education_query)
    education = education_result.scalars().first()

    if not education:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="教育经历不存在或不属于该客户"
        )

    # 删除教育经历
    await db.delete(education)
    await db.commit()

    return None

# 学术经历相关API
@router.post("/{client_id}/academic", response_model=AcademicResponse, status_code=status.HTTP_201_CREATED)
async def add_academic(
    db: DBSession,
    academic_data: AcademicCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加学术经历

    Args:
        academic_data: 学术经历数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        AcademicResponse: 创建的学术经历信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 创建学术经历（使用客户真实ID）
    academic = Academic(**academic_data.dict(), client_id=client.id)
    db.add(academic)
    await db.commit()
    await db.refresh(academic)

    return academic

@router.put("/{client_id}/academic/{academic_id}", response_model=AcademicResponse, status_code=status.HTTP_200_OK)
async def update_academic(
    db: DBSession,
    academic_data: AcademicUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    academic_id: int = Path(..., description="学术经历ID"),
    # current_user: CurrentUser = None,
):
    """
    更新学术经历

    Args:
        academic_data: 学术经历更新数据
        client_id: 客户哈希ID
        academic_id: 学术经历ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        AcademicResponse: 更新后的学术经历信息
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询学术经历是否存在（使用客户真实ID）
    academic_query = select(Academic).where(
        (Academic.id == academic_id) &
        (Academic.client_id == client.id)
    )
    academic_result = await db.execute(academic_query)
    academic = academic_result.scalars().first()

    if not academic:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="学术经历不存在或不属于该客户"
        )

    # 更新学术经历信息
    update_data = academic_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(academic, key, value)

    await db.commit()
    await db.refresh(academic)

    return academic

@router.delete("/{client_id}/academic/{academic_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_academic(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    academic_id: int = Path(..., description="学术经历ID"),
    # current_user: CurrentUser = None,
):
    """
    删除学术经历

    Args:
        client_id: 客户哈希ID
        academic_id: 学术经历ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询学术经历是否存在（使用客户真实ID）
    academic_query = select(Academic).where(
        (Academic.id == academic_id) &
        (Academic.client_id == client.id)
    )
    academic_result = await db.execute(academic_query)
    academic = academic_result.scalars().first()

    if not academic:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="学术经历不存在或不属于该客户"
        )

    # 删除学术经历
    await db.delete(academic)
    await db.commit()

    return None

# 工作经历相关API
@router.post("/{client_id}/work", response_model=WorkResponse, status_code=status.HTTP_201_CREATED)
async def add_work(
    db: DBSession,
    work_data: WorkCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加工作经历

    Args:
        work_data: 工作经历数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        WorkResponse: 创建的工作经历信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 创建工作经历（使用客户真实ID）
    work = Work(**work_data.dict(), client_id=client.id)
    db.add(work)
    await db.commit()
    await db.refresh(work)

    return work

@router.put("/{client_id}/work/{work_id}", response_model=WorkResponse, status_code=status.HTTP_200_OK)
async def update_work(
    db: DBSession,
    work_data: WorkUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    work_id: int = Path(..., description="工作经历ID"),
    # current_user: CurrentUser = None,
):
    """
    更新工作经历

    Args:
        work_data: 工作经历更新数据
        client_id: 客户哈希ID
        work_id: 工作经历ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        WorkResponse: 更新后的工作经历信息
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询工作经历是否存在（使用客户真实ID）
    work_query = select(Work).where(
        (Work.id == work_id) &
        (Work.client_id == client.id)
    )
    work_result = await db.execute(work_query)
    work = work_result.scalars().first()

    if not work:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="工作经历不存在或不属于该客户"
        )

    # 更新工作经历信息
    update_data = work_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(work, key, value)

    await db.commit()
    await db.refresh(work)

    return work

@router.delete("/{client_id}/work/{work_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_work(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    work_id: int = Path(..., description="工作经历ID"),
    # current_user: CurrentUser = None,
):
    """
    删除工作经历

    Args:
        client_id: 客户哈希ID
        work_id: 工作经历ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询工作经历是否存在（使用客户真实ID）
    work_query = select(Work).where(
        (Work.id == work_id) &
        (Work.client_id == client.id)
    )
    work_result = await db.execute(work_query)
    work = work_result.scalars().first()

    if not work:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="工作经历不存在或不属于该客户"
        )

    # 删除工作经历
    await db.delete(work)
    await db.commit()

    return None

# 活动经历相关API
@router.post("/{client_id}/activities", response_model=ActivityResponse, status_code=status.HTTP_201_CREATED)
async def add_activity(
    db: DBSession,
    activity_data: ActivityCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加活动经历

    Args:
        activity_data: 活动经历数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ActivityResponse: 创建的活动经历信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 创建活动经历（使用客户真实ID）
    activity = Activity(**activity_data.dict(), client_id=client.id)
    db.add(activity)
    await db.commit()
    await db.refresh(activity)

    return activity

@router.put("/{client_id}/activities/{activity_id}", response_model=ActivityResponse, status_code=status.HTTP_200_OK)
async def update_activity(
    db: DBSession,
    activity_data: ActivityUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    activity_id: int = Path(..., description="活动经历ID"),
    # current_user: CurrentUser = None,
):
    """
    更新活动经历

    Args:
        activity_data: 活动经历更新数据
        client_id: 客户哈希ID
        activity_id: 活动经历ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ActivityResponse: 更新后的活动经历信息
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询活动经历是否存在（使用客户真实ID）
    activity_query = select(Activity).where(
        (Activity.id == activity_id) &
        (Activity.client_id == client.id)
    )
    activity_result = await db.execute(activity_query)
    activity = activity_result.scalars().first()

    if not activity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="活动经历不存在或不属于该客户"
        )

    # 更新活动经历信息
    update_data = activity_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(activity, key, value)

    await db.commit()
    await db.refresh(activity)

    return activity

@router.delete("/{client_id}/activities/{activity_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_activity(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    activity_id: int = Path(..., description="活动经历ID"),
    # current_user: CurrentUser = None,
):
    """
    删除活动经历

    Args:
        client_id: 客户哈希ID
        activity_id: 活动经历ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询活动经历是否存在（使用客户真实ID）
    activity_query = select(Activity).where(
        (Activity.id == activity_id) &
        (Activity.client_id == client.id)
    )
    activity_result = await db.execute(activity_query)
    activity = activity_result.scalars().first()

    if not activity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="活动经历不存在或不属于该客户"
        )

    # 删除活动经历
    await db.delete(activity)
    await db.commit()

    return None

# 奖项相关API
@router.post("/{client_id}/awards", response_model=AwardResponse, status_code=status.HTTP_201_CREATED)
async def add_award(
    db: DBSession,
    award_data: AwardCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加奖项

    Args:
        award_data: 奖项数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        AwardResponse: 创建的奖项信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 创建奖项（使用客户真实ID）
    award = Award(**award_data.dict(), client_id=client.id)
    db.add(award)
    await db.commit()
    await db.refresh(award)

    return award

@router.put("/{client_id}/awards/{award_id}", response_model=AwardResponse, status_code=status.HTTP_200_OK)
async def update_award(
    db: DBSession,
    award_data: AwardUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    award_id: int = Path(..., description="奖项ID"),
    # current_user: CurrentUser = None,
):
    """
    更新奖项

    Args:
        award_data: 奖项更新数据
        client_id: 客户哈希ID
        award_id: 奖项ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        AwardResponse: 更新后的奖项信息
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询奖项是否存在（使用客户真实ID）
    award_query = select(Award).where(
        (Award.id == award_id) &
        (Award.client_id == client.id)
    )
    award_result = await db.execute(award_query)
    award = award_result.scalars().first()

    if not award:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="奖项不存在或不属于该客户"
        )

    # 更新奖项信息
    update_data = award_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(award, key, value)

    await db.commit()
    await db.refresh(award)

    return award

@router.delete("/{client_id}/awards/{award_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_award(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    award_id: int = Path(..., description="奖项ID"),
    # current_user: CurrentUser = None,
):
    """
    删除奖项

    Args:
        client_id: 客户哈希ID
        award_id: 奖项ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询奖项是否存在（使用客户真实ID）
    award_query = select(Award).where(
        (Award.id == award_id) &
        (Award.client_id == client.id)
    )
    award_result = await db.execute(award_query)
    award = award_result.scalars().first()

    if not award:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="奖项不存在或不属于该客户"
        )

    # 删除奖项
    await db.delete(award)
    await db.commit()

    return None

# 技能相关API
@router.post("/{client_id}/skills", response_model=SkillResponse, status_code=status.HTTP_201_CREATED)
async def add_skill(
    db: DBSession,
    skill_data: SkillCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加技能

    Args:
        skill_data: 技能数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        SkillResponse: 创建的技能信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 创建技能（使用客户真实ID）
    skill = Skill(**skill_data.dict(), client_id=client.id)
    db.add(skill)
    await db.commit()
    await db.refresh(skill)

    return skill

@router.put("/{client_id}/skills/{skill_id}", response_model=SkillResponse, status_code=status.HTTP_200_OK)
async def update_skill(
    db: DBSession,
    skill_data: SkillUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    skill_id: int = Path(..., description="技能ID"),
    # current_user: CurrentUser = None,
):
    """
    更新技能

    Args:
        skill_data: 技能更新数据
        client_id: 客户哈希ID
        skill_id: 技能ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        SkillResponse: 更新后的技能信息
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询技能是否存在（使用客户真实ID）
    skill_query = select(Skill).where(
        (Skill.id == skill_id) &
        (Skill.client_id == client.id)
    )
    skill_result = await db.execute(skill_query)
    skill = skill_result.scalars().first()

    if not skill:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="技能不存在或不属于该客户"
        )

    # 更新技能信息
    update_data = skill_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(skill, key, value)

    await db.commit()
    await db.refresh(skill)

    return skill

@router.delete("/{client_id}/skills/{skill_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_skill(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    skill_id: int = Path(..., description="技能ID"),
    # current_user: CurrentUser = None,
):
    """
    删除技能

    Args:
        client_id: 客户哈希ID
        skill_id: 技能ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询技能是否存在（使用客户真实ID）
    skill_query = select(Skill).where(
        (Skill.id == skill_id) &
        (Skill.client_id == client.id)
    )
    skill_result = await db.execute(skill_query)
    skill = skill_result.scalars().first()

    if not skill:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="技能不存在或不属于该客户"
        )

    # 删除技能
    await db.delete(skill)
    await db.commit()

    return None

# 语言成绩相关API
@router.post("/{client_id}/language-scores", response_model=LanguageScoreResponse, status_code=status.HTTP_201_CREATED)
async def add_language_score(
    db: DBSession,
    language_score_data: LanguageScoreCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加语言成绩

    Args:
        language_score_data: 语言成绩数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        LanguageScoreResponse: 创建的语言成绩信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 创建语言成绩（使用客户真实ID）
    language_score = LanguageScore(**language_score_data.dict(), client_id=client.id)
    db.add(language_score)
    await db.commit()
    await db.refresh(language_score)

    return language_score

@router.put("/{client_id}/language-scores/{language_score_id}", response_model=LanguageScoreResponse, status_code=status.HTTP_200_OK)
async def update_language_score(
    db: DBSession,
    language_score_data: LanguageScoreUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    language_score_id: int = Path(..., description="语言成绩ID"),
    # current_user: CurrentUser = None,
):
    """
    更新语言成绩

    Args:
        language_score_data: 语言成绩更新数据
        client_id: 客户哈希ID
        language_score_id: 语言成绩ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        LanguageScoreResponse: 更新后的语言成绩信息
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询语言成绩是否存在（使用客户真实ID）
    language_score_query = select(LanguageScore).where(
        (LanguageScore.id == language_score_id) &
        (LanguageScore.client_id == client.id)
    )
    language_score_result = await db.execute(language_score_query)
    language_score = language_score_result.scalars().first()

    if not language_score:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="语言成绩不存在或不属于该客户"
        )

    # 更新语言成绩信息
    update_data = language_score_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(language_score, key, value)

    await db.commit()
    await db.refresh(language_score)

    return language_score

@router.delete("/{client_id}/language-scores/{language_score_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_language_score(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    language_score_id: int = Path(..., description="语言成绩ID"),
    # current_user: CurrentUser = None,
):
    """
    删除语言成绩

    Args:
        client_id: 客户哈希ID
        language_score_id: 语言成绩ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询语言成绩是否存在（使用客户真实ID）
    language_score_query = select(LanguageScore).where(
        (LanguageScore.id == language_score_id) &
        (LanguageScore.client_id == client.id)
    )
    language_score_result = await db.execute(language_score_query)
    language_score = language_score_result.scalars().first()

    if not language_score:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="语言成绩不存在或不属于该客户"
        )

    # 删除语言成绩
    await db.delete(language_score)
    await db.commit()

    return None

# 个人想法相关API
@router.post("/{client_id}/thoughts", response_model=ThoughtResponse, status_code=status.HTTP_201_CREATED)
async def add_thought(
    db: DBSession,
    thought_data: ThoughtCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加个人想法

    Args:
        thought_data: 个人想法数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ThoughtResponse: 创建的个人想法信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询是否已存在个人想法记录（使用客户真实ID）
    thought_query = select(Thought).where(Thought.client_id == client.id)
    thought_result = await db.execute(thought_query)
    existing_thought = thought_result.scalars().first()

    if existing_thought:
        # 如果已存在，则更新现有记录
        update_data = thought_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(existing_thought, key, value)
        await db.commit()
        await db.refresh(existing_thought)
        return existing_thought
    else:
        # 如果不存在，则创建新记录（使用客户真实ID）
        thought = Thought(**thought_data.dict(), client_id=client.id)
        db.add(thought)
        await db.commit()
        await db.refresh(thought)
        return thought

@router.put("/{client_id}/thoughts", response_model=ThoughtResponse, status_code=status.HTTP_200_OK)
async def update_thought(
    db: DBSession,
    thought_data: ThoughtUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    更新个人想法

    Args:
        thought_data: 个人想法更新数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ThoughtResponse: 更新后的个人想法信息
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询个人想法是否存在（使用客户真实ID）
    thought_query = select(Thought).where(Thought.client_id == client.id)
    thought_result = await db.execute(thought_query)
    thought = thought_result.scalars().first()

    if not thought:
        # 如果不存在，则创建新记录（使用客户真实ID）
        thought = Thought(**thought_data.dict(), client_id=client.id)
        db.add(thought)
    else:
        # 如果存在，则更新现有记录
        update_data = thought_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(thought, key, value)

    # 更新客户的modified_at时间
    from datetime import datetime
    client.updated_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(thought)

    return thought

@router.delete("/{client_id}/thoughts", status_code=status.HTTP_204_NO_CONTENT)
async def delete_thought(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    删除个人想法

    Args:
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询个人想法是否存在（使用客户真实ID）
    thought_query = select(Thought).where(Thought.client_id == client.id)
    thought_result = await db.execute(thought_query)
    thought = thought_result.scalars().first()

    if not thought:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="个人想法不存在或不属于该客户"
        )

    # 删除个人想法
    await db.delete(thought)
    
    # 更新客户的updated_at时间
    from datetime import datetime
    client.updated_at = datetime.utcnow()
    
    await db.commit()

    return None

# 背景自定义模块相关API
@router.post("/{client_id}/background-modules", response_model=CustomModuleResponse, status_code=status.HTTP_201_CREATED)
async def add_background_module(
    db: DBSession,
    module_data: CustomModuleCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加背景自定义模块

    Args:
        module_data: 背景模块数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        CustomModuleResponse: 创建的背景模块信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 创建背景模块（使用客户真实ID）
    background_module = BackgroundCustomModule(**module_data.dict(), client_id=client.id)
    db.add(background_module)
    await db.commit()
    await db.refresh(background_module)

    return background_module

@router.put("/{client_id}/background-modules/{module_id}", response_model=CustomModuleResponse, status_code=status.HTTP_200_OK)
async def update_background_module(
    db: DBSession,
    module_data: CustomModuleUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    module_id: int = Path(..., description="模块ID"),
    # current_user: CurrentUser = None,
):
    """
    更新背景自定义模块

    Args:
        module_data: 背景模块更新数据
        client_id: 客户哈希ID
        module_id: 模块ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        CustomModuleResponse: 更新后的背景模块信息
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询背景模块是否存在（使用客户真实ID）
    module_query = select(BackgroundCustomModule).where(
        (BackgroundCustomModule.id == module_id) &
        (BackgroundCustomModule.client_id == client.id)
    )
    module_result = await db.execute(module_query)
    background_module = module_result.scalars().first()

    if not background_module:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="背景模块不存在或不属于该客户"
        )

    # 更新背景模块信息
    update_data = module_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(background_module, key, value)

    await db.commit()
    await db.refresh(background_module)

    return background_module

@router.delete("/{client_id}/background-modules/{module_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_background_module(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    module_id: int = Path(..., description="模块ID"),
    # current_user: CurrentUser = None,
):
    """
    删除背景自定义模块

    Args:
        client_id: 客户哈希ID
        module_id: 模块ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询背景模块是否存在（使用客户真实ID）
    module_query = select(BackgroundCustomModule).where(
        (BackgroundCustomModule.id == module_id) &
        (BackgroundCustomModule.client_id == client.id)
    )
    module_result = await db.execute(module_query)
    background_module = module_result.scalars().first()

    if not background_module:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="背景模块不存在或不属于该客户"
        )

    # 删除背景模块
    await db.delete(background_module)
    await db.commit()

    return None

# 想法自定义模块相关API
@router.post("/{client_id}/thought-modules", response_model=CustomModuleResponse, status_code=status.HTTP_201_CREATED)
async def add_thought_module(
    db: DBSession,
    module_data: CustomModuleCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加想法自定义模块

    Args:
        module_data: 想法模块数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        CustomModuleResponse: 创建的想法模块信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 创建想法模块（使用客户真实ID）
    thought_module = ThoughtCustomModule(**module_data.dict(), client_id=client.id)
    db.add(thought_module)
    await db.commit()
    await db.refresh(thought_module)

    return thought_module

@router.put("/{client_id}/thought-modules/{module_id}", response_model=CustomModuleResponse, status_code=status.HTTP_200_OK)
async def update_thought_module(
    db: DBSession,
    module_data: CustomModuleUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    module_id: int = Path(..., description="模块ID"),
    # current_user: CurrentUser = None,
):
    """
    更新想法自定义模块

    Args:
        module_data: 想法模块更新数据
        client_id: 客户哈希ID
        module_id: 模块ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        CustomModuleResponse: 更新后的想法模块信息
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询想法模块是否存在（使用客户真实ID）
    module_query = select(ThoughtCustomModule).where(
        (ThoughtCustomModule.id == module_id) &
        (ThoughtCustomModule.client_id == client.id)
    )
    module_result = await db.execute(module_query)
    thought_module = module_result.scalars().first()

    if not thought_module:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="想法模块不存在或不属于该客户"
        )

    # 更新想法模块信息
    update_data = module_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(thought_module, key, value)
    
    # 更新客户的updated_at时间
    from datetime import datetime
    client.updated_at = datetime.utcnow()

    await db.commit()
    await db.refresh(thought_module)

    return thought_module

@router.delete("/{client_id}/thought-modules/{module_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_thought_module(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    module_id: int = Path(..., description="模块ID"),
    # current_user: CurrentUser = None,
):
    """
    删除想法自定义模块

    Args:
        client_id: 客户哈希ID
        module_id: 模块ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询想法模块是否存在（使用客户真实ID）
    module_query = select(ThoughtCustomModule).where(
        (ThoughtCustomModule.id == module_id) &
        (ThoughtCustomModule.client_id == client.id)
    )
    module_result = await db.execute(module_query)
    thought_module = module_result.scalars().first()

    if not thought_module:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="想法模块不存在或不属于该客户"
        )

    # 删除想法模块
    await db.delete(thought_module)
    
    # 更新客户的updated_at时间
    from datetime import datetime
    client.updated_at = datetime.utcnow()
    
    await db.commit()

    return None

# 定校书相关API
@router.get("/{client_id}/programs/", response_model=List[ClientProgramResponse], status_code=status.HTTP_200_OK)
async def get_client_programs(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    获取客户定校书列表 - 优化版本，解决N+1查询问题

    Args:
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        List[ClientProgramResponse]: 客户定校书列表
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询定校书（使用客户真实ID）- 包含项目详情
    program_query = select(ClientProgram).options(
        joinedload(ClientProgram.ai_selection_program)
    ).where(ClientProgram.client_id == client.id)
    program_result = await db.execute(program_query)
    programs = program_result.scalars().all()

    # 批量查询学校logo - 解决N+1查询问题
    from app.ai_selection.db.models import AISelectionAbroadSchool
    school_names = [cp.ai_selection_program.school_name_cn 
                    for cp in programs 
                    if cp.ai_selection_program and cp.ai_selection_program.school_name_cn]
    
    school_logo_map = {}
    if school_names:
        school_logo_query = select(
            AISelectionAbroadSchool.school_name_cn,
            AISelectionAbroadSchool.school_logo_url
        ).where(AISelectionAbroadSchool.school_name_cn.in_(school_names))
        school_logo_result = await db.execute(school_logo_query)
        school_logo_map = {row[0]: row[1] for row in school_logo_result.fetchall()}

    # 转换为字典并包含项目详情
    programs_with_details = []
    for cp in programs:
        cp_dict = cp.to_dict()
        if cp.ai_selection_program:
            # 使用预查询的logo数据
            school_logo_url = school_logo_map.get(cp.ai_selection_program.school_name_cn)

            # 添加项目详情信息
            program_details = {
                'id': cp.ai_selection_program.id,
                'school_name_cn': cp.ai_selection_program.school_name_cn,
                'school_name_en': cp.ai_selection_program.school_name_en,
                'school_qs_rank': cp.ai_selection_program.school_qs_rank,
                'school_region': cp.ai_selection_program.school_region,
                'program_name_cn': cp.ai_selection_program.program_name_cn,
                'program_name_en': cp.ai_selection_program.program_name_en,
                'program_category': cp.ai_selection_program.program_category,
                'degree': cp.ai_selection_program.degree,
                'program_website': cp.ai_selection_program.program_website,
                'gpa_requirements': cp.ai_selection_program.gpa_requirements,
                'language_requirements': cp.ai_selection_program.language_requirements,
                'application_requirements': cp.ai_selection_program.application_requirements,
                'program_tuition': cp.ai_selection_program.program_tuition,
                'enrollment_time': cp.ai_selection_program.enrollment_time,
                'program_duration': cp.ai_selection_program.program_duration,
                'school_logo_url': school_logo_url
            }
            cp_dict['program_details'] = program_details
        programs_with_details.append(cp_dict)

    return programs_with_details

@router.post("/{client_id}/programs", response_model=ClientProgramResponse, status_code=status.HTTP_201_CREATED)
async def add_client_program(
    db: DBSession,
    program_data: ClientProgramCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加客户定校书

    Args:
        program_data: 定校书数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ClientProgramResponse: 创建的定校书信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 检查是否已经添加过这个项目
    existing_query = select(ClientProgram).where(
        ClientProgram.client_id == client.id,
        ClientProgram.program_id == program_data.program_id
    )
    existing_result = await db.execute(existing_query)
    existing_program = existing_result.scalars().first()

    if existing_program:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该项目已添加到定校书中"
        )

    # 创建定校书记录（使用客户真实ID）
    client_program = ClientProgram(**program_data.dict(), client_id=client.id)
    db.add(client_program)
    await db.commit()
    await db.refresh(client_program)

    return client_program

@router.delete("/{client_id}/programs/{program_id}", status_code=status.HTTP_204_NO_CONTENT)
async def remove_client_program(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    program_id: int = Path(..., description="项目ID"),
    # current_user: CurrentUser = None,
):
    """
    从定校书中移除项目

    Args:
        client_id: 客户哈希ID
        program_id: 项目ID
        current_user: 当前用户
        db: 数据库会话
    """
    print(f"删除定校书项目请求: client_id={client_id}, program_id={program_id}")
    
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        print(f"客户不存在: {client_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    print(f"找到客户: id={client.id}, name={client.name}")

    # 查询定校书记录
    program_query = select(ClientProgram).where(
        ClientProgram.client_id == client.id,
        ClientProgram.program_id == program_id
    )
    program_result = await db.execute(program_query)
    client_program = program_result.scalars().first()

    if not client_program:
        print(f"定校书记录不存在: client_id={client.id}, program_id={program_id}")
        # 查询该客户的所有定校书记录用于调试
        all_programs_query = select(ClientProgram).where(ClientProgram.client_id == client.id)
        all_programs_result = await db.execute(all_programs_query)
        all_programs = all_programs_result.scalars().all()
        print(f"该客户现有定校书记录: {[cp.program_id for cp in all_programs]}")
        
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="定校书记录不存在"
        )

    print(f"找到定校书记录: id={client_program.id}, program_id={client_program.program_id}")

    # 删除定校书记录
    await db.delete(client_program)
    
    # 更新客户的updated_at时间
    from datetime import datetime
    client.updated_at = datetime.utcnow()
    
    await db.commit()

    print(f"成功删除定校书记录: client_id={client.id}, program_id={program_id}")
    return None

@router.get("/{client_id}/documents", response_model=List[ClientDocumentSummary])
async def get_client_documents(
    client_id: str,
    db: DBSession,
    # current_user: CurrentUser = None, # This line was removed as per the new_code, as the user dependency is no longer needed.
):
    """
    获取客户的文书列表（CV和RL）
    """
    try:
        # 查找客户
        client_query = select(Client).where(Client.id_hashed == client_id)
        result = await db.execute(client_query)
        client = result.scalar_one_or_none()
        
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="客户不存在"
            )
        
        # 并行获取CV和RL列表
        cv_query = select(AIWritingCV).where(AIWritingCV.client_id == client.id).order_by(AIWritingCV.created_at.desc())
        rl_query = select(AIWritingRL).where(AIWritingRL.client_id == client.id).order_by(AIWritingRL.created_at.desc())
        
        # 并行执行查询
        cv_result, rl_result = await asyncio.gather(
            db.execute(cv_query),
            db.execute(rl_query)
        )
        
        cvs = cv_result.scalars().all()
        rls = rl_result.scalars().all()
        
        documents = []
        
        # 添加CV文档
        for cv in cvs:
            documents.append({
                "id": cv.id,
                "type": "CV",
                "title": cv.version_name or "未命名CV",
                "target_major": cv.target_major,
                "content_preview": cv.content_markdown[:200] + "..." if cv.content_markdown and len(cv.content_markdown) > 200 else cv.content_markdown or "",
                "created_at": cv.created_at.isoformat() if cv.created_at else None,
                "updated_at": cv.updated_at.isoformat() if cv.updated_at else None
            })
        
        # 添加RL文档
        for rl in rls:
            documents.append({
                "id": rl.id,
                "type": "RL",
                "title": rl.version_name or "未命名推荐信",
                "target_major": None,  # RL没有target_major字段
                "recommender_name": rl.recommender_name,
                "content_preview": rl.content_markdown[:200] + "..." if rl.content_markdown and len(rl.content_markdown) > 200 else rl.content_markdown or "",
                "created_at": rl.created_at.isoformat() if rl.created_at else None,
                "updated_at": rl.updated_at.isoformat() if rl.updated_at else None
            })
        
        # 按创建时间排序（最新的在前）
        documents.sort(key=lambda x: x["created_at"] or "", reverse=True)
        
        return documents
        
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        # logger.error(f"获取客户文书列表失败: {e}") # This line was removed as per the new_code, as the logger is not defined.
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取文书列表失败"
        )