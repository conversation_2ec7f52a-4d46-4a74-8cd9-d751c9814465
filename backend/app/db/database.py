from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, declarative_base
from app.core.config import settings

# 创建异步引擎 (优化本地数据库性能)
engine = create_async_engine(
    settings.DATABASE_URL,
    echo=False,  # 设置为 True 会在控制台显示 SQL 语句，方便调试
    future=True,  # 使用 SQLAlchemy 2.0 特性
    
    # 连接池优化 - 大幅增加连接数以提高并发性能
    pool_size=50,  # 增加连接池大小至50以支持更多并发连接
    max_overflow=100,  # 增加最大溢出连接数至100
    pool_timeout=20,  # 减少获取连接的超时时间至20秒
    pool_recycle=3600,  # 连接回收时间(1小时)，防止连接被数据库服务器断开
    pool_pre_ping=True,  # 启用连接前ping，确保连接有效性
    
    connect_args={
        # 本地数据库连接配置 (无需SSL)
        # "ssl": "require",  # Supabase云端数据库需要SSL，本地数据库注释掉
        
        # 连接超时优化
        "command_timeout": 10,  # 减少命令超时时间至10秒
    }
)

# 创建异步会话工厂（性能优化配置）
AsyncSessionLocal = sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,  # 提交后不过期，允许在事务提交后仍使用对象
    autoflush=False,  # 禁用自动刷新，手动控制刷新时机以提高性能
    autocommit=False,  # 禁用自动提交，手动控制事务
)

# 创建所有模型的基类
Base = declarative_base()

# 创建异步获取数据库会话的依赖函数
async def get_db():
    """
    获取异步数据库会话的依赖函数，用于 FastAPI 依赖注入
    """
    session = AsyncSessionLocal()
    try:
        yield session
        # 如果没有异常，则提交事务
        await session.commit()
    except Exception as e:
        # 如果有异常，则回滚事务
        await session.rollback()
        raise e
    finally:
        # 最后关闭会话
        await session.close()