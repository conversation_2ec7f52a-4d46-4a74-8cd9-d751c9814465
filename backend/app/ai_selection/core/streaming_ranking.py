"""
流式推荐排序模块
实现边评分边推送的推荐策略，提升用户体验
"""

from typing import List, Dict, Any, AsyncGenerator
import asyncio
import time
import json
from collections import defaultdict

from app.ai_selection.schemas.user import EnhancedUserProfile
from app.ai_selection.schemas.recommendation import RecommendationItem, RecommendationScore
from app.ai_selection.utils.llm import generate_recommendation_reason_async
from app.ai_selection.utils.ranking_parser import get_ranking_sort_key
from app.ai_selection.core.program_matching import _process_single_candidate
from app.ai_selection.config import WEIGHTS, FINAL_RECOMMENDATIONS, PREFERENCE_WEIGHTS
from app.ai_selection.core.school_matching import calculate_school_ranking_scores

async def stream_recommendations(
    candidate_pool: List[Dict[str, Any]],
    user_profile: EnhancedUserProfile,
    similar_cases: List[Dict[str, Any]]
) -> AsyncGenerator[Dict[str, Any], None]:
    """
    流式生成推荐结果
    
    Args:
        candidate_pool: 候选池
        user_profile: 用户画像
        similar_cases: 相似案例列表
        
    Yields:
        流式推荐数据
    """
    total_candidates = len(candidate_pool)
    processed_count = 0
    matching_start_time = time.time()
    
    # 存储已完成评分的候选项目
    scored_candidates = []
    
    # 获取用户偏好权重
    preference_type = user_profile.preference_type or "balanced"
    current_weights = PREFERENCE_WEIGHTS.get(preference_type, WEIGHTS)
    
    # 发送用户偏好信息（用户主动选择）
    preference_descriptions = {
        "ranking_focused": "您选择了排名导向型，系统将优先推荐排名较高的院校",
        "major_focused": "您选择了专业导向型，系统将优先推荐专业匹配度高的项目",
        "balanced": "您选择了平衡型，系统将综合考虑排名和专业匹配度"
    }
    preference_reason = preference_descriptions.get(preference_type, "使用系统默认的平衡型策略")
    
    yield {
        "type": "user_preference_detected",
        "preference_type": preference_type,
        "reason": preference_reason
    }
    
    # 发送权重配置信息
    yield {
        "type": "weights_applied",
        "weights": current_weights
    }
    
    # 当前TOP10推荐
    current_top_recommendations = []
    
    yield {
        "type": "progress",
        "stage": "matching_start", 
        "message": f"开始评估 {total_candidates} 个候选专业的匹配度...",
        "total": total_candidates,
        "processed": 0,
        "start_time": matching_start_time
    }
    
    # 分批处理候选项目（每批5个）
    batch_size = 5
    for i in range(0, total_candidates, batch_size):
        batch = candidate_pool[i:i + batch_size]
        batch_start_time = time.time()
        
        # 并发处理当前批次
        batch_tasks = []
        for j, candidate in enumerate(batch):
            task = _process_single_candidate(
                candidate, user_profile, similar_cases, i + j
            )
            batch_tasks.append(task)
        
        # 等待批次完成
        batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
        
        # 处理批次结果
        for result in batch_results:
            if isinstance(result, Exception):
                print(f"候选专业处理失败: {result}")
                continue
                
            # 计算总分（暂时不包含排名评分，后面会重新计算）
            total_match = (
                current_weights["school_tier_match"] * result.get("school_tier_match", 0) +
                current_weights["program_direction_match"] * result.get("program_direction_match", 0) +
                current_weights["experience_match"] * result.get("experience_match", 0) +
                current_weights["academic_performance_match"] * result.get("academic_performance_match", 0)
            )
            result["total_match"] = total_match
            scored_candidates.append(result)
            processed_count += 1
        
        batch_time = time.time() - batch_start_time
        
        # 更新当前TOP10
        current_top_recommendations = _get_current_top_recommendations(scored_candidates)
        
        # 发送批次进度
        yield {
            "type": "progress",
            "stage": "matching_progress",
            "message": f"已评估 {processed_count}/{total_candidates} 个专业 (批次耗时: {batch_time:.2f}秒)",
            "total": total_candidates,
            "processed": processed_count,
            "batch_time": round(batch_time, 2),
            "batch_size": len(batch),
            "current_top_count": len(current_top_recommendations)
        }
        
        # 如果有足够的候选项目，发送当前TOP5预览
        if len(current_top_recommendations) >= 5:
            current_elapsed = time.time() - matching_start_time
            yield {
                "type": "preview",
                "stage": "top_preview",
                "message": f"当前TOP5预览 (已用时: {current_elapsed:.2f}秒)",
                "elapsed_time": round(current_elapsed, 2),
                "preview_schools": [
                    {
                        "rank": i + 1,
                        "school_name": rec.get("school_name_cn", "未知"),
                        "program_name": rec.get("program_name_cn", "未知"),
                        "qs_rank": rec.get("school_qs_rank", "未知"),
                        "total_match": round(rec.get("total_match", 0), 3)
                    }
                    for i, rec in enumerate(current_top_recommendations[:5])
                ]
            }
    
    # 评分完成，开始生成最终推荐
    matching_end_time = time.time()
    total_matching_time = matching_end_time - matching_start_time
    
    # 计算院校排名评分
    yield {
        "type": "progress",
        "stage": "ranking_calculation",
        "message": "计算院校排名评分...",
        "elapsed_time": round(total_matching_time, 2)
    }
    
    scored_candidates = await calculate_school_ranking_scores(scored_candidates)
    
    # 重新计算包含排名评分的总分
    for candidate in scored_candidates:
        total_match = (
            current_weights["school_tier_match"] * candidate.get("school_tier_match", 0) +
            current_weights["school_ranking_score"] * candidate.get("school_ranking_score", 0) +
            current_weights["program_direction_match"] * candidate.get("program_direction_match", 0) +
            current_weights["experience_match"] * candidate.get("experience_match", 0) +
            current_weights["academic_performance_match"] * candidate.get("academic_performance_match", 0)
        )
        candidate["total_match"] = total_match
    
    yield {
        "type": "progress",
        "stage": "matching_complete",
        "message": f"专业匹配评估完成，总耗时 {total_matching_time:.2f}秒，开始生成推荐理由...",
        "total_processed": processed_count,
        "matching_time": round(total_matching_time, 2),
        "avg_time_per_candidate": round(total_matching_time / max(processed_count, 1), 2)
    }
    
    # 最终所有专业评估完成，发送最终候选池信息（显示前20个）
    final_pool_candidates = sorted(scored_candidates, key=lambda x: x.get("total_match", 0), reverse=True)[:20]
    yield {
        "type": "candidate_pool_finalized",
        "stage": "final_pool",
        "message": f"所有 {len(scored_candidates)} 个候选专业评估完成，显示前20名候选池",
        "candidates": [{
            "rank": i + 1,
            "school_name_cn": c.get("school_name_cn", "未知"),
            "program_name_cn": c.get("program_name_cn", "未知"),
            "total_match": round(c.get("total_match", 0), 3),
            "school_tier_match": round(c.get("school_tier_match", 0), 3),
            "school_ranking_score": round(c.get("school_ranking_score", 0), 3),
            "program_direction_match": round(c.get("program_direction_match", 0), 3),
            "experience_match": round(c.get("experience_match", 0), 3),
            "academic_performance_match": round(c.get("academic_performance_match", 0), 3),
        } for i, c in enumerate(final_pool_candidates)]
    }

    # 获取最终TOP10并按QS排名排序
    final_top_candidates = _get_final_top_candidates(scored_candidates, user_profile)
    
    yield {
        "type": "progress",
        "stage": "ranking_complete",
        "message": f"选出TOP {len(final_top_candidates)} 个专业，按QS排名排序",
        "final_ranking": [
            {
                "rank": i + 1,
                "school_name": candidate.get("school_name_cn", "未知"),
                "qs_rank": candidate.get("school_qs_rank", "未知"),
                "total_match": round(candidate.get("total_match", 0), 3)
            }
            for i, candidate in enumerate(final_top_candidates)
        ]
    }
    
    # 流式生成推荐理由
    recommendation_count = 0
    async for recommendation_data in _stream_recommendation_generation(
        final_top_candidates, user_profile
    ):
        recommendation_count += 1
        
        # 添加进度信息
        recommendation_data.update({
            "current_count": recommendation_count,
            "total_count": len(final_top_candidates)
        })
        
        yield recommendation_data

def _get_current_top_recommendations(scored_candidates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """获取当前的TOP推荐"""
    if not scored_candidates:
        return []
    
    # 按总分排序
    sorted_candidates = sorted(
        scored_candidates, 
        key=lambda x: x.get("total_match", 0), 
        reverse=True
    )
    
    return sorted_candidates[:FINAL_RECOMMENDATIONS]

def _get_final_top_candidates(
    scored_candidates: List[Dict[str, Any]],
    user_profile: EnhancedUserProfile
) -> List[Dict[str, Any]]:
    """获取最终TOP候选 - 修改为简化逻辑：按总分取前10，然后按QS排名排序"""
    # 1. 应用排名偏好加分
    for candidate in scored_candidates:
        user_rank_prefs = user_profile.ranking_preference
        if user_rank_prefs:
            school_rank_str = candidate.get("school_qs_rank")
            from app.ai_selection.utils.ranking_parser import parse_qs_rank
            school_rank = parse_qs_rank(school_rank_str)
            if school_rank:
                for pref in user_rank_prefs:
                    try:
                        max_rank_pref = int(pref.split('-')[-1])
                        if school_rank <= max_rank_pref:
                            candidate["total_match"] = candidate.get("total_match", 0) + 0.05
                            break
                    except (ValueError, IndexError):
                        continue

    # 2. 按总分排序，取前10个（FINAL_RECOMMENDATIONS）
    sorted_by_score = sorted(
        scored_candidates, 
        key=lambda x: x.get("total_match", 0), 
        reverse=True
    )
    
    # 3. 直接取前10个，不再应用复杂的多样性限制
    top_candidates = sorted_by_score[:FINAL_RECOMMENDATIONS]
    
    # 4. 按QS排名重新排序用于显示
    top_candidates.sort(key=lambda x: get_ranking_sort_key(x.get("school_qs_rank", "")))
    
    return top_candidates

async def _stream_recommendation_generation(
    final_candidates: List[Dict[str, Any]],
    user_profile: EnhancedUserProfile
) -> AsyncGenerator[Dict[str, Any], None]:
    """流式生成推荐理由 - 并发优化版本"""
    
    generation_start_time = time.time()
    
    # 发送批量生成开始事件
    yield {
        "type": "recommendation_batch_start",
        "stage": "generating_reasons",
        "total_count": len(final_candidates),
        "message": f"开始并发生成 {len(final_candidates)} 个推荐理由...",
        "start_time": generation_start_time
    }
    
    tasks = []
    for i, candidate in enumerate(final_candidates):
        task = asyncio.create_task(
            _generate_single_recommendation_async(
                i, candidate, user_profile
            )
        )
        tasks.append(task)
    
    # 使用 asyncio.as_completed 实现并发执行 + 流式输出
    # 这样可以在任何一个推荐理由生成完成时立即返回，而不是等待所有完成
    completed_count = 0
    
    if not tasks:
        return

    for completed_task in asyncio.as_completed(tasks):
        try:
            recommendation_data = await completed_task
            completed_count += 1
            current_time = time.time()
            elapsed_time = current_time - generation_start_time
            
            # 添加完成进度信息
            recommendation_data.update({
                "completed_count": completed_count,
                "total_count": len(final_candidates),
                "progress_percent": round(completed_count / len(final_candidates) * 100, 1),
                "total_elapsed_time": round(elapsed_time, 2),
                "avg_time_per_recommendation": round(elapsed_time / completed_count, 2)
            })
            
            yield recommendation_data
            
        except Exception as e:
            completed_count += 1
            # 记录详细错误，方便调试
            print(f"生成推荐理由API调用失败: {e}")
            # 处理单个任务失败的情况
            yield {
                "type": "recommendation_error",
                "stage": "reason_generation_failed",
                "error": str(e),
                "message": f"推荐理由生成失败: {str(e)}",
                "completed_count": completed_count,
                "total_count": len(final_candidates),
                "progress_percent": round(completed_count / len(final_candidates) * 100, 1)
            }

async def _generate_single_recommendation_async(
    index: int,
    candidate: Dict[str, Any],
    user_profile: EnhancedUserProfile
) -> Dict[str, Any]:
    """异步生成单个推荐项目（用于并发执行）"""
    recommendation_start_time = time.time()
    
    try:
        # 准备数据
        school_info = {
            "name": candidate.get("school_name_cn") or candidate.get("school_name"),
            "name_cn": candidate.get("school_name_cn"),
            "name_en": candidate.get("school_name_en"),
            "qs_rank": candidate.get("school_qs_rank"),
            "tier": candidate.get("school_tier")
        }
        
        program_info = {
            "id": candidate.get("id"),
            "name": candidate.get("program_name_cn"),
            "name_en": candidate.get("program_name_en"),
            "discipline": candidate.get("program_direction")
        }
        
        scores = {
            "school_tier_match": candidate.get("school_tier_match", 0),
            "program_direction_match": candidate.get("program_direction_match", 0),
            "experience_match": candidate.get("experience_match", 0),
            "academic_performance_match": candidate.get("academic_performance_match", 0),
            "total_match": candidate.get("total_match", 0)
        }
        
        # [!关键修复] 使用与每个候选专业直接相关的案例，而不是全局案例列表
        candidate_specific_cases = candidate.get("matching_cases", [])
        
        # 异步生成推荐理由
        recommendation_reason = await generate_recommendation_reason_async(
            user_profile.dict(),
            school_info,
            program_info,
            scores,
            candidate_specific_cases
        )
        
        generation_time = time.time() - recommendation_start_time
        
        # 创建推荐项目
        recommendation = {
            "rank": index + 1,
            "school_name": school_info["name"],
            "school_tier": candidate.get("school_tier"),
            "region_name": candidate.get("school_region") or candidate.get("region_name"),
            "program_id": candidate.get("id"),
            "program_name_cn": candidate.get("program_name_cn"),
            "program_name_en": candidate.get("program_name_en"),
            "program_direction": candidate.get("program_direction"),
            "scores": scores,
            "recommendation_reason": recommendation_reason,
            "matching_cases_count": len(candidate_specific_cases),
            "generation_time": round(generation_time, 2)
        }
        
        # 返回完成的推荐数据
        return {
            "type": "recommendation_complete",
            "stage": "reason_generated",
            "rank": index + 1,
            "recommendation": recommendation,
            "generation_time": round(generation_time, 2),
            "message": f"第 {index + 1} 个推荐生成完成 (耗时: {generation_time:.2f}秒)",
            "school_name": candidate.get("school_name_cn", "未知"),
            "program_name": candidate.get("program_name_cn", "未知")
        }
        
    except Exception as e:
        # 生成失败时的处理
        return {
            "type": "recommendation_error",
            "stage": "reason_generation_failed",
            "rank": index + 1,
            "error": str(e),
            "message": f"第 {index + 1} 个推荐理由生成失败"
        }

async def stream_hard_filtered_results(
    hard_filtered_results: List[Dict[str, Any]],
    user_profile: EnhancedUserProfile
) -> AsyncGenerator[Dict[str, Any], None]:
    """
    流式返回硬筛选结果
    
    Args:
        hard_filtered_results: 硬筛选的结果列表
        user_profile: 用户画像
        
    Yields:
        流式硬筛选数据
    """
    total_results = len(hard_filtered_results)
    
    # 发送硬筛选模式开始事件
    yield {
        "type": "hard_filter_start",
        "message": f"硬筛选模式：根据您的留学意向筛选出 {total_results} 个匹配的专业",
        "mode": "hard_filter",
        "total_count": total_results
    }
    
    # 发送筛选条件信息
    yield {
        "type": "filter_criteria",
        "criteria": {
            "target_regions": user_profile.target_regions,
            "target_degree": user_profile.target_degree,
            "target_major_directions": user_profile.target_major_directions,
            "duration": user_profile.duration,
            "ranking_preference": user_profile.ranking_preference
        }
    }
    
    # 按批次发送结果（每批20个）
    batch_size = 20
    for i in range(0, total_results, batch_size):
        batch = hard_filtered_results[i:i + batch_size]
        
        # 格式化批次结果
        formatted_batch = []
        for j, result in enumerate(batch):
            formatted_result = {
                "rank": i + j + 1,
                "school_name_cn": result.get("school_name_cn", "未知"),
                "school_name_en": result.get("school_name_en", ""),
                "school_qs_rank": result.get("school_qs_rank", "未知"),
                "school_region": result.get("school_region", ""),
                "program_id": result.get("id"),
                "program_name_cn": result.get("program_name_cn", "未知"),
                "program_name_en": result.get("program_name_en", ""),
                "program_direction": result.get("program_direction", ""),
                "faculty": result.get("faculty", ""),
                "program_duration": result.get("program_duration", ""),
                "program_tuition": result.get("program_tuition", ""),
                "gpa_requirements": result.get("gpa_requirements"),
                "match_source": result.get("match_source", "database_filter")
            }
            formatted_batch.append(formatted_result)
        
        # 发送批次结果
        yield {
            "type": "hard_filter_batch",
            "stage": "results_batch",
            "batch_index": i // batch_size + 1,
            "total_batches": (total_results + batch_size - 1) // batch_size,
            "batch_start": i + 1,
            "batch_end": min(i + batch_size, total_results),
            "results": formatted_batch,
            "message": f"第 {i // batch_size + 1} 批结果 ({i + 1}-{min(i + batch_size, total_results)}/{total_results})"
        }
    
    # 发送完成事件
    yield {
        "type": "hard_filter_complete",
        "stage": "complete",
        "total_results": total_results,
        "message": f"硬筛选完成，共找到 {total_results} 个匹配的专业项目",
        "summary": {
            "exact_matches": len([r for r in hard_filtered_results if r.get("match_source") == "exact_match"]),
            "fuzzy_matches": len([r for r in hard_filtered_results if r.get("match_source") == "fuzzy_match"]),
            "related_matches": len([r for r in hard_filtered_results if r.get("match_source") == "related_match"])
        }
    } 