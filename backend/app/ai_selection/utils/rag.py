"""
RAG (Retrieval Augmented Generation) 功能实现
使用实际大模型API进行向量嵌入和检索
"""

import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from typing import List, Dict, Any, Tuple
import requests
import os
import json

from app.ai_selection.config import SILICONE_FLOW_API_KEY

class SiliconeFlowEmbedding:
    def __init__(self, api_key: str = SILICONE_FLOW_API_KEY):
        self.api_key = api_key
        self.base_url = "https://api.siliconflow.cn/v1"
        
    def get_embedding(self, input: str) -> np.ndarray:
        """获取单个文本的向量表示"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "Pro/BAAI/bge-m3",
            "input": input,
            "encoding_format": "float"
        }
        
        response = requests.post(
            f"{self.base_url}/embeddings",
            headers=headers,
            json=payload
        )
        
        if response.status_code != 200:
            raise Exception(f"API调用失败: {response.status_code}, 响应: {response.text}")
            
        # 检查响应内容是否为空
        if not response.text.strip():
            raise Exception("API返回空响应")
        
        # 尝试解析JSON
        try:
            response_json = response.json()
        except (ValueError, json.JSONDecodeError) as json_error:
            raise Exception(f"JSON解析失败: {json_error}, 响应内容: {response.text[:200]}")
        
        # 检查响应结构
        if not response_json.get("data") or not response_json["data"]:
            raise Exception(f"API响应格式异常: {response_json}")
        
        if not response_json["data"][0].get("embedding"):
            raise Exception(f"API响应中缺少embedding字段: {response_json}")
            
        embedding = response_json["data"][0]["embedding"]
        return np.array(embedding)

# 创建嵌入模型实例
embedding_model = SiliconeFlowEmbedding()

def embed_text(text: str) -> np.ndarray:
    """
    将文本转换为向量表示（实际API实现）
    
    Args:
        text: 输入文本
        
    Returns:
        包含嵌入向量的numpy数组
        
    Raises:
        Exception: 当API调用失败时抛出异常
    """
    try:
        return embedding_model.get_embedding(text)
    except Exception as e:
        print(f"嵌入API调用失败: {e}")
        # 抛出异常而不是返回随机值，这样可以明确知道哪些向量生成失败
        raise Exception(f"向量嵌入生成失败: {e}")

def vector_search(
    query_vector: np.ndarray, 
    document_vectors: List[np.ndarray], 
    texts: List[str], 
    top_k: int = 3
) -> List[Tuple[str, float]]:
    """
    基于向量相似度搜索最相关的文档
    
    Args:
        query_vector: 查询向量
        document_vectors: 文档向量列表
        texts: 原始文本列表
        top_k: 返回的最相关结果数量
        
    Returns:
        包含(文本, 相似度分数)的列表，按相似度降序排列
    """
    if not document_vectors:
        return []
    
    # 计算余弦相似度
    document_vectors_matrix = np.vstack(document_vectors)
    similarities = cosine_similarity([query_vector], document_vectors_matrix)[0]
    
    # 取前top_k个结果
    top_indices = np.argsort(similarities)[::-1][:top_k]
    return [(texts[i], float(similarities[i])) for i in top_indices]

async def match_program_with_experience_async(
    program_info: Dict[str, Any],
    user_experiences: str,
    top_similar_cases: List[Dict[str, Any]] = None
) -> Tuple[float, str]:
    """
    异步版本：评估专业与用户经历的匹配度
    
    Args:
        program_info: 专业信息
        user_experiences: 用户经历描述
        top_similar_cases: 相似案例列表（可选）
        
    Returns:
        匹配分数和匹配理由
    """
    # 准备提示词，让模型评估匹配度
    prompt = f"""
    请你作为留学选校顾问，评估学生的经历与专业的匹配度。

    专业信息:
    专业名称: {program_info.get('program_name_cn', '')}
    学科类别: {program_info.get('program_direction', '')}
    专业描述: {program_info.get('program_objectives', '')}

    学生经历:
    {user_experiences}

    请严格按照以下格式回答:
    1. 给出一个0到1之间的匹配度分数，其中0表示完全不匹配，1表示完全匹配
    2. 给出匹配度评价理由，不超过150个字
    
    答案格式:
    分数: [数字]
    理由: [评价理由]
    """
    
    try:
        # 调用异步大模型API获取回答
        from app.ai_selection.utils.llm import process_text_with_api_async
        response = await process_text_with_api_async(prompt)
        
        # 解析回答
        lines = response.strip().split('\n')
        score_line = next((line for line in lines if line.startswith("分数:")), None)
        reason_line = next((line for line in lines if line.startswith("理由:")), None)
        
        if score_line and reason_line:
            try:
                score = float(score_line.replace("分数:", "").strip())
                reason = reason_line.replace("理由:", "").strip()
                
                # 如果有相似案例，增加案例支持的理由
                if top_similar_cases:
                    matching_case_count = sum(1 for case in top_similar_cases 
                                            if case.get('offer_program_id') == program_info.get('id'))  # 更新字段名
                    if matching_case_count > 0:
                        reason += f" 另外，我们发现有{matching_case_count}个与您背景相似的学生成功申请了该专业。"
                
                return score, reason
            except ValueError:
                # 如果无法解析分数，使用备选方法
                pass
                
        # 如果API调用失败或解析失败，回退到关键词匹配方法
        return _fallback_keyword_match(program_info, user_experiences, top_similar_cases)
        
    except Exception as e:
        print(f"匹配评估API调用失败: {e}")
        return _fallback_keyword_match(program_info, user_experiences, top_similar_cases)

def match_program_with_experience(
    program_info: Dict[str, Any],
    user_experiences: str,
    top_similar_cases: List[Dict[str, Any]] = None
) -> Tuple[float, str]:
    """
    同步版本：评估专业与用户经历的匹配度（保留兼容性，但建议使用异步版本）
    
    Args:
        program_info: 专业信息
        user_experiences: 用户经历描述
        top_similar_cases: 相似案例列表（可选）
        
    Returns:
        匹配分数和匹配理由
    """
    # 准备提示词，让模型评估匹配度
    prompt = f"""
    请你作为留学选校顾问，评估学生的经历与专业的匹配度。

    专业信息:
    专业名称: {program_info.get('program_name_cn', '')}
    学科类别: {program_info.get('program_direction', '')}
    专业描述: {program_info.get('program_objectives', '')}

    学生经历:
    {user_experiences}

    请严格按照以下格式回答:
    1. 给出一个0到1之间的匹配度分数，其中0表示完全不匹配，1表示完全匹配
    2. 给出匹配度评价理由，不超过150个字
    
    答案格式:
    分数: [数字]
    理由: [评价理由]
    """
    
    try:
        # 调用大模型API获取回答
        from app.ai_selection.utils.llm import process_text_with_api
        response = process_text_with_api(prompt)
        
        # 解析回答
        lines = response.strip().split('\n')
        score_line = next((line for line in lines if line.startswith("分数:")), None)
        reason_line = next((line for line in lines if line.startswith("理由:")), None)
        
        if score_line and reason_line:
            try:
                score = float(score_line.replace("分数:", "").strip())
                reason = reason_line.replace("理由:", "").strip()
                
                # 如果有相似案例，增加案例支持的理由
                if top_similar_cases:
                    matching_case_count = sum(1 for case in top_similar_cases 
                                            if case.get('offer_program_id') == program_info.get('id'))  # 更新字段名
                    if matching_case_count > 0:
                        reason += f" 另外，我们发现有{matching_case_count}个与您背景相似的学生成功申请了该专业。"
                
                return score, reason
            except ValueError:
                # 如果无法解析分数，使用备选方法
                pass
                
        # 如果API调用失败或解析失败，回退到关键词匹配方法
        return _fallback_keyword_match(program_info, user_experiences, top_similar_cases)
        
    except Exception as e:
        print(f"匹配评估API调用失败: {e}")
        return _fallback_keyword_match(program_info, user_experiences, top_similar_cases)

def _fallback_keyword_match(
    program_info: Dict[str, Any],
    user_experiences: str,
    top_similar_cases: List[Dict[str, Any]] = None
) -> Tuple[float, str]:
    """
    当API调用失败时的备选关键词匹配方法。
    根据专业方向与结构化的用户经历进行匹配。
    """
    # 1. 定义专业领域与相关经历的映射关系
    # 键是程序方向中可能出现的关键词，值是format_strength_as_text生成的相关经历文本
    program_experience_relevance = {
        "计算机": ["互联网大厂实习", "国家级竞赛获奖", "国际级竞赛获奖", "sci刊物发表论文"],
        "人工智能": ["互联网大厂实习", "国家级竞赛获奖", "国际级竞赛获奖", "sci刊物发表论文", "核心刊物发表论文"],
        "数据科学": ["互联网大厂实习", "头部金融机构实习", "国家级竞赛获奖", "国际级竞赛获奖", "sci刊物发表论文"],
        "软件工程": ["互联网大厂实习", "国家级竞赛获奖", "国际级竞赛获奖"],
        "电子": ["世界500强实习", "国家级竞赛获奖", "国际级竞赛获奖", "sci刊物发表论文"],
        "通信": ["世界500强实习", "互联网大厂实习", "国家级竞赛获奖", "国际级竞赛获奖"],
        "金融": ["头部金融机构实习", "世界500强实习", "互联网大厂实习", "国家级竞赛获奖"],
        "商业分析": ["互联网大厂实习", "头部金融机构实习", "世界500强实习"],
        "经济": ["头部金融机构实习", "世界500强实习"],
        "管理": ["世界500强实习", "头部金融机构实习", "互联网大厂实习"],
        "市场营销": ["互联网大厂实习", "世界500强实习"],
        "会计": ["头部金融机构实习", "世界500强实习"],
        "研究": ["sci刊物发表论文", "核心刊物发表论文", "国际级竞赛获奖", "国家级竞赛获奖"],
        "工程": ["国家级竞赛获奖", "国际级竞赛获奖", "世界500强实习", "sci刊物发表论文"],
    }

    program_direction = program_info.get('program_direction', '').lower()
    
    # 2. 根据专业方向，找出所有相关的经历要求
    relevant_experiences = []
    for area_keyword, experiences in program_experience_relevance.items():
        if area_keyword in program_direction:
            relevant_experiences.extend(experiences)
    
    # 如果没有定义相关经历，或者用户没有经历，则返回中性分数
    if not relevant_experiences or not user_experiences:
        return 0.5, "无法通过关键词确定经历与专业的特定关联度，建议使用AI智能匹配以获得更精确评估。"

    # 3. 计算匹配上的经历数量
    matched_experiences = []
    # 去重以防一个经历匹配多个领域关键词
    for experience in set(relevant_experiences):
        if experience.lower() in user_experiences.lower():
            matched_experiences.append(experience)

    # 4. 计算分数
    # 基础分0.3，每个匹配项加分，但总分不超过1.0
    # 匹配的权重可以根据经历的含金量调整，这里简化处理
    score = 0.3 + len(matched_experiences) * 0.25
    match_score = min(1.0, score)

    # 5. 生成匹配理由
    if match_score > 0.8:
        reason = f"您的经历（如{'、'.join(matched_experiences)}）与{program_info.get('program_name_cn')}专业的核心要求高度相关。"
    elif match_score > 0.5:
        reason = f"您的部分经历（如{'、'.join(matched_experiences)}）与{program_info.get('program_name_cn')}专业有较好的关联度。"
    else:
        reason = f"您的经历与{program_info.get('program_name_cn')}专业的匹配度一般。可能需要补充与该领域更相关的实习或科研项目。"
    
    # 6. 如果有相似案例，增加案例支持的理由
    if top_similar_cases:
        matching_case_count = sum(1 for case in top_similar_cases 
                                if case.get('offer_program_id') == program_info.get('id'))
        if matching_case_count > 0:
            reason += f" 另外，我们发现有{matching_case_count}个与您背景相似的学生成功申请了该专业。"
    
    return match_score, reason 