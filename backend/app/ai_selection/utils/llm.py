"""
LLM (Large Language Model) 功能实现
使用实际大模型API进行文本生成和分析
"""

from typing import Dict, Any, List, Tuple
import requests
import asyncio
from concurrent.futures import ThreadPoolExecutor
import time

from app.ai_selection.config import SILICONE_FLOW_API_KEY, ALIBABACLOUD_API_KEY_ai_selection

# 创建并发限制信号量，避免API过载
API_SEMAPHORE = asyncio.Semaphore(5)  # 降低到5个并发请求，避免API限流

# 线程池执行器，用于同步API调用的异步化
executor = ThreadPoolExecutor(max_workers=5)  # 同样降低线程池大小

async def process_text_with_api_async(prompt: str) -> str:
    """
    异步调用大模型API处理文本，并返回结果
    
    Args:
        prompt: 提示词
        
    Returns:
        模型生成的回复
    """
    async with API_SEMAPHORE:  # 限制并发数量
        loop = asyncio.get_event_loop()
        # 在线程池中执行同步API调用
        try:
            result = await loop.run_in_executor(executor, process_text_with_api, prompt)
            return result
        except Exception as e:
            print(f"异步API调用失败: {e}")
            return "API调用失败，请稍后重试。"

def process_text_with_api(prompt: str) -> str:
    """
    调用大模型API处理文本，并返回结果（同步版本，仅供线程池调用）
    
    Args:
        prompt: 提示词
        
    Returns:
        模型生成的回复
    """
    # url = "https://api.siliconflow.cn/v1/chat/completions"

    # payload = {
    #     "model": "Qwen/Qwen3-14B",
    #     "messages": [
    #         {
    #             "role": "user",
    #             "content": prompt
    #         }
    #     ],
    #     "stream": False,
    #     "max_tokens": 512,
    #     "stop": None,
    #     "temperature": 0.3,
    #     "top_p": 0.7,
    #     "top_k": 50,
    #     "frequency_penalty": 0.5,
    #     "chat_template_kwargs": {"enable_thinking": False},
    #     "n": 1,
    #     "response_format": {"type": "text"}
    # }
    # headers = {
    #     "Authorization": f"Bearer {SILICONE_FLOW_API_KEY}",
    #     "Content-Type": "application/json"
    # }

    url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"

    payload = {
        "model": "qwen3-1.7b",
        "messages": [
            {
                "role": "user",
                "content": prompt
            }
        ],
        "stream": False,
        "max_tokens": 512,
        "stop": None,
        "temperature": 0.3,
        "top_p": 0.7,
        "top_k": 50,
        "enable_thinking": False,
        "n": 1,
        "response_format": {"type": "text"}
    }
    headers = {
        "Authorization": f"Bearer {ALIBABACLOUD_API_KEY_ai_selection}",
        "Content-Type": "application/json"
    }

    try:
        # 添加超时设置：连接超时5秒，读取超时30秒
        response = requests.request("POST", url, json=payload, headers=headers, timeout=(5, 10))
        
        # 检查HTTP状态码
        if response.status_code != 200:
            print(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
            return "API调用失败，请稍后重试。"
        
        # 检查响应内容是否为空
        if not response.text.strip():
            print("API返回空响应")
            return "API调用失败，请稍后重试。"
        
        # 尝试解析JSON
        try:
            response_json = response.json()
        except ValueError as json_error:
            print(f"JSON解析失败: {json_error}, 响应内容: {response.text[:200]}")
            return "API调用失败，请稍后重试。"
        
        # 检查响应结构
        if not response_json.get("choices"):
            print(f"API响应格式异常: {response_json}")
            return "API调用失败，请稍后重试。"
        
        content = response_json.get("choices")[0].get("message", {}).get("content", "")
        if not content:
            print("API返回空内容")
            return "API调用失败，请稍后重试。"
        
        return content
    except requests.exceptions.Timeout:
        print("API调用超时")
        return "API调用超时，请稍后重试。"
    except requests.exceptions.ConnectionError:
        print("API连接失败")
        return "网络连接失败，请检查网络后重试。"
    except Exception as e:
        print(f"API调用异常: {e}")
        return "API调用失败，请稍后重试。"

async def extract_key_entities_from_experience_async(experience: str) -> List[str]:
    """
    异步版本：从用户经历中提取关键实体
    
    Args:
        experience: 用户经历描述
        
    Returns:
        提取的关键实体列表
    """
    if not experience:
        return []
    
    prompt = f"""
    请分析以下学生的经历描述，提取其中包含的关键技能、经验类型和领域知识。
    返回一个标签列表，每个标签应该是简短的名词短语，表示一种技能、经验类型或领域知识。
    
    经历描述:
    {experience}
    
    请直接列出标签，每行一个，不要有编号或其他格式。例如:
    研究经验
    Python编程
    机器学习
    """
    
    try:
        response = await process_text_with_api_async(prompt)
        # 解析响应，获取实体列表
        entities = [line.strip() for line in response.strip().split('\n') if line.strip()]
        return entities
    except Exception as e:
        print(f"提取实体API调用失败: {e}")
        # 回退到关键词匹配方法
        return _fallback_entity_extraction(experience)

def _fallback_entity_extraction(experience: str) -> List[str]:
    """API调用失败时的备选实体提取方法"""
    entities = []
    
    # 简单的关键词匹配
    if "研究" in experience:
        entities.append("研究经验")
    if "实习" in experience:
        entities.append("实习经验")
    
    domain_keywords = {
        # 计算机与信息技术类专业的相关领域
        "计算机": ["计算机科学", "软件工程", "人工智能", "数据科学", "信息技术", "网络工程", "信息安全"],
        "软件": ["计算机科学", "软件工程", "信息技术", "系统工程", "数据科学"],
        "信息": ["计算机科学", "信息技术", "数据科学", "信息系统", "信息管理"],
        "人工智能": ["计算机科学", "数据科学", "机器学习", "认知科学", "统计学", "数学"],
        "数据科学": ["计算机科学", "统计学", "数学", "商业分析", "人工智能", "经济学"],
        "网络": ["网络工程", "信息安全", "计算机科学", "通信工程", "电子工程"],
        
        # 工程技术类专业的相关领域
        "电子": ["电子工程", "电气工程", "通信工程", "微电子", "计算机科学", "物理学"],
        "电气": ["电气工程", "电子工程", "自动化", "控制工程", "机械工程"],
        "通信": ["通信工程", "电子工程", "信息工程", "网络工程", "计算机科学"],
        "机械": ["机械工程", "自动化", "制造工程", "工业工程", "材料科学", "物理学"],
        "土木": ["土木工程", "建筑工程", "环境工程", "交通工程", "水利工程", "材料科学"],
        "材料": ["材料科学", "化学工程", "物理学", "化学", "机械工程", "电子工程"],
        "化工": ["化学工程", "化学", "材料科学", "环境工程", "生物工程", "石油工程"],
        "航空": ["航空工程", "航天工程", "机械工程", "材料科学", "物理学", "控制工程"],
        "生物工程": ["生物医学工程", "生物技术", "医学工程", "化学工程", "生物科学"],
        "环境": ["环境工程", "化学工程", "生态学", "地理科学", "公共卫生", "土木工程"],
        
        # 理学类专业的相关领域
        "数学": ["数学", "统计学", "数据科学", "金融数学", "计算机科学", "物理学"],
        "统计": ["统计学", "数学", "数据科学", "经济学", "心理学", "公共卫生"],
        "物理": ["物理学", "材料科学", "工程物理", "数学", "天文学", "地球科学"],
        "化学": ["化学", "材料科学", "化学工程", "生物化学", "药学", "环境科学"],
        "生物": ["生物科学", "生物技术", "生物医学", "医学", "环境科学", "农学"],
        "心理": ["心理学", "认知科学", "教育学", "社会学", "医学", "统计学"],
        "地理": ["地理科学", "环境科学", "测绘工程", "城市规划", "地质学", "生态学"],
        "地质": ["地质学", "地球科学", "环境科学", "地理科学", "材料科学", "物理学"],
        
        # 商科与经济类专业的相关领域
        "金融": ["金融", "经济学", "投资学", "风险管理", "数学", "统计学", "会计学"],
        "经济": ["经济学", "金融", "国际贸易", "统计学", "数学", "政治学", "社会学"],
        "管理": ["管理学", "工商管理", "人力资源", "心理学", "经济学", "社会学"],
        "会计": ["会计学", "金融", "经济学", "管理学", "数学", "统计学"],
        "市场": ["市场营销", "心理学", "统计学", "管理学", "传播学", "设计学"],
        "物流": ["物流管理", "供应链管理", "运筹学", "管理学", "工业工程", "经济学"],
        "商业": ["商业分析", "数据科学", "统计学", "管理学", "经济学", "计算机科学"],
        
        # 医学与健康类专业的相关领域
        "医学": ["临床医学", "基础医学", "生物医学", "生物科学", "化学", "心理学"],
        "护理": ["护理学", "医学", "心理学", "社会学", "管理学", "公共卫生"],
        "药学": ["药学", "化学", "生物科学", "医学", "化学工程", "生物工程"],
        "公共卫生": ["公共卫生", "医学", "统计学", "社会学", "环境科学", "管理学"],
        "康复": ["康复治疗", "医学", "心理学", "运动科学", "生物医学工程"],
        
        # 人文社科类专业的相关领域
        "教育": ["教育学", "心理学", "管理学", "社会学", "哲学", "数字媒体"],
        "法学": ["法学", "政治学", "社会学", "经济学", "哲学", "管理学"],
        "社会": ["社会学", "心理学", "政治学", "人类学", "教育学", "管理学"],
        "新闻": ["新闻学", "传播学", "社会学", "政治学", "文学", "数字媒体"],
        "语言": ["语言学", "文学", "教育学", "心理学", "传播学", "计算机科学"],
        "历史": ["历史学", "文学", "哲学", "考古学", "社会学", "政治学"],
        "哲学": ["哲学", "逻辑学", "心理学", "社会学", "政治学", "宗教学"],
        "政治": ["政治学", "国际关系", "法学", "经济学", "社会学", "历史学"],
        
        # 艺术设计类专业的相关领域
        "设计": ["设计学", "艺术学", "计算机科学", "心理学", "工程学", "市场营销"],
        "建筑": ["建筑学", "城市规划", "土木工程", "艺术学", "环境科学", "历史学"],
        "艺术": ["艺术学", "设计学", "文学", "历史学", "心理学", "教育学"],
        "传媒": ["传播学", "艺术学", "计算机科学", "心理学", "社会学", "市场营销"],
        
        # 农业与食品类专业的相关领域
        "农业": ["农学", "生物科学", "环境科学", "化学", "经济学", "管理学"],
        "食品": ["食品科学", "化学", "生物科学", "营养学", "化学工程", "管理学"],
        "林业": ["林学", "生态学", "环境科学", "生物科学", "地理科学", "管理学"],
        "水产": ["水产科学", "生物科学", "环境科学", "海洋科学", "食品科学"],
        
        # 其他交叉学科专业的相关领域
        "体育": ["体育学", "医学", "心理学", "教育学", "管理学", "康复治疗"],
        "旅游": ["旅游管理", "管理学", "经济学", "地理科学", "心理学", "市场营销"],
        "能源": ["能源工程", "物理学", "化学工程", "环境工程", "机械工程", "电气工程"],
        
        # 新兴交叉领域
        "金融科技": ["金融", "计算机科学", "数据科学", "数学", "统计学", "经济学"],
        "生物信息": ["生物科学", "计算机科学", "数学", "统计学", "医学", "化学"],
        "智能制造": ["机械工程", "计算机科学", "自动化", "工业工程", "材料科学"],
        "数字媒体": ["计算机科学", "艺术学", "传播学", "心理学", "市场营销", "设计学"],
        "可持续发展": ["环境科学", "经济学", "管理学", "工程学", "政治学", "社会学"]
    }
    
    for domain, keywords in domain_keywords.items():
        for kw in keywords:
            if kw in experience:
                entities.append(f"{domain}领域技能")
                break
    
    # 通用技能
    if any(kw in experience for kw in ["项目", "设计", "系统"]):
        entities.append("项目经验")
    if any(kw in experience for kw in ["竞赛", "比赛", "获奖", "奖项"]):
        entities.append("竞赛获奖")
    
    return entities

async def enhance_user_profile_async(basic_profile: Dict[str, Any], experiences: str) -> Dict[str, Any]:
    """
    异步版本：增强用户画像，添加从非结构化描述中提取的信息
    
    Args:
        basic_profile: 基本用户信息
        experiences: 用户经历描述
        
    Returns:
        增强后的用户画像
    """
    enhanced_profile = basic_profile.copy()
    
    # 提取关键实体和技能（异步）
    key_entities = await extract_key_entities_from_experience_async(experiences)
    enhanced_profile["extracted_skills"] = key_entities
    
    # 使用大模型推断兴趣领域和学术潜力（异步）
    if experiences:
        prompt = f"""
        请分析以下学生的背景信息和经历，推断:
        1. 该学生最可能感兴趣的学术领域
        2. 该学生的学术潜力评级（高/中等偏上/中等）
        
        学生背景:
        本科学校: {basic_profile.get("undergraduate_school", "")}
        本科专业: {basic_profile.get("undergraduate_major", "")}
        GPA: {basic_profile.get("gpa", "")}

        留学意向:
        意向地区: {basic_profile.get("target_regions", "")}
        意向专业领域: {basic_profile.get("target_major_direction", "")}
        
        学生经历:
        {experiences}
        
        请按以下格式回答:
        兴趣领域: [领域名称]
        学术潜力: [评级]
        """
        
        try:
            response = await process_text_with_api_async(prompt)
            
            # 解析响应
            lines = response.strip().split('\n')
            interest_line = next((line for line in lines if line.startswith("兴趣领域:")), None)
            potential_line = next((line for line in lines if line.startswith("学术潜力:")), None)
            
            if interest_line:
                enhanced_profile["inferred_interest_domain"] = interest_line.replace("兴趣领域:", "").strip()
            if potential_line:
                enhanced_profile["academic_potential"] = potential_line.replace("学术潜力:", "").strip()
            
            # 如果解析失败，使用备选方法
            if not enhanced_profile.get("inferred_interest_domain") or not enhanced_profile.get("academic_potential"):
                fallback_results = _fallback_profile_enhancement(basic_profile, experiences, key_entities)
                if not enhanced_profile.get("inferred_interest_domain"):
                    enhanced_profile["inferred_interest_domain"] = fallback_results.get("inferred_interest_domain")
                if not enhanced_profile.get("academic_potential"):
                    enhanced_profile["academic_potential"] = fallback_results.get("academic_potential")
        
        except Exception as e:
            print(f"增强用户画像API调用失败: {e}")
            # 使用备选方法
            fallback_results = _fallback_profile_enhancement(basic_profile, experiences, key_entities)
            enhanced_profile["inferred_interest_domain"] = fallback_results.get("inferred_interest_domain")
            enhanced_profile["academic_potential"] = fallback_results.get("academic_potential")
    
    else:
        # 如果没有经历信息，使用备选方法
        fallback_results = _fallback_profile_enhancement(basic_profile, "", key_entities)
        enhanced_profile["inferred_interest_domain"] = fallback_results.get("inferred_interest_domain")
        enhanced_profile["academic_potential"] = fallback_results.get("academic_potential")
    
    return enhanced_profile

def _fallback_profile_enhancement(
    basic_profile: Dict[str, Any], 
    experiences: str, 
    key_entities: List[str]
) -> Dict[str, Any]:
    """API调用失败时的备选用户画像增强方法"""
    result = {}
    
    # 推断主要兴趣领域
    major = basic_profile.get("undergraduate_major", "").lower()
    interest_domain = None
    
    if any(kw in major for kw in ["计算机", "软件", "信息"]):
        interest_domain = "计算机科学与技术"
    elif any(kw in major for kw in ["电子", "电气", "通信"]):
        interest_domain = "电子与电气工程"
    elif any(kw in major for kw in ["数学", "统计"]):
        interest_domain = "数学与统计"
    elif any(kw in major for kw in ["经济", "金融", "商业", "管理"]):
        interest_domain = "商业与经济"
    else:
        # 从经历中推断
        if "计算机领域技能" in key_entities:
            interest_domain = "计算机科学与技术"
        elif "电子领域技能" in key_entities:
            interest_domain = "电子与电气工程"
        elif "数学领域技能" in key_entities:
            interest_domain = "数学与统计"
        elif "商业领域技能" in key_entities:
            interest_domain = "商业与经济"
    
    if interest_domain:
        result["inferred_interest_domain"] = interest_domain
    
    # 评估学术潜力
    gpa = float(basic_profile.get("gpa", 0))
    has_research = "研究经验" in key_entities
    
    if gpa > 85 and has_research:
        academic_potential = "高"
    elif gpa > 80 or has_research:
        academic_potential = "中等偏上"
    else:
        academic_potential = "中等"
    
    result["academic_potential"] = academic_potential
    
    return result

async def generate_recommendation_reason_async(
    user_profile: Dict[str, Any],
    school_info: Dict[str, Any],
    program_info: Dict[str, Any],
    scores: Dict[str, float],
    similar_cases: List[Dict[str, Any]] = None
) -> str:
    """
    异步版本：生成个性化推荐理由
    
    Args:
        user_profile: 用户画像
        school_info: 学校信息
        program_info: 专业信息
        scores: 各维度评分
        similar_cases: 相似案例列表（可选）
        
    Returns:
        推荐理由文本
    """
    # 准备提示词
    prompt = f"""
    请你作为留学选校顾问，为学生生成一段有说服力的院校专业推荐理由。
    
    学生信息:
    本科学校: {user_profile.get('undergraduate_school', '')}
    本科学校层级: {user_profile.get('undergraduate_school_tier', '')}
    本科专业: {user_profile.get('undergraduate_major', '')}
    GPA: {user_profile.get('gpa', '')}
    
    推荐院校与专业:
    学校名称: {school_info.get('name', '')}
    学校层级: {school_info.get('tier', '')}
    专业名称: {program_info.get('name', '')}
    专业学科: {program_info.get('discipline', '')}
    
    匹配评分:
    院校层级匹配分: {scores.get('school_tier_match', 0)*10:.1f}/10
    专业方向契合分: {scores.get('program_direction_match', 0)*10:.1f}/10
    经历匹配分: {scores.get('experience_match', 0)*10:.1f}/10
    学术表现匹配分: {scores.get('academic_performance_match', 0)*10:.1f}/10
    总匹配分: {scores.get('total_match', 0)*10:.1f}/10
    
    相似案例数量: {len(similar_cases) if similar_cases else 0}
    
    请根据以上信息，生成一段不超过200字的推荐理由，包括:
    1. 学校匹配度评价
    2. 专业方向契合度评价
    3. 经历与专业的相关性
    4. 如果有相似案例支持，请简要提及
    
    回答格式:
    [直接输出推荐理由，不要包含任何前缀]
    """
    
    try:
        return await process_text_with_api_async(prompt)
    except Exception as e:
        print(f"生成推荐理由API调用失败: {e}")
        # 回退到模板式推荐理由
        return _fallback_recommendation_reason(user_profile, school_info, program_info, scores, similar_cases)

def _fallback_recommendation_reason(
    user_profile: Dict[str, Any],
    school_info: Dict[str, Any],
    program_info: Dict[str, Any],
    scores: Dict[str, float],
    similar_cases: List[Dict[str, Any]] = None
) -> str:
    """API调用失败时的备选推荐理由生成方法"""
    # 组装各部分内容
    school_tier_match = scores.get("school_tier_match", 0)
    program_match = scores.get("program_direction_match", 0)
    experience_match = scores.get("experience_match", 0)
    
    # 生成学校匹配部分
    if school_tier_match > 0.8:
        school_part = f"{school_info.get('name')}的录取难度与您的背景非常匹配，是适合您申请的目标。"
    elif school_tier_match > 0.5:
        school_part = f"{school_info.get('name')}对于您来说是具有一定挑战性但可达的目标，建议作为冲刺选择。"
    else:
        school_part = f"{school_info.get('name')}对于您当前的背景可能有一定难度，如果申请需要同时考虑更多保底选择。"
    
    # 生成专业匹配部分
    if program_match > 0.8:
        program_part = f"您的背景与{program_info.get('name')}专业方向高度相关，专业匹配度很高。"
    elif program_match > 0.5:
        program_part = f"您的背景与{program_info.get('name')}专业方向有一定的关联性，可以考虑申请。"
    else:
        program_part = f"您的背景与{program_info.get('name')}专业的相关性不是特别强，可能需要在申请材料中特别说明您的兴趣和转专业的动机。"
    
    # 生成经历匹配部分
    if experience_match > 0.8:
        experience_part = f"您的实习和项目经历非常符合该专业的要求，是很有竞争力的申请者。"
    elif experience_match > 0.5:
        experience_part = f"您有一些与该专业相关的经历，这将对您的申请有所帮助。"
    else:
        experience_part = f"建议您在申请前尝试获取更多与该专业相关的实践经验，以增强申请竞争力。"
    
    # 生成案例支持部分
    case_part = ""
    if similar_cases:
        # 在新的数据结构中，案例直接通过offer_program_id关联到专业
        matching_cases = [case for case in similar_cases 
                          if case.get("offer_program_id") == program_info.get("id")]
        if matching_cases:
            case_part = f"\n\n数据显示，我们有{len(matching_cases)}个与您背景类似的学生成功获得了该校该专业的录取，这进一步支持了我们的推荐。"
    
    # 组合完整推荐理由
    recommendation_reason = f"{school_part} {program_part} {experience_part}{case_part}"
    
    return recommendation_reason 