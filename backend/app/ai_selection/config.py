"""AI选校系统配置"""
from app.core.config import settings

# 系统配置
CANDIDATE_POOL_SIZE = 30  # 初始候选池大小
FINAL_RECOMMENDATIONS = 15  # 最终推荐数量
GPA_TOLERANCE = 0.05  # GPA匹配容差范围 (±5%)
MIN_CASE_THRESHOLD = 5  # 最小案例数阈值，低于此值时启用RAG补充

# 多样性控制配置
MAX_PROGRAMS_PER_SCHOOL = 3  # 每所学校最多推荐的专业数量，避免结果过于集中

# 向量检索配置
VECTOR_SIMILARITY_THRESHOLD = 0.5  # 向量检索的相似度阈值，低于此值的专业将被过滤

# 评分权重 - 支持动态权重调整
WEIGHTS = {
    "school_tier_match": 0.3,  # 院校层级匹配权重（申请难度）
    "school_ranking_score": 0.2,  # 院校排名评分权重（排名吸引力）
    "program_direction_match": 0.3,  # 专业方向匹配权重
    "experience_match": 0.15,  # 经历匹配权重
    "academic_performance_match": 0.05  # 学术表现匹配权重
}

# 动态权重配置 - 根据申请者偏好调整
PREFERENCE_WEIGHTS = {
    "ranking_focused": {  # 排名导向型申请者
        "school_tier_match": 0.2,
        "school_ranking_score": 0.4,  # 提高排名权重
        "program_direction_match": 0.2,  # 降低专业权重
        "experience_match": 0.15,
        "academic_performance_match": 0.05
    },
    "major_focused": {  # 专业导向型申请者
        "school_tier_match": 0.2,
        "school_ranking_score": 0.1,  # 降低排名权重
        "program_direction_match": 0.5,  # 提高专业权重
        "experience_match": 0.15,
        "academic_performance_match": 0.05
    },
    "balanced": {  # 平衡型申请者（默认）
        "school_tier_match": 0.3,
        "school_ranking_score": 0.2,
        "program_direction_match": 0.3,
        "experience_match": 0.15,
        "academic_performance_match": 0.05
    }
}

# LLM API配置 - 从主配置获取
SILICONE_FLOW_API_KEY = getattr(settings, 'SILICONE_FLOW_API_KEY') 
ALIBABACLOUD_API_KEY_ai_selection = getattr(settings, 'ALIBABACLOUD_API_KEY_ai_selection')