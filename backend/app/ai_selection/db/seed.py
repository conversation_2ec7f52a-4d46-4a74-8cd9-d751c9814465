import json
from sqlalchemy import select
from app.db.database import get_db
from app.ai_selection.db.models import (
    AISelectionProgram as Program, 
    AISelectionCase as Case
)
from app.ai_selection.utils.rag import embed_text
import numpy as np
from tqdm import tqdm
import time
import asyncio

async def generate_embeddings_for_existing_data():
    """为现有数据库中的专业和案例生成嵌入向量 - 批量处理优化版本"""
    async for session in get_db():
        try:
            # 统计需要处理的数据
            print("正在统计需要处理的数据...")
            
            # 查询需要生成嵌入向量的专业
            programs_query = select(Program).where(Program.embedding.is_(None))
            programs_result = await session.execute(programs_query)
            programs_to_process = programs_result.scalars().all()
            
            # 查询需要生成嵌入向量的案例
            cases_query = select(Case).where(Case.embedding.is_(None))
            cases_result = await session.execute(cases_query)
            cases_to_process = cases_result.scalars().all()
            
            total_programs = len(programs_to_process)
            total_cases = len(cases_to_process)
            
            print(f"发现 {total_programs} 个专业和 {total_cases} 个案例需要生成嵌入向量")
            
            if total_programs == 0 and total_cases == 0:
                print("所有数据已经有嵌入向量，无需处理")
                return
            
            # 批量处理专业数据
            if total_programs > 0:
                print(f"\n开始批量处理 {total_programs} 个专业...")
                await process_programs_in_batches(session, programs_to_process)
            
            # 批量处理案例数据
            if total_cases > 0:
                print(f"\n开始批量处理 {total_cases} 个案例...")
                await process_cases_in_batches(session, cases_to_process)
            
            print(f"\n所有嵌入向量生成完成！")
            
        except Exception as e:
            print(f"生成嵌入向量时出错: {e}")
            await session.rollback()
            raise
        finally:
            await session.close()
        break  # 只需要一个session

async def process_programs_in_batches(session, programs_to_process):
    """批量处理专业数据，每1000条保存一次"""
    batch_size = 1000
    total_programs = len(programs_to_process)
    program_success = 0
    program_failed = 0
    
    with tqdm(total=total_programs, desc="处理专业", unit="个") as pbar:
        for i in range(0, total_programs, batch_size):
            batch = programs_to_process[i:i + batch_size]
            batch_success = 0
            batch_failed = 0
            
            # 处理当前批次
            for program in batch:
                # 生成表示专业的文本
                program_text = f"""
                学校: {program.school_name_cn}
                地区: {program.school_region}
                专业名称: {program.program_name_cn}
                英文名称: {program.program_name_en}
                学科分类: {program.program_direction}
                专业描述: {program.program_objectives}
                """
                
                try:
                    # 生成嵌入向量，添加重试机制
                    embedding = await generate_embedding_with_retry(program_text, max_retries=1)
                    # 将向量转为JSON格式存储
                    program.embedding = json.dumps(embedding.tolist())
                    batch_success += 1
                    program_success += 1
                    
                except Exception as e:
                    batch_failed += 1
                    program_failed += 1
                    print(f"\n为专业 {program.program_name_cn} 生成嵌入向量失败: {e}")
                
                pbar.update(1)
                pbar.set_postfix({
                    "总成功": program_success, 
                    "总失败": program_failed,
                    "批次成功": batch_success,
                    "批次失败": batch_failed
                })
            
            # 每批次保存到数据库
            try:
                await session.commit()
                print(f"\n已保存第 {i//batch_size + 1} 批专业数据（{batch_success} 成功，{batch_failed} 失败）")
            except Exception as e:
                await session.rollback()
                print(f"\n保存第 {i//batch_size + 1} 批专业数据失败: {e}")
                # 重新标记这批数据为失败
                for program in batch:
                    if hasattr(program, 'embedding') and program.embedding:
                        program.embedding = None
    
    print(f"专业处理完成: 总成功 {program_success}/{total_programs}，总失败 {program_failed}/{total_programs}")

async def process_cases_in_batches(session, cases_to_process):
    """批量处理案例数据，每1000条保存一次"""
    batch_size = 1000
    total_cases = len(cases_to_process)
    case_success = 0
    case_failed = 0
    
    with tqdm(total=total_cases, desc="处理案例", unit="个") as pbar:
        for i in range(0, total_cases, batch_size):
            batch = cases_to_process[i:i + batch_size]
            batch_success = 0
            batch_failed = 0
            
            # 处理当前批次
            for case in batch:
                # 生成表示学生背景的文本
                case_background = f"""
                本科学校: {case.undergraduate_school}
                本科学校层级: {case.undergraduate_school_tier}
                本科专业: {case.undergraduate_major}
                GPA: {case.gpa}
                录取地区: {case.offer_region}
                录取学位: {case.offer_degree}
                录取专业方向: {case.offer_major_direction}
                关键经历: {case.key_experiences}
                """
                
                try:
                    # 生成嵌入向量，添加重试机制
                    embedding = await generate_embedding_with_retry(case_background, max_retries=1)
                    # 将向量转为JSON格式存储
                    case.embedding = json.dumps(embedding.tolist())
                    batch_success += 1
                    case_success += 1
                    
                except Exception as e:
                    batch_failed += 1
                    case_failed += 1
                    print(f"\n为案例 {case.student_name} 生成嵌入向量失败: {e}")
                
                pbar.update(1)
                pbar.set_postfix({
                    "总成功": case_success, 
                    "总失败": case_failed,
                    "批次成功": batch_success,
                    "批次失败": batch_failed
                })
            
            # 每批次保存到数据库
            try:
                await session.commit()
                print(f"\n已保存第 {i//batch_size + 1} 批案例数据（{batch_success} 成功，{batch_failed} 失败）")
            except Exception as e:
                await session.rollback()
                print(f"\n保存第 {i//batch_size + 1} 批案例数据失败: {e}")
                # 重新标记这批数据为失败
                for case in batch:
                    if hasattr(case, 'embedding') and case.embedding:
                        case.embedding = None
    
    print(f"案例处理完成: 总成功 {case_success}/{total_cases}，总失败 {case_failed}/{total_cases}")

async def generate_embedding_with_retry(text: str, max_retries: int = 1) -> np.ndarray:
    """
    生成嵌入向量，包含重试机制
    
    Args:
        text: 要嵌入的文本
        max_retries: 最大重试次数
        
    Returns:
        嵌入向量
        
    Raises:
        Exception: 所有重试都失败时抛出最后一个异常
    """
    last_exception = None
    
    for attempt in range(max_retries):
        try:
            embedding = embed_text(text)
            return embedding
        except Exception as e:
            last_exception = e
            if attempt < max_retries - 1:
                # 在重试前等待递增的时间（指数退避）
                wait_time = 2 ** attempt  # 1秒, 2秒, 4秒...
                await asyncio.sleep(wait_time)
                print(f"嵌入向量生成失败，第 {attempt + 1} 次重试...")
            else:
                print(f"嵌入向量生成失败，已达到最大重试次数")
    
    # 所有重试都失败，抛出最后一个异常
    raise last_exception 