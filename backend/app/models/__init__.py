from app.models.user import User
from app.models.client import (
    Client,
    Education,
    Academic,
    Work,
    Activity,
    Award,
    Skill,
    LanguageScore,
    Thought,
    BackgroundCustomModule,
    ThoughtCustomModule,
    ClientProgram
)

# 导入 AI 选择模块的模型
from app.ai_selection.db.models import (
    AISelectionProgram,
    AISelectionCase,
    AISelectionHomeSchool,
    AISelectionAbroadSchool
)

# 导出所有模型，方便其他模块导入
__all__ = [
    "User",
    "Client",
    "Education",
    "Academic",
    "Work",
    "Activity",
    "Award",
    "Skill",
    "LanguageScore",
    "Thought",
    "BackgroundCustomModule", 
    "ThoughtCustomModule",
    "ClientProgram",
    "AISelectionProgram",
    "AISelectionCase",
    "AISelectionHomeSchool",
    "AISelectionAbroadSchool"
]