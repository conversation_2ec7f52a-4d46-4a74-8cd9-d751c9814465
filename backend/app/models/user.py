from sqlalchemy import Column, Integer, String, Boolean, DateTime, func
from sqlalchemy.orm import relationship
from datetime import datetime
import bcrypt
from app.db.database import Base

class User(Base):
    """
    用户模型类，对应 PostgreSQL 数据库中的 users 表
    """
    __tablename__ = "users"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="用户 ID，自增主键")
    username = Column(String(64), unique=True, nullable=False, index=True, comment="用户名，唯一，用于登录")
    email = Column(String(120), unique=True, nullable=False, index=True, comment="电子邮箱，唯一")
    nickname = Column(String(64), nullable=True, comment="用户昵称，可选")
    password_hash = Column(String(128), nullable=False, comment="密码哈希值")
    role = Column(String(20), default="user", comment="用户角色，默认为普通用户(user)")
    is_active = Column(Boolean, default=True, comment="账户是否激活，默认为激活状态")

    # 关联的客户（一对多关系）
    clients = relationship("Client", back_populates="user")

    # 时间相关字段
    last_login = Column(DateTime, nullable=True, comment="最后登录时间")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def set_password(self, password: str) -> None:
        """
        设置用户密码，将明文密码转换为哈希值存储

        Args:
            password: 明文密码
        """
        self.password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

    def check_password(self, password: str) -> bool:
        """
        验证用户密码是否正确

        Args:
            password: 明文密码

        Returns:
            bool: 密码是否正确
        """
        return bcrypt.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))

    def to_dict(self) -> dict:
        """
        将用户对象转换为字典，用于 API 响应

        Returns:
            dict: 用户信息字典
        """
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'nickname': self.nickname,
            'role': self.role,
            'is_active': self.is_active,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }