from fastapi import APIRouter, Depends, File, UploadFile, Path, status, HTTPException
from typing import Any

from app.core.dependencies import DBSession
from app.ai_augmentation.core.augmenter import augment_and_save_experience
from app.ai_augmentation.schemas.augmentation import AugmentationResponse

router = APIRouter()

@router.post(
    "/augment/{client_id}/{experience_type}",
    response_model=AugmentationResponse,
    status_code=status.HTTP_201_CREATED,
    summary="通过AI从文件智能新增学术或工作经历"
)
async def augment_experience_from_file(
    db: DBSession,
    client_id: str = Path(..., description="客户的哈希ID"),
    experience_type: str = Path(..., description="要增强的经历类型，只能是 'academic' 或 'work'"),
    file: UploadFile = File(..., description="包含经历描述的简历或文档 (.pdf, .docx, .txt)")
) -> Any:
    """
    上传一个文件，系统将从中提取信息，创建一个新的学术或工作经历，并将其与指定客户关联。
    """
    if experience_type not in ["academic", "work"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, 
            detail=f"无效的经历类型 '{experience_type}'，只支持 'academic' 或 'work'"
        )
    
    result = await augment_and_save_experience(
        db=db,
        client_id=client_id,
        experience_type=experience_type,
        file=file
    )
    
    return AugmentationResponse(
        experience_type=result.experience_type,
        data=result.data
    ) 