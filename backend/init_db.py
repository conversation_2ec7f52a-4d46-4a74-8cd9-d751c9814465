import asyncio
import sys
from sqlalchemy import select
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from app.models.user import User
from app.core.config import settings

# 创建异步引擎
engine = create_async_engine(
    settings.DATABASE_URL,
    echo=True,
    future=True,
)

# 创建异步会话工厂
AsyncSessionLocal = sessionmaker(
    engine, 
    class_=AsyncSession, 
    expire_on_commit=False,
    autoflush=False
)

async def init_db():
    """初始化数据库，创建超级管理员用户"""
    async with AsyncSessionLocal() as session:
        # 检查是否已存在admin用户
        result = await session.execute(select(User).where(User.username == "admin"))
        admin = result.scalars().first()
        
        if not admin:
            print("创建管理员用户...")
            # 创建超级管理员
            admin = User(
                username="admin",
                email="<EMAIL>",
                nickname="管理员",
                role="admin",
                is_active=True
            )
            admin.set_password("admin123")
            
            session.add(admin)
            await session.commit()
            print("管理员用户创建成功!")
        else:
            print("管理员用户已存在!")

if __name__ == "__main__":
    print("初始化数据库...")
    asyncio.run(init_db())
    print("数据库初始化完成!") 