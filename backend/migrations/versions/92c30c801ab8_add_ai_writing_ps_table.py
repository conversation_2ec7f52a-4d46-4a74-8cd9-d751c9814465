"""add ai_writing_ps table

Revision ID: 92c30c801ab8
Revises: 6af031850163
Create Date: 2025-07-10 15:15:01.226986

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '92c30c801ab8'
down_revision: Union[str, None] = '6af031850163'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ai_writing_ps',
    sa.Column('id', sa.Integer(), nullable=False, comment='PS记录ID，自增主键'),
    sa.Column('version_name', sa.String(length=200), nullable=False, comment='PS版本名称'),
    sa.Column('target_major', sa.String(length=500), nullable=True, comment='目标专业（院校-学位-专业）'),
    sa.Column('content_markdown', sa.Text(), nullable=True, comment='PS内容的Markdown格式'),
    sa.Column('word_limit', sa.Integer(), nullable=True, comment='字数限制'),
    sa.Column('paragraph_setting', sa.Integer(), nullable=True, comment='段落设置'),
    sa.Column('client_id', sa.Integer(), nullable=False, comment='关联的客户ID'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ai_writing_ps_id'), 'ai_writing_ps', ['id'], unique=False)
    op.drop_index('idx_academic_client_id', table_name='academic')
    op.drop_table_comment(
        'academic',
        existing_comment='学术经历表，存储客户的学术经历信息',
        schema=None
    )
    op.drop_index('idx_activities_client_id', table_name='activities')
    op.drop_table_comment(
        'activities',
        existing_comment='课外活动表，存储客户的课外活动信息',
        schema=None
    )
    op.create_foreign_key(None, 'ai_selection_cases', 'ai_selection_programs', ['offer_program_id'], ['id'])
    op.alter_column('ai_selection_programs', 'program_code',
               existing_type=sa.INTEGER(),
               comment='专业代码',
               existing_comment='项目代码',
               existing_nullable=True)
    op.alter_column('ai_selection_programs', 'embedding',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment='专业描述的向量嵌入',
               existing_comment='项目描述的向量嵌入',
               existing_nullable=True)
    op.drop_column('ai_selection_programs', 'school_ranks')
    op.drop_column('ai_selection_programs', 'interview_experience')
    op.drop_column('ai_selection_programs', 'school_labels')
    op.drop_column('ai_selection_programs', 'interview_type')
    op.drop_column('ai_selection_programs', 'consultant_analysis')
    op.drop_index('idx_ai_writing_cv_client_id', table_name='ai_writing_cv')
    op.drop_table_comment(
        'ai_writing_cv',
        existing_comment='AI写作-CV内容表，存储客户的CV内容，支持多版本',
        schema=None
    )
    op.alter_column('ai_writing_rl', 'id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='推荐信记录ID，自增主键',
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('ai_writing_rl', 'client_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='关联的客户ID',
               existing_nullable=False)
    op.alter_column('ai_writing_rl', 'content_markdown',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='推荐信内容的Markdown格式',
               existing_nullable=False)
    op.alter_column('ai_writing_rl', 'version_name',
               existing_type=sa.VARCHAR(),
               comment=None,
               existing_comment='推荐信版本名称',
               existing_nullable=False)
    op.alter_column('ai_writing_rl', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('ai_writing_rl', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=True)
    op.drop_index('idx_ai_writing_rl_client_id', table_name='ai_writing_rl')
    op.drop_table_comment(
        'ai_writing_rl',
        existing_comment='AI写作-推荐信内容表，存储客户的推荐信内容，支持多版本',
        schema=None
    )
    op.drop_index('idx_awards_client_id', table_name='awards')
    op.drop_table_comment(
        'awards',
        existing_comment='奖项荣誉表，存储客户获得的奖项和荣誉信息',
        schema=None
    )
    op.drop_index('idx_background_custom_modules_client_id', table_name='background_custom_modules')
    op.drop_table_comment(
        'background_custom_modules',
        existing_comment='背景自定义模块表，存储客户的背景模块信息',
        schema=None
    )
    op.alter_column('client_programs', 'id',
               existing_type=sa.INTEGER(),
               comment='定校记录 ID，自增主键',
               existing_comment='定校记录 ID, 自增主键',
               existing_nullable=False,
               autoincrement=True)
    op.drop_index('idx_client_programs_client_id', table_name='client_programs')
    op.drop_index('idx_client_programs_program_id', table_name='client_programs')
    op.drop_table_comment(
        'client_programs',
        existing_comment='客户定校书表, 存储客户选择的学校项目信息',
        schema=None
    )
    op.drop_index('idx_clients_id_hashed', table_name='clients')
    op.drop_index('idx_clients_name', table_name='clients')
    op.drop_index('idx_clients_user_id', table_name='clients')
    op.create_foreign_key(None, 'clients', 'users', ['user_id'], ['id'])
    op.drop_table_comment(
        'clients',
        existing_comment='客户表，存储客户基本信息',
        schema=None
    )
    op.drop_index('idx_education_client_id', table_name='education')
    op.drop_table_comment(
        'education',
        existing_comment='教育经历表，存储客户的教育背景信息',
        schema=None
    )
    op.drop_index('idx_language_scores_client_id', table_name='language_scores')
    op.drop_table_comment(
        'language_scores',
        existing_comment='语言成绩表，存储客户的语言考试成绩信息',
        schema=None
    )
    op.alter_column('skills', 'type',
               existing_type=sa.VARCHAR(length=100),
               comment="技能类型，如'专业技能'、'综合技能'等",
               existing_comment='技能类型，如"专业技能"、"综合技能"等',
               existing_nullable=False)
    op.drop_index('idx_skills_client_id', table_name='skills')
    op.drop_table_comment(
        'skills',
        existing_comment='技能表，存储客户的技能信息',
        schema=None
    )
    op.drop_index('idx_thought_custom_modules_client_id', table_name='thought_custom_modules')
    op.drop_table_comment(
        'thought_custom_modules',
        existing_comment='想法自定义模块表，存储客户的想法模块信息',
        schema=None
    )
    op.drop_index('idx_thoughts_client_id', table_name='thoughts')
    op.drop_table_comment(
        'thoughts',
        existing_comment='个人想法表，存储客户的个人想法信息',
        schema=None
    )
    op.drop_constraint('users_email_key', 'users', type_='unique')
    op.drop_constraint('users_username_key', 'users', type_='unique')
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.drop_table_comment(
        'users',
        existing_comment='用户表，存储系统用户信息',
        schema=None
    )
    op.drop_index('idx_work_client_id', table_name='work')
    op.drop_table_comment(
        'work',
        existing_comment='工作经历表，存储客户的工作/实习经历信息',
        schema=None
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table_comment(
        'work',
        '工作经历表，存储客户的工作/实习经历信息',
        existing_comment=None,
        schema=None
    )
    op.create_index('idx_work_client_id', 'work', ['client_id'], unique=False)
    op.create_table_comment(
        'users',
        '用户表，存储系统用户信息',
        existing_comment=None,
        schema=None
    )
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.create_unique_constraint('users_username_key', 'users', ['username'])
    op.create_unique_constraint('users_email_key', 'users', ['email'])
    op.create_table_comment(
        'thoughts',
        '个人想法表，存储客户的个人想法信息',
        existing_comment=None,
        schema=None
    )
    op.create_index('idx_thoughts_client_id', 'thoughts', ['client_id'], unique=False)
    op.create_table_comment(
        'thought_custom_modules',
        '想法自定义模块表，存储客户的想法模块信息',
        existing_comment=None,
        schema=None
    )
    op.create_index('idx_thought_custom_modules_client_id', 'thought_custom_modules', ['client_id'], unique=False)
    op.create_table_comment(
        'skills',
        '技能表，存储客户的技能信息',
        existing_comment=None,
        schema=None
    )
    op.create_index('idx_skills_client_id', 'skills', ['client_id'], unique=False)
    op.alter_column('skills', 'type',
               existing_type=sa.VARCHAR(length=100),
               comment='技能类型，如"专业技能"、"综合技能"等',
               existing_comment="技能类型，如'专业技能'、'综合技能'等",
               existing_nullable=False)
    op.create_table_comment(
        'language_scores',
        '语言成绩表，存储客户的语言考试成绩信息',
        existing_comment=None,
        schema=None
    )
    op.create_index('idx_language_scores_client_id', 'language_scores', ['client_id'], unique=False)
    op.create_table_comment(
        'education',
        '教育经历表，存储客户的教育背景信息',
        existing_comment=None,
        schema=None
    )
    op.create_index('idx_education_client_id', 'education', ['client_id'], unique=False)
    op.create_table_comment(
        'clients',
        '客户表，存储客户基本信息',
        existing_comment=None,
        schema=None
    )
    op.drop_constraint(None, 'clients', type_='foreignkey')
    op.create_index('idx_clients_user_id', 'clients', ['user_id'], unique=False)
    op.create_index('idx_clients_name', 'clients', ['name'], unique=False)
    op.create_index('idx_clients_id_hashed', 'clients', ['id_hashed'], unique=False)
    op.create_table_comment(
        'client_programs',
        '客户定校书表, 存储客户选择的学校项目信息',
        existing_comment=None,
        schema=None
    )
    op.create_index('idx_client_programs_program_id', 'client_programs', ['program_id'], unique=False)
    op.create_index('idx_client_programs_client_id', 'client_programs', ['client_id'], unique=False)
    op.alter_column('client_programs', 'id',
               existing_type=sa.INTEGER(),
               comment='定校记录 ID, 自增主键',
               existing_comment='定校记录 ID，自增主键',
               existing_nullable=False,
               autoincrement=True)
    op.create_table_comment(
        'background_custom_modules',
        '背景自定义模块表，存储客户的背景模块信息',
        existing_comment=None,
        schema=None
    )
    op.create_index('idx_background_custom_modules_client_id', 'background_custom_modules', ['client_id'], unique=False)
    op.create_table_comment(
        'awards',
        '奖项荣誉表，存储客户获得的奖项和荣誉信息',
        existing_comment=None,
        schema=None
    )
    op.create_index('idx_awards_client_id', 'awards', ['client_id'], unique=False)
    op.create_table_comment(
        'ai_writing_rl',
        'AI写作-推荐信内容表，存储客户的推荐信内容，支持多版本',
        existing_comment=None,
        schema=None
    )
    op.create_index('idx_ai_writing_rl_client_id', 'ai_writing_rl', ['client_id'], unique=False)
    op.alter_column('ai_writing_rl', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='更新时间',
               existing_nullable=True)
    op.alter_column('ai_writing_rl', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='创建时间',
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('ai_writing_rl', 'version_name',
               existing_type=sa.VARCHAR(),
               comment='推荐信版本名称',
               existing_nullable=False)
    op.alter_column('ai_writing_rl', 'content_markdown',
               existing_type=sa.TEXT(),
               comment='推荐信内容的Markdown格式',
               existing_nullable=False)
    op.alter_column('ai_writing_rl', 'client_id',
               existing_type=sa.INTEGER(),
               comment='关联的客户ID',
               existing_nullable=False)
    op.alter_column('ai_writing_rl', 'id',
               existing_type=sa.INTEGER(),
               comment='推荐信记录ID，自增主键',
               existing_nullable=False,
               autoincrement=True)
    op.create_table_comment(
        'ai_writing_cv',
        'AI写作-CV内容表，存储客户的CV内容，支持多版本',
        existing_comment=None,
        schema=None
    )
    op.create_index('idx_ai_writing_cv_client_id', 'ai_writing_cv', ['client_id'], unique=False)
    op.add_column('ai_selection_programs', sa.Column('consultant_analysis', sa.TEXT(), autoincrement=False, nullable=True, comment='顾问分析'))
    op.add_column('ai_selection_programs', sa.Column('interview_type', sa.VARCHAR(length=50), autoincrement=False, nullable=True, comment='面试类型'))
    op.add_column('ai_selection_programs', sa.Column('school_labels', sa.VARCHAR(length=200), autoincrement=False, nullable=True, comment='学校标签'))
    op.add_column('ai_selection_programs', sa.Column('interview_experience', sa.TEXT(), autoincrement=False, nullable=True, comment='面试经验'))
    op.add_column('ai_selection_programs', sa.Column('school_ranks', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='学校排名信息'))
    op.alter_column('ai_selection_programs', 'embedding',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment='项目描述的向量嵌入',
               existing_comment='专业描述的向量嵌入',
               existing_nullable=True)
    op.alter_column('ai_selection_programs', 'program_code',
               existing_type=sa.INTEGER(),
               comment='项目代码',
               existing_comment='专业代码',
               existing_nullable=True)
    op.drop_constraint(None, 'ai_selection_cases', type_='foreignkey')
    op.create_table_comment(
        'activities',
        '课外活动表，存储客户的课外活动信息',
        existing_comment=None,
        schema=None
    )
    op.create_index('idx_activities_client_id', 'activities', ['client_id'], unique=False)
    op.create_table_comment(
        'academic',
        '学术经历表，存储客户的学术经历信息',
        existing_comment=None,
        schema=None
    )
    op.create_index('idx_academic_client_id', 'academic', ['client_id'], unique=False)
    op.drop_index(op.f('ix_ai_writing_ps_id'), table_name='ai_writing_ps')
    op.drop_table('ai_writing_ps')
    # ### end Alembic commands ###
