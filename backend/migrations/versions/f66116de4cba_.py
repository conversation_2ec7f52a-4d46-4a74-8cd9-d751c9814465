"""empty message

Revision ID: f66116de4cba
Revises: 
Create Date: 2025-07-04 18:00:25.491574

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f66116de4cba'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ai_writing_rl',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_id', sa.Integer(), nullable=False),
    sa.Column('content_markdown', sa.Text(), nullable=False),
    sa.Column('version_name', sa.String(), nullable=False),
    sa.Column('recommender_name', sa.String(), nullable=False, comment='推荐人姓名'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ai_writing_rl_id'), 'ai_writing_rl', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_ai_writing_rl_id'), table_name='ai_writing_rl')
    op.drop_table('ai_writing_rl')
    # ### end Alembic commands ###
