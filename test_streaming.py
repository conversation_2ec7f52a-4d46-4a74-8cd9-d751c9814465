#!/usr/bin/env python3
"""
流式输出诊断脚本
用于测试流式输出功能是否正常工作
"""

import asyncio
import aiohttp
import json
import time

async def test_streaming_output():
    """测试流式输出功能"""
    
    # 测试数据
    test_data = {
        "academic": {
            "education": "master",
            "school": "北京大学",
            "major": "计算机科学与技术",
            "gpa": "3.5",
            "gpaScale": "4.0"
        },
        "intention": {
            "countries": ["美国", "英国"],
            "majors": ["计算机科学", "软件工程"],
            "duration": "1-2年"
        },
        "strength": {
            "rankingPreference": ["QS-50", "QS-100"],
            "preference_type": "balanced",
            "enable_ai_selection": True
        }
    }
    
    print("🚀 开始测试流式输出...")
    print(f"📊 测试数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    
    start_time = time.time()
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                'http://localhost:8000/api/ai-selection/recommendation/recommend/stream',
                json=test_data,
                headers={'Content-Type': 'application/json'}
            ) as response:
                
                print(f"📡 响应状态: {response.status}")
                print(f"📋 响应头: {dict(response.headers)}")
                
                if response.status != 200:
                    error_text = await response.text()
                    print(f"❌ 错误响应: {error_text}")
                    return
                
                print("\n🔄 开始接收流式数据...")
                event_count = 0
                
                async for line in response.content:
                    line_str = line.decode('utf-8').strip()
                    
                    if line_str.startswith('data: '):
                        data_str = line_str[6:].strip()
                        if data_str and data_str != '[DONE]':
                            try:
                                event_data = json.loads(data_str)
                                event_count += 1
                                elapsed = time.time() - start_time
                                
                                print(f"📦 事件 #{event_count} (耗时: {elapsed:.2f}s)")
                                print(f"   类型: {event_data.get('type', 'unknown')}")
                                print(f"   消息: {event_data.get('message', 'N/A')}")
                                
                                if event_data.get('type') == 'recommendation':
                                    print(f"   推荐: {event_data.get('recommendation', {}).get('school_name_cn', 'N/A')}")
                                
                                # 检查是否完成
                                if event_data.get('type') in ['complete', 'hard_filter_complete']:
                                    print("✅ 流式输出完成!")
                                    break
                                    
                            except json.JSONDecodeError as e:
                                print(f"⚠️  JSON解析错误: {e}")
                                print(f"   原始数据: {data_str}")
                
                total_time = time.time() - start_time
                print(f"\n📈 测试结果:")
                print(f"   总事件数: {event_count}")
                print(f"   总耗时: {total_time:.2f}秒")
                print(f"   平均事件间隔: {total_time/max(event_count, 1):.2f}秒")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_redis_connection():
    """测试Redis连接"""
    print("\n🔍 测试Redis连接...")
    
    try:
        import aioredis
        redis = aioredis.from_url("redis://localhost:6379/0")
        
        # 测试连接
        await redis.ping()
        print("✅ Redis连接正常")
        
        # 测试读写
        await redis.set("test_key", "test_value", ex=10)
        value = await redis.get("test_key")
        print(f"✅ Redis读写测试: {value.decode() if value else 'None'}")
        
        # 清理测试键
        await redis.delete("test_key")
        await redis.close()
        
    except Exception as e:
        print(f"❌ Redis测试失败: {e}")

async def test_cache_performance():
    """测试缓存性能"""
    print("\n⚡ 测试缓存性能...")
    
    try:
        from backend.app.core.cache import cache
        
        # 测试缓存写入
        start_time = time.time()
        await cache.set("perf_test", {"data": "test"}, ttl=60)
        write_time = time.time() - start_time
        print(f"📝 缓存写入耗时: {write_time*1000:.2f}ms")
        
        # 测试缓存读取
        start_time = time.time()
        result = await cache.get("perf_test")
        read_time = time.time() - start_time
        print(f"📖 缓存读取耗时: {read_time*1000:.2f}ms")
        print(f"📊 缓存数据: {result}")
        
        # 清理测试数据
        await cache.delete("perf_test")
        
    except Exception as e:
        print(f"❌ 缓存性能测试失败: {e}")

async def main():
    """主函数"""
    print("🔧 流式输出诊断工具")
    print("=" * 50)
    
    # 测试Redis连接
    await test_redis_connection()
    
    # 测试缓存性能
    await test_cache_performance()
    
    # 测试流式输出
    await test_streaming_output()
    
    print("\n🏁 诊断完成!")

if __name__ == "__main__":
    asyncio.run(main())
