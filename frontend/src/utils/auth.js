/**
 * auth.js - 认证工具
 *
 * 本模块提供认证相关的工具函数，包括：
 * 1. 存储和获取认证信息（令牌、用户信息）
 * 2. 清除认证信息（登出）
 * 3. 检查认证状态
 */

// 存储键名常量，便于统一管理
const TOKEN_KEY = 'token'
const REFRESH_TOKEN_KEY = 'refresh_token'
const USER_KEY = 'user'

/**
 * 存储认证信息
 *
 * 将访问令牌、刷新令牌和用户信息保存到本地存储
 *
 * @param {string} token - 访问令牌
 * @param {string} refreshToken - 刷新令牌
 * @param {Object} user - 用户信息对象
 */
export const setAuth = (token, refreshToken, user) => {
  localStorage.setItem(TOKEN_KEY, token)
  localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken)
  localStorage.setItem(USER_KEY, JSON.stringify(user))
}

/**
 * 清除认证信息
 *
 * 从本地存储中移除所有认证相关信息，用于登出
 */
export const clearAuth = () => {
  localStorage.removeItem(TOKEN_KEY)
  localStorage.removeItem(REFRESH_TOKEN_KEY)
  localStorage.removeItem(USER_KEY)
}

/**
 * 获取访问令牌
 *
 * @returns {string|null} 访问令牌，如果不存在则返回null
 */
export const getToken = () => localStorage.getItem(TOKEN_KEY)

/**
 * 获取刷新令牌
 *
 * @returns {string|null} 刷新令牌，如果不存在则返回null
 */
export const getRefreshToken = () => localStorage.getItem(REFRESH_TOKEN_KEY)

/**
 * 获取用户信息
 *
 * @returns {Object|null} 用户信息对象，如果不存在则返回null
 */
export const getUser = () => {
  const userStr = localStorage.getItem(USER_KEY)
  return userStr ? JSON.parse(userStr) : null
}

/**
 * 检查是否已认证
 *
 * @returns {boolean} 是否已认证
 */
export const isAuthenticated = () => {
  return !!getToken()
}

/**
 * 更新访问令牌
 *
 * @param {string} token - 新的访问令牌
 */
export const updateToken = (token) => {
  localStorage.setItem(TOKEN_KEY, token)
}

/**
 * 更新用户信息
 *
 * @param {Object} user - 新的用户信息对象
 */
export const updateUser = (user) => {
  // 获取当前存储的用户信息
  const currentUser = getUser()

  // 合并现有用户信息和新的用户信息
  const updatedUser = { ...currentUser, ...user }

  // 特别确保昵称字段被正确更新
  if (user.nickname) {
    updatedUser.nickname = user.nickname
  }

  // 保存更新后的用户信息
  localStorage.setItem(USER_KEY, JSON.stringify(updatedUser))
}