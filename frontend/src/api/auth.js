import request from '@/utils/request'
import { setAuth, clearAuth } from '@/utils/auth'

export const login = async (username, password) => {
  const params = new URLSearchParams();
  params.append('username', username);
  params.append('password', password);

  try {
    const response = await request.post('/api/auth/login', params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      }
    });

    if (response.access_token) {
      setAuth(response.access_token, response.refresh_token, response.user)
    }
    return response;
  } catch (error) {
    console.error('Login failed:', error);
    throw error;
  }
}

export const register = async (username, email, password) => {
  const response = await request.post('/api/auth/register', {
    username,
    email,
    password
  })
  if (response.access_token) {
    setAuth(response.access_token, response.refresh_token, response.user)
  }
  return response
}

export const logout = () => {
  clearAuth()
  window.location.href = '/login'
}

export const getCurrentUser = async (params = {}) => {
  return request.get('/api/auth/me', { params })
}

export const updateProfile = async (data) => {
  try {
    const response = await request.put('/api/auth/update-profile', data)

    // 如果更新成功，同时更新本地存储的用户信息
    if (response) {
      // 获取当前存储的用户信息
      const currentUser = JSON.parse(localStorage.getItem('user') || 'null')

      if (currentUser) {
        // 更新用户信息
        const updatedUser = { ...currentUser }

        // 更新昵称
        if (data.nickname) {
          updatedUser.nickname = data.nickname
        }

        // 更新其他字段
        if (response.updated_at) {
          updatedUser.updated_at = response.updated_at
        }

        // 保存更新后的用户信息
        localStorage.setItem('user', JSON.stringify(updatedUser))
      }
    }

    return response
  } catch (error) {
    throw error
  }
}

export const refreshToken = async () => {
  const refresh_token = localStorage.getItem('refresh_token')
  if (!refresh_token) {
    throw new Error('No refresh token')
  }

  try {
    // 使用原始axios实例发送请求，避免进入request拦截器循环
    const axios = require('axios').default
    const baseURL = import.meta.env.VITE_API_URL || 'http://localhost:8000'

    const response = await axios({
      method: 'post',
      url: `${baseURL}/api/auth/refresh`,
      headers: {
        'Authorization': `Bearer ${refresh_token}`,
        'Content-Type': 'application/json'
      }
    })

    // 更新本地存储的token
    if (response.data && response.data.access_token) {
      localStorage.setItem('token', response.data.access_token)
      return response.data
    } else {
      throw new Error('刷新令牌响应无效')
    }
  } catch (error) {
    logout()
    throw error
  }
}

export const changePassword = async (oldPassword, newPassword) => {
  return request.post('/api/auth/change-password', {
    old_password: oldPassword,
    new_password: newPassword
  })
}

export const forgotPassword = async (email) => {
  return request.post('/api/auth/forgot-password', {
    email
  })
}