import request from '@/utils/request'

// 获取当前用户信息
export function getCurrentUser() {
  return request({
    url: '/account/profile',
    method: 'get'
  })
}

// 更新个人信息
export function updateProfile(data) {
  return request({
    url: '/account/profile',
    method: 'put',
    data
  })
}

// 修改密码
export function changePassword(data) {
  return request({
    url: '/account/password',
    method: 'put',
    data
  })
}

// 更新机构信息
export function updateOrganization(data) {
  return request({
    url: '/account/organization',
    method: 'put',
    data
  })
} 