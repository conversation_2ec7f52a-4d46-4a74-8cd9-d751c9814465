/**
 * AI检测API客户端
 */
import request from '@/utils/request'

const AI_DETECTION_BASE = '/api/ai-detection'

/**
 * 单文档AI检测
 * @param {string} text - 要检测的文本
 * @returns {Promise} 检测结果
 */
export const detectAIContent = async (text) => {
  return request({
    url: `${AI_DETECTION_BASE}/detect`,
    method: 'post',
    data: { text }
  })
}

/**
 * 批量文档AI检测
 * @param {Array<string>} texts - 要检测的文本数组
 * @returns {Promise} 批量检测结果
 */
export const detectAIContentBatch = async (texts) => {
  return request({
    url: `${AI_DETECTION_BASE}/detect-batch`,
    method: 'post',
    data: { texts }
  })
}

/**
 * 批量检测统计分析
 * @param {Array<string>} texts - 要分析的文本数组
 * @returns {Promise} 统计分析结果
 */
export const analyzeAIContentBatch = async (texts) => {
  return request({
    url: `${AI_DETECTION_BASE}/analyze-batch`,
    method: 'post',
    data: { texts }
  })
}

/**
 * 获取AI检测服务健康状态
 * @returns {Promise} 健康状态
 */
export const getAIDetectionHealth = async () => {
  return request({
    url: `${AI_DETECTION_BASE}/health`,
    method: 'get'
  })
}

/**
 * 获取AI检测服务配置信息
 * @returns {Promise} 配置信息
 */
export const getAIDetectionConfig = async () => {
  return request({
    url: `${AI_DETECTION_BASE}/config`,
    method: 'get'
  })
}

/**
 * 获取AI检测API信息
 * @returns {Promise} API信息
 */
export const getAIDetectionInfo = async () => {
  return request({
    url: `${AI_DETECTION_BASE}/`,
    method: 'get'
  })
} 