import request from '@/utils/request'

// 客户管理相关API
export const clientApi = {
  // 获取客户列表
  getClients: (params = {}) => {
    return request({
      url: '/api/crm/clients',
      method: 'get',
      params
    })
  },

  // 获取客户详情
  getClient: (id) => {
    return request({
      url: `/api/crm/clients/${id}`,
      method: 'get'
    })
  },

  // 创建客户
  createClient: (data) => {
    return request({
      url: '/api/crm/clients',
      method: 'post',
      data
    })
  },

  // 更新客户
  updateClient: (id, data) => {
    return request({
      url: `/api/crm/clients/${id}`,
      method: 'put',
      data
    })
  },

  // 删除客户
  deleteClient: (id) => {
    return request({
      url: `/api/crm/clients/${id}`,
      method: 'delete'
    })
  },

  // 更新客户进度
  updateClientProgress: (id, progress) => {
    return request({
      url: `/api/crm/clients/${id}/progress`,
      method: 'patch',
      data: { progress }
    })
  },

  // 更新客户阶段
  updateClientStage: (id, stage) => {
    return request({
      url: `/api/crm/clients/${id}/stage`,
      method: 'patch',
      data: { stage }
    })
  }
}

// 任务管理相关API
export const taskApi = {
  // 获取任务列表
  getTasks: (params = {}) => {
    return request({
      url: '/api/crm/tasks',
      method: 'get',
      params
    })
  },

  // 获取任务详情
  getTask: (id) => {
    return request({
      url: `/api/crm/tasks/${id}`,
      method: 'get'
    })
  },

  // 创建任务
  createTask: (data) => {
    return request({
      url: '/api/crm/tasks',
      method: 'post',
      data
    })
  },

  // 更新任务
  updateTask: (id, data) => {
    return request({
      url: `/api/crm/tasks/${id}`,
      method: 'put',
      data
    })
  },

  // 删除任务
  deleteTask: (id) => {
    return request({
      url: `/api/crm/tasks/${id}`,
      method: 'delete'
    })
  },

  // 更新任务状态
  updateTaskStatus: (id, status) => {
    return request({
      url: `/api/crm/tasks/${id}/status`,
      method: 'patch',
      data: { status }
    })
  },

  // 批量更新任务
  batchUpdateTasks: (taskIds, data) => {
    return request({
      url: '/api/crm/tasks/batch',
      method: 'patch',
      data: { task_ids: taskIds, ...data }
    })
  },

  // 获取任务统计
  getTaskStats: () => {
    return request({
      url: '/api/crm/tasks/stats',
      method: 'get'
    })
  }
}

// 材料管理相关API
export const materialApi = {
  // 获取客户材料列表
  getClientMaterials: (clientId, params = {}) => {
    return request({
      url: `/api/crm/clients/${clientId}/materials`,
      method: 'get',
      params
    })
  },

  // 上传材料
  uploadMaterial: (clientId, data) => {
    return request({
      url: `/api/crm/clients/${clientId}/materials`,
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 更新材料信息
  updateMaterial: (materialId, data) => {
    return request({
      url: `/api/crm/materials/${materialId}`,
      method: 'put',
      data
    })
  },

  // 删除材料
  deleteMaterial: (materialId) => {
    return request({
      url: `/api/crm/materials/${materialId}`,
      method: 'delete'
    })
  },

  // 下载材料
  downloadMaterial: (materialId) => {
    return request({
      url: `/api/crm/materials/${materialId}/download`,
      method: 'get',
      responseType: 'blob'
    })
  },

  // 获取材料版本历史
  getMaterialVersions: (materialId) => {
    return request({
      url: `/api/crm/materials/${materialId}/versions`,
      method: 'get'
    })
  }
}

// 选校管理相关API
export const schoolApi = {
  // 获取客户选校列表
  getClientSchools: (clientId) => {
    return request({
      url: `/api/crm/clients/${clientId}/schools`,
      method: 'get'
    })
  },

  // 添加选校
  addClientSchool: (clientId, data) => {
    return request({
      url: `/api/crm/clients/${clientId}/schools`,
      method: 'post',
      data
    })
  },

  // 更新选校信息
  updateClientSchool: (schoolId, data) => {
    return request({
      url: `/api/crm/client-schools/${schoolId}`,
      method: 'put',
      data
    })
  },

  // 删除选校
  deleteClientSchool: (schoolId) => {
    return request({
      url: `/api/crm/client-schools/${schoolId}`,
      method: 'delete'
    })
  },

  // 上传定校书
  uploadSchoolPlan: (clientId, data) => {
    return request({
      url: `/api/crm/clients/${clientId}/school-plan`,
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

// 文书管理相关API
export const documentApi = {
  // 获取客户文书列表
  getClientDocuments: (clientId) => {
    return request({
      url: `/api/crm/clients/${clientId}/documents`,
      method: 'get'
    })
  },

  // 创建文书任务
  createDocument: (clientId, data) => {
    return request({
      url: `/api/crm/clients/${clientId}/documents`,
      method: 'post',
      data
    })
  },

  // 更新文书信息
  updateDocument: (documentId, data) => {
    return request({
      url: `/api/crm/documents/${documentId}`,
      method: 'put',
      data
    })
  },

  // 上传文书文件
  uploadDocumentFile: (documentId, data) => {
    return request({
      url: `/api/crm/documents/${documentId}/file`,
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 更新文书状态
  updateDocumentStatus: (documentId, status) => {
    return request({
      url: `/api/crm/documents/${documentId}/status`,
      method: 'patch',
      data: { status }
    })
  }
}

// 网申进度相关API
export const applicationApi = {
  // 获取客户网申进度
  getClientApplications: (clientId) => {
    return request({
      url: `/api/crm/clients/${clientId}/applications`,
      method: 'get'
    })
  },

  // 创建网申记录
  createApplication: (clientId, data) => {
    return request({
      url: `/api/crm/clients/${clientId}/applications`,
      method: 'post',
      data
    })
  },

  // 更新网申状态
  updateApplicationStatus: (applicationId, data) => {
    return request({
      url: `/api/crm/applications/${applicationId}`,
      method: 'put',
      data
    })
  },

  // 上传网申截图
  uploadApplicationScreenshot: (applicationId, data) => {
    return request({
      url: `/api/crm/applications/${applicationId}/screenshot`,
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

// Offer管理相关API
export const offerApi = {
  // 获取客户Offer列表
  getClientOffers: (clientId) => {
    return request({
      url: `/api/crm/clients/${clientId}/offers`,
      method: 'get'
    })
  },

  // 添加Offer
  createOffer: (clientId, data) => {
    return request({
      url: `/api/crm/clients/${clientId}/offers`,
      method: 'post',
      data
    })
  },

  // 更新Offer信息
  updateOffer: (offerId, data) => {
    return request({
      url: `/api/crm/offers/${offerId}`,
      method: 'put',
      data
    })
  },

  // 删除Offer
  deleteOffer: (offerId) => {
    return request({
      url: `/api/crm/offers/${offerId}`,
      method: 'delete'
    })
  },

  // 上传Offer文件
  uploadOfferFile: (offerId, data) => {
    return request({
      url: `/api/crm/offers/${offerId}/file`,
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 接受/拒绝Offer
  respondToOffer: (offerId, response) => {
    return request({
      url: `/api/crm/offers/${offerId}/respond`,
      method: 'patch',
      data: { response }
    })
  }
}

// 备注相关API
export const noteApi = {
  // 获取客户备注列表
  getClientNotes: (clientId) => {
    return request({
      url: `/api/crm/clients/${clientId}/notes`,
      method: 'get'
    })
  },

  // 添加备注
  addNote: (clientId, content) => {
    return request({
      url: `/api/crm/clients/${clientId}/notes`,
      method: 'post',
      data: { content }
    })
  },

  // 更新备注
  updateNote: (noteId, content) => {
    return request({
      url: `/api/crm/notes/${noteId}`,
      method: 'put',
      data: { content }
    })
  },

  // 删除备注
  deleteNote: (noteId) => {
    return request({
      url: `/api/crm/notes/${noteId}`,
      method: 'delete'
    })
  }
}

// 仪表盘统计相关API
export const dashboardApi = {
  // 获取CRM仪表盘数据
  getCRMDashboard: () => {
    return request({
      url: '/api/crm/dashboard',
      method: 'get'
    })
  },

  // 获取客户统计
  getClientStats: (params = {}) => {
    return request({
      url: '/api/crm/stats/clients',
      method: 'get',
      params
    })
  },

  // 获取任务统计
  getTaskStats: (params = {}) => {
    return request({
      url: '/api/crm/stats/tasks',
      method: 'get',
      params
    })
  },

  // 获取进度统计
  getProgressStats: (params = {}) => {
    return request({
      url: '/api/crm/stats/progress',
      method: 'get',
      params
    })
  }
}

// 文件上传相关API
export const uploadApi = {
  // 通用文件上传
  uploadFile: (data) => {
    return request({
      url: '/api/crm/upload',
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取上传凭证（如果使用OSS直传）
  getUploadCredentials: (params = {}) => {
    return request({
      url: '/api/crm/upload/credentials',
      method: 'get',
      params
    })
  }
}

// 默认导出所有API
export default {
  clientApi,
  taskApi,
  materialApi,
  schoolApi,
  documentApi,
  applicationApi,
  offerApi,
  noteApi,
  dashboardApi,
  uploadApi
} 