import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import App from './App.vue'
import router from './router'
import './styles/index.css'
import './style.css'

// 优化字体加载策略
const optimizeFontLoading = () => {
  try {
    // 1. 添加预连接，减少DNS查询时间
    if (!document.querySelector('link[rel="preconnect"][href="https://fonts.googleapis.com"]')) {
      const preconnectGoogle = document.createElement('link')
      preconnectGoogle.rel = 'preconnect'
      preconnectGoogle.href = 'https://fonts.googleapis.com'
      document.head.appendChild(preconnectGoogle)
    }

    if (!document.querySelector('link[rel="preconnect"][href="https://fonts.gstatic.com"]')) {
      const preconnectGstatic = document.createElement('link')
      preconnectGstatic.rel = 'preconnect'
      preconnectGstatic.href = 'https://fonts.gstatic.com'
      preconnectGstatic.crossOrigin = 'anonymous'
      document.head.appendChild(preconnectGstatic)
    }

    // 2. 确保Material Icons样式表已加载 (最常用的图标)
    if (!document.querySelector('link[href*="Material+Icons+Outlined"]')) {
      // 直接加载Material Icons样式表
      const materialStyle = document.createElement('link')
      materialStyle.rel = 'stylesheet'
      materialStyle.href = 'https://fonts.googleapis.com/icon?family=Material+Icons+Outlined&display=swap'
      
      // 添加错误处理，如果Google Fonts不可用，使用本地备选方案
      materialStyle.onerror = () => {
        console.warn('Google Fonts Material Icons 加载失败，使用本地备选方案')
        // 可以在这里添加本地字体或备选CDN
      }
      
      document.head.appendChild(materialStyle)
    }

    // 3. 使用字体显示策略加载Inter字体
    if (!document.querySelector('link[href*="family=Inter"]')) {
      const interStyle = document.createElement('link')
      interStyle.rel = 'stylesheet'
      interStyle.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap&text=abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789.,;:?!(){}[]'
      document.head.appendChild(interStyle)

      // 延迟加载其他字重
      setTimeout(() => {
        const interFullStyle = document.createElement('link')
        interFullStyle.rel = 'stylesheet'
        interFullStyle.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap'
        document.head.appendChild(interFullStyle)
      }, 500)
    }
  } catch (error) {
    console.warn('字体加载优化可能受到限制:', error)
  }
}

// 立即执行字体优化，不等待页面加载
optimizeFontLoading()

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(ElementPlus, {
  locale: zhCn,
})

app.mount('#app')
