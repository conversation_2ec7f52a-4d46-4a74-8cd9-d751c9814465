export default [
  {
    path: 'crm',
    redirect: '/crm/students/unsigned'
  },
  
  // === 简化的CRM路由 ===
  {
    path: 'crm/students/unsigned',
    name: 'CRMUnsignedStudents',
    component: () => import('@/views/crm/UnsignedStudents.vue'),
    meta: {
      title: '未签约学生',
      requiresAuth: true,
      keepAlive: true
    }
  },
  {
    path: 'crm/students/signed',
    name: 'CRMSignedStudents', 
    component: () => import('@/views/crm/SignedStudents.vue'),
    meta: {
      title: '签约学生',
      requiresAuth: true,
      keepAlive: true
    }
  },
  {
    path: 'crm/tasks',
    name: 'CRMTaskManagement',
    component: () => import('@/views/crm/TaskManagement.vue'),
    meta: {
      title: '任务管理',
      requiresAuth: true,
      keepAlive: true
    }
  },
  
  // === 组织管理模块 ===
  {
    path: 'crm/organization/departments',
    name: 'CRMOrganizationManagement',
    component: () => import('@/views/crm/organization/DepartmentManagement.vue'),
    meta: {
      title: '组织管理',
      requiresAuth: true,
      keepAlive: true
    }
  }
] 