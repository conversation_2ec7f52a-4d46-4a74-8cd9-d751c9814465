<template>
  <div class="course-structure" :class="{ 'dialog-mode': inDialog }">
    <!-- 紧凑模式：简洁显示 -->
    <div v-if="compact" class="compact-structure">
      <div class="course-summary" :class="{ 'dialog-summary': inDialog }">
        <div class="summary-stats">
          <span class="stat-item">
            <span class="stat-number">{{ totalCourses }}</span>
            <span class="stat-label">门课程</span>
          </span>
          <span v-if="requiredCourses > 0" class="stat-item">
            <span class="stat-number">{{ requiredCourses }}</span>
            <span class="stat-label">必修</span>
          </span>
          <span v-if="electiveCourses > 0" class="stat-item">
            <span class="stat-number">{{ electiveCourses }}</span>
            <span class="stat-label">选修</span>
          </span>
        </div>
        <div v-if="courseDescription" class="course-description-compact">
          {{ truncateText(courseDescription, inDialog ? 80 : 100) }}
        </div>
      </div>
    </div>

    <!-- 完整模式：详细展示 -->
    <div v-else class="full-structure">
      <!-- 课程描述 -->
      <div v-if="courseDescription" class="course-description">
        <h4 class="description-title">课程描述</h4>
        <p class="description-content">{{ courseDescription }}</p>
      </div>

      <!-- 课程统计 -->
      <div v-if="totalCourses > 0" class="course-stats">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">
              <span class="material-icons-outlined">school</span>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ totalCourses }}</div>
              <div class="stat-label">总课程数</div>
            </div>
          </div>
          <div v-if="requiredCourses > 0" class="stat-card required">
            <div class="stat-icon">
              <span class="material-icons-outlined">bookmark</span>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ requiredCourses }}</div>
              <div class="stat-label">必修课程</div>
            </div>
          </div>
          <div v-if="electiveCourses > 0" class="stat-card elective">
            <div class="stat-icon">
              <span class="material-icons-outlined">bookmark_border</span>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ electiveCourses }}</div>
              <div class="stat-label">选修课程</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 课程分类展示 -->
      <div v-if="courseCategories.length > 0" class="course-categories">
        <div class="categories-header">
          <h4 class="categories-title">课程分类</h4>
          <div class="categories-tabs">
            <button
              v-for="category in courseCategories"
              :key="category.name"
              @click="activeCategory = category.name"
              :class="['category-tab', { active: activeCategory === category.name }]"
            >
              {{ category.name }}
              <span class="course-count">({{ category.courses.length }})</span>
            </button>
          </div>
        </div>

        <!-- 当前分类的课程列表 -->
        <div class="course-list">
          <div 
            v-for="course in displayedCourses" 
            :key="course.id"
            class="course-item"
          >
            <div class="course-main">
              <h5 class="course-name-cn">{{ course.nameCn }}</h5>
              <p v-if="course.nameEn" class="course-name-en">{{ course.nameEn }}</p>
            </div>
            <div class="course-meta">
              <span 
                :class="['course-type-badge', course.type]"
              >
                {{ getCourseTypeText(course.type) }}
              </span>
              <span v-if="course.credits" class="course-credits">
                {{ course.credits }}学分
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 展开/收起按钮 -->
      <div v-if="shouldShowExpandButton" class="expand-controls">
        <button 
          @click="toggleExpanded"
          class="expand-button"
        >
          <span class="material-icons-outlined">
            {{ isExpanded ? 'expand_less' : 'expand_more' }}
          </span>
          {{ isExpanded ? '收起' : `展开全部 (共${activeCategoryCourses.length}门课程)` }}
        </button>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!courseDescription && totalCourses === 0" class="text-center py-4">
      <span class="text-gray-400 text-sm">暂无课程设置信息</span>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'

// Props定义
const props = defineProps({
  // 课程设置字符串
  courseStructure: {
    type: String,
    default: ''
  },
  // 是否使用紧凑模式
  compact: {
    type: Boolean,
    default: false
  },
  // 自定义样式类
  customClass: {
    type: String,
    default: ''
  },
  // 是否在弹窗中使用
  inDialog: {
    type: Boolean,
    default: false
  }
})

// 响应式状态
const activeCategory = ref('全部')
const isExpanded = ref(false)

// 清理HTML标签和特殊字符
const cleanHtmlText = (text) => {
  if (!text) return ''
  
  return text
    .replace(/<br\s*\/?>/gi, '\n') // 替换<br/>为换行
    .replace(/<[^>]*>/g, '') // 移除所有HTML标签
    .replace(/&nbsp;/g, ' ') // 替换&nbsp;为空格
    .replace(/&amp;/g, '&') // 替换&amp;为&
    .replace(/&lt;/g, '<') // 替换&lt;为<
    .replace(/&gt;/g, '>') // 替换&gt;为>
    .trim()
}

// 解析课程结构
const parsedStructure = computed(() => {
  if (!props.courseStructure || props.courseStructure.trim() === '') {
    return {
      description: '',
      courses: [],
      categories: []
    }
  }

  const cleanedText = cleanHtmlText(props.courseStructure)
  
  try {
    // 分离课程描述和课程列表
    const parts = cleanedText.split(/课程列表[：:]/i)
    const description = parts[0]?.replace(/课程描述[：:]?/i, '').trim() || ''
    const courseListText = parts[1] || ''

    // 解析课程列表
    const courses = []
    if (courseListText) {
      const courseLines = courseListText.split('\n').filter(line => line.trim())
      
      for (const line of courseLines) {
        const course = parseCourse(line.trim())
        if (course) {
          courses.push(course)
        }
      }
    }

    // 按类型分类课程
    const categories = categorizeCourses(courses)

    return {
      description,
      courses,
      categories
    }
  } catch (error) {
    console.warn('解析课程结构失败:', error)
    return {
      description: cleanedText,
      courses: [],
      categories: []
    }
  }
})

// 解析单个课程
const parseCourse = (courseText) => {
  if (!courseText) return null

  try {
    // 格式：课程中文名 | 课程英文名 | 课程类型
    const parts = courseText.split('|').map(part => part.trim())
    
    if (parts.length >= 1) {
      const nameCn = parts[0] || ''
      const nameEn = parts[1] || ''
      const typeText = parts[2] || '其他课程'
      
      // 确定课程类型
      let type = 'other'
      if (typeText.includes('必修')) {
        type = 'required'
      } else if (typeText.includes('选修')) {
        type = 'elective'
      } else if (typeText.includes('核心')) {
        type = 'core'
      }

      return {
        id: `course_${Date.now()}_${Math.random()}`,
        nameCn,
        nameEn,
        type,
        typeText,
        credits: extractCredits(courseText)
      }
    }
  } catch (error) {
    console.warn('解析课程失败:', error, courseText)
  }

  return null
}

// 提取学分信息
const extractCredits = (text) => {
  const creditMatch = text.match(/(\d+)\s*学分/i)
  return creditMatch ? creditMatch[1] : null
}

// 课程分类
const categorizeCourses = (courses) => {
  const categories = []
  const categoryMap = new Map()

  // 按类型分组
  for (const course of courses) {
    const categoryName = getCourseTypeText(course.type)
    
    if (!categoryMap.has(categoryName)) {
      categoryMap.set(categoryName, {
        name: categoryName,
        courses: []
      })
    }
    
    categoryMap.get(categoryName).courses.push(course)
  }

  // 添加"全部"分类
  if (courses.length > 0) {
    categories.push({
      name: '全部',
      courses: [...courses]
    })
  }

  // 添加其他分类
  categories.push(...Array.from(categoryMap.values()))

  return categories
}

// 计算属性
const courseDescription = computed(() => parsedStructure.value.description)
const courseCategories = computed(() => parsedStructure.value.categories)
const totalCourses = computed(() => parsedStructure.value.courses.length)
const requiredCourses = computed(() => 
  parsedStructure.value.courses.filter(course => course.type === 'required').length
)
const electiveCourses = computed(() => 
  parsedStructure.value.courses.filter(course => course.type === 'elective').length
)

const activeCategoryCourses = computed(() => {
  const category = courseCategories.value.find(cat => cat.name === activeCategory.value)
  return category ? category.courses : []
})

// 在弹窗模式下限制显示的课程数量
const displayedCourses = computed(() => {
  const courses = activeCategoryCourses.value
  if (props.inDialog && !isExpanded.value) {
    return courses.slice(0, 5)
  }
  return courses
})

// 判断是否需要显示展开按钮
const shouldShowExpandButton = computed(() => {
  if (props.inDialog) {
    return activeCategoryCourses.value.length > 5
  }
  return totalCourses.value > 5
})

// 方法
const getCourseTypeText = (type) => {
  const typeMap = {
    required: '必修课程',
    elective: '选修课程',
    core: '核心课程',
    other: '其他课程'
  }
  return typeMap[type] || '其他课程'
}

const truncateText = (text, maxLength) => {
  if (!text || text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

// 监听分类变化，初始化活跃分类
watch(courseCategories, (newCategories) => {
  if (newCategories.length > 0 && !activeCategory.value) {
    activeCategory.value = newCategories[0].name
  }
}, { immediate: true })
</script>

<style scoped>
.course-structure {
  @apply w-full;
}

/* 紧凑模式样式 */
.compact-structure {
  @apply space-y-2;
}

.course-summary {
  @apply bg-gray-50 rounded-lg p-3;
}

/* 弹窗模式下的样式调整 */
.dialog-mode .course-summary.dialog-summary {
  @apply bg-gray-50 rounded p-2;
  font-size: 0.75rem;
}

.summary-stats {
  @apply flex items-center space-x-4 mb-2;
}

.stat-item {
  @apply flex items-center space-x-1 text-sm;
}

.stat-item .stat-number {
  @apply font-bold text-primary;
}

.stat-item .stat-label {
  @apply text-gray-600;
}

.course-description-compact {
  @apply text-xs text-gray-600 leading-relaxed;
}

/* 完整模式样式 */
.full-structure {
  @apply space-y-4;
}

.course-description {
  @apply bg-purple-50 rounded-lg p-4;
}

.description-title {
  @apply text-sm font-semibold text-purple-800 mb-2;
}

.description-content {
  @apply text-sm text-purple-700 leading-relaxed;
}

/* 课程统计 */
.course-stats {
  @apply bg-white rounded-lg border border-gray-200 p-4;
}

.stats-grid {
  @apply grid grid-cols-1 sm:grid-cols-3 gap-4;
}

.stat-card {
  @apply flex items-center space-x-3 p-3 bg-gray-50 rounded-lg;
}

.stat-card.required {
  @apply bg-purple-50;
}

.stat-card.elective {
  @apply bg-purple-50;
}

.stat-card .stat-icon {
  @apply text-gray-500;
}

.stat-card.required .stat-icon {
  @apply text-purple-600;
}

.stat-card.elective .stat-icon {
  @apply text-purple-600;
}

.stat-card .stat-info {
  @apply flex-1;
}

.stat-card .stat-number {
  @apply text-lg font-bold text-gray-800;
}

.stat-card .stat-label {
  @apply text-xs text-gray-600;
}

/* 课程分类 */
.course-categories {
  @apply bg-white rounded-lg border border-gray-200 overflow-hidden;
}

.categories-header {
  @apply p-4 border-b border-gray-200;
}

.categories-title {
  @apply text-sm font-semibold text-gray-800 mb-3;
}

.categories-tabs {
  @apply flex flex-wrap gap-2;
}

.category-tab {
  @apply px-3 py-1.5 text-xs font-medium rounded-full border transition-colors;
  @apply border-gray-200 text-gray-600 hover:border-primary hover:text-primary;
}

.category-tab.active {
  @apply bg-primary text-white border-primary;
}

.category-tab .course-count {
  @apply ml-1 opacity-75;
}

/* 课程列表 */
.course-list {
  @apply divide-y divide-gray-100;
}

.course-item {
  @apply flex items-center justify-between p-4 hover:bg-gray-50 transition-colors;
}

.course-main {
  @apply flex-1 min-w-0;
}

.course-name-cn {
  @apply text-sm font-medium text-gray-800 truncate;
}

.course-name-en {
  @apply text-xs text-gray-500 truncate mt-0.5;
}

.course-meta {
  @apply flex items-center space-x-2 ml-4;
}

.course-type-badge {
  @apply px-2 py-1 text-xs font-medium rounded-full;
}

.course-type-badge.required {
  @apply bg-green-100 text-green-800;
}

.course-type-badge.elective {
  @apply bg-yellow-100 text-yellow-800;
}

.course-type-badge.core {
  @apply bg-blue-100 text-blue-800;
}

.course-type-badge.other {
  @apply bg-gray-100 text-gray-800;
}

.course-credits {
  @apply text-xs text-gray-500;
}

/* 展开控制 */
.expand-controls {
  @apply text-center pt-3 border-t border-gray-100;
}

.expand-button {
  @apply inline-flex items-center space-x-1 px-4 py-2 text-sm font-medium;
  @apply border border-gray-200 rounded-full text-gray-600 hover:text-purple-600;
  @apply hover:border-purple-200 hover:bg-purple-50 transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2;
}

/* 弹窗模式下的展开按钮样式 */
.dialog-mode .expand-button {
  @apply bg-gradient-to-r from-purple-50 to-indigo-50 border-purple-200 text-purple-700;
  @apply hover:from-purple-500 hover:to-indigo-500 hover:border-purple-500 hover:text-white;
  @apply text-xs px-3 py-1.5 shadow-sm hover:shadow-md;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .stats-grid {
    @apply grid-cols-1;
  }
  
  .categories-tabs {
    @apply flex-col;
  }
  
  .course-item {
    @apply flex-col items-start space-y-2;
  }
  
  .course-meta {
    @apply ml-0 self-end;
  }
}
</style>
