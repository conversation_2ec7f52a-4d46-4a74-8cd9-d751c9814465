<template>
  <div class="animated-input-container" :class="{ 'is-active': isActive || hasValue, 'has-error': hasError }">
    <!-- 浮动标签 -->
    <div 
      class="animated-label"
      :class="{ 
        'is-active': isActive || hasValue,
        'is-required': required
      }"
    >
      {{ label }}
      <!-- 仅在激活状态显示必选标记，避免重复 -->
      <span v-if="required && (isActive || hasValue)" class="text-red-600">*</span>
    </div>
    
    <!-- 输入区域 - 根据类型显示不同内容 -->
    <div class="input-wrapper" @click="handleWrapperClick">
      <!-- 普通输入框 -->
      <template v-if="type === 'input'">
        <div 
          ref="inputRef"
          class="animated-input-wrapper"
          :class="{ 'is-active': isActive, 'has-error': hasError }"
        >
          <input
            :value="modelValue"
            :placeholder="isActive ? placeholder : ''"
            class="animated-input"
            @focus="handleFocus"
            @blur="handleBlur"
            @input="handleInput($event)"
            @change="handleChange($event)"
          />
        </div>
      </template>
      
      <!-- 下拉选择框 -->
      <template v-else-if="type === 'select'">
        <div 
          ref="inputRef"
          class="animated-select"
          :class="{ 'is-active': isActive, 'has-error': hasError }"
          tabindex="0"
          @focus="handleFocus"
          @blur="handleBlur($event, true)"
        >
          <!-- 当激活且无值时显示 placeholder -->
          <div v-if="isActive && !hasValue" class="placeholder-text">
            {{ placeholder }}
          </div>
          <!-- 多选模式下显示标签 -->
          <div class="selected-value" v-else-if="hasValue && props.multiple">
            <div class="tags-container">
              <span 
                v-for="selectedValue in props.modelValue" 
                :key="selectedValue"
                class="tag-item"
                @click.stop="removeSelectedValue(selectedValue)"
              >
                {{ getOptionLabel(selectedValue) }}
                <span class="tag-close">×</span>
              </span>
            </div>
          </div>
          <!-- 单选模式下显示选中值 -->
          <div class="selected-value" v-else-if="hasValue && !props.multiple && !isActive">
            {{ getDisplayValue }}
          </div>
          
          <!-- 可点击的下拉箭头按钮 -->
          <button 
            type="button"
            class="dropdown-arrow-btn"
            :class="{ 'is-active': isActive }"
            @click.stop="toggleDropdown"
            @mousedown.prevent
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          
          <!-- 传送门，将下拉菜单移到body直接子元素，避免嵌套和overflow限制 -->
          <Teleport to="body">
            <div 
              v-if="isActive" 
              class="options-container" 
              :style="dropdownStyle"
              @mousedown.prevent
              @touchstart.prevent.stop
              @touchend.prevent.stop
            >
              <!-- 如果有分组选项，使用分组渲染 -->
              <template v-if="optionGroups.length > 0">
                <div
                  v-for="(group, groupIndex) in optionGroups"
                  :key="`group-${groupIndex}`"
                  class="option-group"
                >
                  <div 
                    class="group-label"
                    @mousedown="toggleGroup(groupIndex)"
                    @touchend.prevent.stop="toggleGroup(groupIndex)"
                  >
                    <span>{{ group.label }}</span>
                    <svg 
                      class="group-arrow"
                      :class="{ 'is-expanded': expandedGroups[groupIndex] }"
                      xmlns="http://www.w3.org/2000/svg" 
                      fill="none" 
                      viewBox="0 0 24 24" 
                      stroke="currentColor"
                    >
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                  <Transition name="group-expand">
                    <div 
                      v-if="expandedGroups[groupIndex]"
                      class="group-options"
                    >
                    <div
                      v-for="(option, optionIndex) in group.options"
                      :key="`${groupIndex}-${optionIndex}`"
                      class="option-item"
                      :class="{ 'is-selected': isSelected(option) }"
                      @mousedown="handleOptionSelect(option)"
                      @touchend.prevent.stop="handleOptionSelect(option)"
                    >
                      {{ option.label }}
                      <span v-if="option.secondaryText" class="secondary-text">{{ option.secondaryText }}</span>
                    </div>
                  </div>
                </Transition>
              </div>
            </template>
              <!-- 否则使用常规选项渲染 -->
              <template v-else>
                <div
                  v-for="(option, index) in options"
                  :key="index"
                  class="option-item"
                  :class="{ 'is-selected': isSelected(option) }"
                  @mousedown="handleOptionSelect(option)"
                  @touchend.prevent.stop="handleOptionSelect(option)"
                >
                  {{ option.label }}
                  <span v-if="option.secondaryText" class="secondary-text">{{ option.secondaryText }}</span>
                </div>
              </template>
            </div>
          </Teleport>
        </div>
      </template>

      <!-- 自动完成输入框 -->
      <template v-else-if="type === 'autocomplete'">
        <div 
          ref="inputRef"
          class="animated-autocomplete"
          :class="{ 'is-active': isActive, 'has-error': hasError }"
        >
          <input
            :value="modelValue"
            :placeholder="isActive ? placeholder : ''"
            class="autocomplete-input"
            @focus="handleFocus"
            @blur="handleBlur($event, true)"
            @input="handleAutoCompleteInput($event)"
            @click="handleAutoCompleteClick($event)"
          />
          
          <!-- 可点击的下拉箭头按钮 -->
          <button 
            type="button"
            class="dropdown-arrow-btn"
            :class="{ 'is-active': isActive }"
            @click.stop="toggleDropdown"
            @mousedown.prevent
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          
          <!-- 传送门，将下拉菜单移到body直接子元素 -->
          <Teleport to="body">
            <div 
              v-if="isActive && suggestions.length > 0" 
              class="options-container" 
              :style="dropdownStyle"
              @mousedown.prevent
              @touchstart.prevent.stop
              @touchend.prevent.stop
            >
              <div
                v-for="(suggestion, index) in suggestions"
                :key="index"
                class="option-item"
                @mousedown="handleSuggestionSelect(suggestion)"
                @touchend.prevent.stop="handleSuggestionSelect(suggestion)"
              >
                {{ typeof suggestion === 'object' ? suggestion.label || suggestion.value : suggestion }}
                <span v-if="typeof suggestion === 'object' && suggestion.secondaryText" class="secondary-text">
                  {{ suggestion.secondaryText }}
                </span>
              </div>
            </div>
          </Teleport>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount, reactive } from 'vue';

const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    default: ''
  },
  label: {
    type: String,
    required: true
  },
  placeholder: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'input',
    validator: (value) => ['input', 'select', 'autocomplete'].includes(value)
  },
  options: {
    type: Array,
    default: () => []
  },
  optionGroups: {
    type: Array,
    default: () => []
  },
  multiple: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
  fetchSuggestions: {
    type: Function,
    default: null
  },
  hasError: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'focus', 'blur', 'change', 'input']);

// 响应式状态
const isActive = ref(false);
const inputRef = ref(null);
const suggestions = ref([]);
const expandedGroups = ref({});

// 下拉框样式
const dropdownStyle = reactive({
  position: 'fixed',
  top: '0px',
  left: '0px',
  width: '100px',
  backgroundColor: '#ffffff',
  zIndex: 99999,
  maxHeight: '320px',
  overflowY: 'auto',
  border: '1px solid #e5e7eb',
  borderRadius: '0.5rem',
  boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
});

// 计算值是否存在
const hasValue = computed(() => {
  if (Array.isArray(props.modelValue)) {
    return props.modelValue.length > 0;
  }
  return props.modelValue !== null && props.modelValue !== undefined && props.modelValue !== '';
});

// 获取所有选项（包括分组中的选项）
const getAllOptions = computed(() => {
  if (props.optionGroups.length > 0) {
    return props.optionGroups.flatMap(group => group.options);
  }
  return props.options;
});

// 获取显示值
const getDisplayValue = computed(() => {
  if (props.type === 'select') {
    if (props.multiple && Array.isArray(props.modelValue)) {
      if (props.modelValue.length === 0) return '';
      
      return getAllOptions.value
        .filter(option => props.modelValue.includes(option.value))
        .map(option => option.label)
        .join(', ');
    } else {
      const selected = getAllOptions.value.find(option => option.value === props.modelValue);
      return selected ? selected.label : '';
    }
  }
  return props.modelValue;
});

// 获取选项标签
const getOptionLabel = (value) => {
  const option = getAllOptions.value.find(opt => opt.value === value);
  return option ? option.label : value;
};

// 移除选中的值（用于多选标签的删除）
const removeSelectedValue = (valueToRemove) => {
  if (props.multiple && Array.isArray(props.modelValue)) {
    const newValue = props.modelValue.filter(value => value !== valueToRemove);
    emit('update:modelValue', newValue);
    emit('change', newValue);
  }
};

// 检查选项是否被选中
const isSelected = (option) => {
  if (props.multiple && Array.isArray(props.modelValue)) {
    return props.modelValue.includes(option.value);
  }
  return props.modelValue === option.value;
};

// 切换分组展开/折叠状态
const toggleGroup = (groupIndex) => {
  expandedGroups.value[groupIndex] = !expandedGroups.value[groupIndex];
  
  // 在下一帧重新计算下拉框位置
  setTimeout(() => {
    setDropdownPosition();
  }, 10);
};

// 设置下拉框位置
const setDropdownPosition = () => {
  if (!inputRef.value || !isActive.value) return;
  
  const inputEl = inputRef.value;
  const rect = inputEl.getBoundingClientRect();
  
  // 计算可用空间
  const viewportHeight = window.innerHeight;
  const scrollY = window.scrollY || document.documentElement.scrollTop;
  const spaceBelow = viewportHeight - rect.bottom;
  const spaceAbove = rect.top;
  
  // 估算下拉框高度
  const estimatedItemCount = Math.max(suggestions.value.length, props.options.length);
  const estimatedHeight = Math.min(320, estimatedItemCount * 40 + 8); // 每项40px高度 + 内边距
  
  // 决定显示位置：优先显示在下方，空间不足时显示在上方
  const showBelow = spaceBelow >= estimatedHeight || spaceBelow > spaceAbove;
  
  // 更新下拉框基本位置和高度
  dropdownStyle.maxHeight = showBelow ? `${Math.min(320, spaceBelow - 8)}px` : `${Math.min(320, spaceAbove - 8)}px`;
  
  if (showBelow) {
    dropdownStyle.top = `${rect.bottom + scrollY + 2}px`;
  } else {
    dropdownStyle.top = `${rect.top + scrollY - Math.min(320, estimatedHeight) - 2}px`;
  }
  
  // 防止下拉框超出屏幕左右边界
  const viewportWidth = window.innerWidth;
  const minMargin = window.innerWidth <= 768 ? 8 : 16; // 移动设备使用更小的边距
  
  // 计算下拉框最大宽度（在移动设备上可以稍微超出输入框宽度）
  const maxDropdownWidth = Math.min(rect.width, viewportWidth - minMargin * 2);
  dropdownStyle.width = `${maxDropdownWidth}px`;
  
  // 调整左边距以确保下拉框完全可见
  let leftPosition = rect.left + window.scrollX;
  const maxLeft = viewportWidth - maxDropdownWidth - minMargin;
  
  if (leftPosition > maxLeft) {
    leftPosition = maxLeft;
  }
  if (leftPosition < minMargin) {
    leftPosition = minMargin;
  }
  
  dropdownStyle.left = `${leftPosition}px`;
};

// 处理容器点击
const handleWrapperClick = () => {
  if (inputRef.value) {
    inputRef.value.focus();
  }
  isActive.value = true;
  
  // 点击时更新下拉框位置
  setDropdownPosition();
};

// 切换下拉框状态
const toggleDropdown = () => {
  isActive.value = !isActive.value;
  
  if (isActive.value) {
    // 打开下拉框时设置位置
    setDropdownPosition();
    
    // 对于自动完成类型，触发建议获取
    if (props.type === 'autocomplete' && props.fetchSuggestions && typeof props.fetchSuggestions === 'function') {
      props.fetchSuggestions('', (results) => {
        suggestions.value = results || [];
        setDropdownPosition();
      });
    }
  } else {
    // 关闭下拉框
    suggestions.value = [];
    emit('blur');
  }
};

// 处理获得焦点
const handleFocus = () => {
  isActive.value = true;
  emit('focus');
  
  // 设置下拉框位置
  setDropdownPosition();
  
  // 对于自动完成输入框，根据当前输入值获取建议
  if (props.type === 'autocomplete' && props.fetchSuggestions && typeof props.fetchSuggestions === 'function') {
    const currentValue = props.modelValue || '';
    props.fetchSuggestions(currentValue, (results) => {
      suggestions.value = results || [];
      setDropdownPosition();
    });
  }
};

// 处理普通输入框的input事件
const handleInput = (event) => {
  const value = event.target.value;
  emit('update:modelValue', value);
  emit('input', value);
};

// 处理普通输入框的change事件
const handleChange = (event) => {
  const value = event.target.value;
  emit('change', value);
};

// 处理全局点击事件，用于关闭下拉框
const handleGlobalClick = (event) => {
  if (!isActive.value) return;

  const targetEl = event.target;
  const containerEl = inputRef.value?.closest('.animated-input-container');
  
  // 如果点击的不是组件内部元素，则关闭下拉框
  if (containerEl && !containerEl.contains(targetEl) && 
      !targetEl.classList.contains('option-item') && 
      !targetEl.closest('.options-container')) {
    isActive.value = false;
    emit('blur');
  }
};

// 处理全局触摸事件，用于关闭下拉框
const handleGlobalTouch = (event) => {
  if (!isActive.value) return;

  const targetEl = event.target;
  const containerEl = inputRef.value?.closest('.animated-input-container');
  
  // 如果触摸的不是组件内部元素，则关闭下拉框
  if (containerEl && !containerEl.contains(targetEl) && 
      !targetEl.classList.contains('option-item') && 
      !targetEl.closest('.options-container')) {
    isActive.value = false;
    emit('blur');
  }
};

// 处理失去焦点
const handleBlur = (event, isSelectEvent = false) => {
  // 如果是选择事件，不立即关闭下拉菜单
  if (isSelectEvent) {
    // 延迟处理，避免与选项点击事件冲突
    setTimeout(() => {
      // 此处不直接关闭，而是由全局点击事件处理
    }, 50);
  } else {
    isActive.value = false;
    emit('blur');
  }
};

// 处理选项选择
const handleOptionSelect = (option) => {
  if (props.multiple) {
    const newValue = Array.isArray(props.modelValue) ? [...props.modelValue] : [];
    const index = newValue.indexOf(option.value);
    
    if (index === -1) {
      newValue.push(option.value);
    } else {
      newValue.splice(index, 1);
    }
    
    emit('update:modelValue', newValue);
    emit('change', newValue);
    
    // 多选模式下保持下拉框打开
    setDropdownPosition();
  } else {
    // 单选模式：如果点击的是已选中的选项，则取消选择
    if (props.modelValue === option.value) {
      emit('update:modelValue', '');
      emit('change', '');
    } else {
      emit('update:modelValue', option.value);
      emit('change', option.value);
    }
    isActive.value = false;
  }
};

// 处理自动完成输入
const handleAutoCompleteInput = (event) => {
  const value = event.target.value;
  emit('update:modelValue', value);
  emit('input', value);
  
  if (props.fetchSuggestions && typeof props.fetchSuggestions === 'function') {
    // 确保isActive为true以显示下拉选项
    isActive.value = true;
    
    // 调用建议获取函数
    props.fetchSuggestions(value, (results) => {
      suggestions.value = results || [];
      
      // 只要输入框是聚焦状态，就保持下拉框显示
      if (document.activeElement === event.target) {
        isActive.value = true;
        setDropdownPosition();
      }
    });
  }
};

// 处理建议选择
const handleSuggestionSelect = (suggestion) => {
  const value = typeof suggestion === 'object' ? suggestion.value : suggestion;
  emit('update:modelValue', value);
  emit('change', value); // 增加触发change事件
  // 选择后立即关闭下拉框
  isActive.value = false;
  suggestions.value = [];
};

// 处理自动完成输入框点击
const handleAutoCompleteClick = (event) => {
  isActive.value = true;
  setDropdownPosition();
  
  // 如果点击时有输入值，立即获取建议
  if (props.fetchSuggestions && typeof props.fetchSuggestions === 'function') {
    const currentValue = props.modelValue || '';
    props.fetchSuggestions(currentValue, (results) => {
      suggestions.value = results || [];
      setDropdownPosition();
    });
  }
};

// 添加和移除全局点击事件监听
onMounted(() => {
  document.addEventListener('click', handleGlobalClick);
  document.addEventListener('touchend', handleGlobalTouch);
  window.addEventListener('resize', setDropdownPosition);
  document.addEventListener('scroll', setDropdownPosition, true);
  
  // 监听屏幕方向变化
  window.addEventListener('orientationchange', () => {
    setTimeout(setDropdownPosition, 100);
  });
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleGlobalClick);
  document.removeEventListener('touchend', handleGlobalTouch);
  window.removeEventListener('resize', setDropdownPosition);
  document.removeEventListener('scroll', setDropdownPosition, true);
  window.removeEventListener('orientationchange', setDropdownPosition);
});

// 监听 isActive 状态变化，更新下拉框位置
watch(() => isActive.value, (newVal) => {
  if (newVal) {
    setDropdownPosition();
  }
});

// 初始化分组展开状态
const initGroupStates = () => {
  if (props.optionGroups.length > 0) {
    const states = {};
    props.optionGroups.forEach((group, index) => {
      // 保留已有的展开状态，如果没有则默认折叠
      states[index] = expandedGroups.value[index] ?? false;
    });
    expandedGroups.value = states;
  }
};

// 监听optionGroups变化，只在真正结构变化时重新初始化状态
watch(() => props.optionGroups, (newGroups, oldGroups) => {
  // 只有在分组数量发生变化或首次加载时才重新初始化
  if (!oldGroups || newGroups.length !== oldGroups.length || Object.keys(expandedGroups.value).length === 0) {
    initGroupStates();
  }
}, { immediate: true });
</script>

<style>
/* 全局样式，解决下拉框透明问题 */
.options-container {
  background-color: #ffffff !important;
  max-height: 24rem;
  overflow-y: auto;
  border: 1px solid #E2E8F0;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  margin-top: 0.25rem;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  
  /* 添加出现动画 */
  opacity: 0;
  transform: translateY(-8px) scale(0.95);
  animation: dropdown-appear 0.2s ease-out forwards;
}

/* 下拉框出现动画 */
@keyframes dropdown-appear {
  0% {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 优化滚动条样式 */
.options-container::-webkit-scrollbar {
  width: 0.25rem;
}

.options-container::-webkit-scrollbar-track {
  background: #F8FAFC;
  border-radius: 0.25rem;
}

.options-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #CBD5E1 0%, #94A3B8 100%);
  border-radius: 0.25rem;
  transition: background 0.2s ease;
}

.options-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #94A3B8 0%, #64748B 100%);
}

.options-container .option-item {
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  transition: all 0.15s ease;
  font-size: 0.875rem;
  display: flex;
  flex-direction: column;
  background-color: #ffffff !important;
  position: relative;
  min-height: 44px; /* 确保触摸目标足够大 */
  align-items: flex-start;
  justify-content: center;
  -webkit-tap-highlight-color: transparent; /* 移除WebKit的点击高亮 */
  user-select: none; /* 防止文本选择 */
}

.options-container .option-item:hover {
  background-color: #F8FAFC !important;
}

.options-container .option-item.is-selected {
  background-color: #EEF2FF !important;
  color: #4F46E5;
  font-weight: 500;
}

.options-container .secondary-text {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.125rem;
}

/* 分组选项样式 */
.options-container .option-group {
  margin-bottom: 0;
}

.options-container .group-label {
  padding: 0.75rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  background: #F8FAFC !important;
  user-select: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.15s ease;
  min-height: 44px; /* 确保触摸目标足够大 */
  -webkit-tap-highlight-color: transparent; /* 移除WebKit的点击高亮 */
}

.options-container .group-label:hover {
  background: #F1F5F9 !important;
  color: #4F46E5;
}

.options-container .group-arrow {
  width: 1rem;
  height: 1rem;
  transition: transform 0.2s ease;
  color: #6B7280;
}

.options-container .group-arrow.is-expanded {
  transform: rotate(180deg);
  color: #4F46E5;
}

.options-container .group-options {
  overflow: hidden;
}

/* 分组展开动画 */
.group-expand-enter-active,
.group-expand-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.group-expand-enter-from,
.group-expand-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

.group-expand-enter-to,
.group-expand-leave-from {
  max-height: 400px;
  opacity: 1;
  transform: translateY(0);
}

.options-container .option-group .option-item {
  padding: 0.5rem 0.75rem 0.5rem 1.5rem;
  background-color: #ffffff !important;
  transition: all 0.15s ease;
  position: relative;
}

.options-container .option-group .option-item:hover {
  background-color: #F8FAFC !important;
}

.options-container .option-group .option-item.is-selected {
  background-color: #EEF2FF !important;
  color: #4F46E5;
  font-weight: 500;
}

/* 添加全局统一间距类 */
.form-section-content .animated-input-container:last-child {
  margin-bottom: 0;
}

/* 修复网格布局间距问题 */
.grid.grid-cols-2 .animated-input-container {
  margin-bottom: 0;
}

/* 确保网格布局中的容器有固定的底部边距 */
.grid.grid-cols-2 {
  margin-bottom: 0.75rem;
}

/* 确保网格布局中输入框的一致性 */
.grid.grid-cols-2 .animated-input-wrapper,
.grid.grid-cols-2 .animated-autocomplete,
.grid.grid-cols-2 .animated-select {
  height: 2.5rem !important;
}

/* 确保网格布局中输入值的垂直位置一致 - 使用统一的边距 */
.grid.grid-cols-2 .animated-input,
.grid.grid-cols-2 .autocomplete-input {
  padding: 1rem 0.4rem 0.3rem !important;
}

.grid.grid-cols-2 .autocomplete-input {
  padding-right: 2rem !important;
}

.grid.grid-cols-2 .selected-value,
.grid.grid-cols-2 .placeholder-text {
  padding-top: 0.5rem !important;
  padding-bottom: 0.3rem !important;
}

/* 统一所有输入框内部文本的水平和垂直位置 */
.animated-input,
.autocomplete-input {
  padding-left: 0.4rem !important;
  padding-right: 0.4rem !important;
}

/* 为有下拉箭头的输入框保留右侧空间 */
.autocomplete-input {
  padding-right: 2rem !important;
}

.animated-select {
  padding-left: 0.4rem !important;
  padding-right: 2rem !important;
}
</style>

<style scoped>
.animated-input-container {
  position: relative;
  margin-bottom: 0.5rem;
  width: 100%;
  height: 2.5rem !important; /* 固定高度确保一致性 */
}

.animated-label {
  position: absolute;
  left: 0.4rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.8125rem;
  color: #6b7280;
  transition: all 0.2s ease-in-out;
  pointer-events: none;
  padding: 0;
  background-color: transparent;
  z-index: 1;
}

/* 确保所有输入类型的标签位置一致 */
.animated-input-container .animated-label {
  left: 0.4rem !important;
}

.animated-label.is-active {
  top: 0rem;
  left: 0.4rem !important;
  transform: translateY(0) scale(0.85);
  transform-origin: left center;
  color: #4F46E5;
  background-color: transparent;
  font-weight: 500;
}



.animated-label.is-required:not(.is-active)::after {
  content: "*";
  color: #ef4444;
  margin-left: 0.005rem;
  position: relative;
  top: -0.125rem;
}

.input-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.animated-input-wrapper,
.animated-autocomplete {
  position: relative;
  padding: 0;
  display: flex;
  align-items: center;
  min-height: 2.5rem;
  width: 100%;
  height: 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: #fff;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.animated-input-wrapper.is-active,
.animated-autocomplete.is-active {
  border-color: #4F46E5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.animated-input-wrapper.has-error,
.animated-autocomplete.has-error,
.animated-select.has-error {
  border-color: #ef4444 !important;
}

.animated-input-wrapper.has-error.is-active,
.animated-autocomplete.has-error.is-active,
.animated-select.has-error.is-active {
  border-color: #ef4444 !important;
}

.animated-input,
.autocomplete-input {
  width: 100%;
  height: 100%;
  padding: 1rem 0.4rem 0.3rem;
  border: none;
  outline: none;
  background: transparent;
  color: #374151;
  font-size: 0.8125rem;
  z-index: 1;
}

/* 为自动完成输入框添加右侧内边距，为下拉箭头留出空间 */
.autocomplete-input {
  padding-right: 2rem;
}

.animated-select {
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  height: 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: #fff;
  transition: all 0.2s ease;
  box-sizing: border-box;
  padding: 1rem 2rem 0.3rem 0.4rem;
}

.animated-select.is-active {
  border-color: #4F46E5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 可点击的下拉箭头按钮样式 */
.dropdown-arrow-btn {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.5rem;
  height: 1.5rem;
  border: none;
  background: none;
  cursor: pointer;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
  z-index: 2;
}

.dropdown-arrow-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.dropdown-arrow-btn svg {
  width: 1rem;
  height: 1rem;
  transition: transform 0.2s ease;
}

.dropdown-arrow-btn.is-active svg {
  transform: rotate(180deg);
}

.dropdown-arrow-btn:focus {
  outline: none;
  background-color: #f3f4f6;
}

.selected-value {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.8125rem;
  color: #374151;
  padding-top: 0.5rem;
  padding-bottom: 0.3rem;
  padding-left: 0 !important;
}

.placeholder-text {
  font-size: 0.8125rem;
  color: #9ca3af;
  padding-top: 0.5rem;
  padding-bottom: 0.3rem;
  padding-left: 0 !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 多选标签样式 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  min-height: 1.125rem;
  padding-top: 0.125rem;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  background-color: #EEF2FF;
  color: #4F46E5;
  font-size: 0.6875rem;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  border: 1px solid #C7D2FE;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  line-height: 1.2;
}

.tag-item:hover {
  background-color: #E0E7FF;
  border-color: #A5B4FC;
}

.tag-close {
  margin-left: 0.1875rem;
  font-weight: bold;
  font-size: 0.75rem;
  line-height: 1;
  color: #6366F1;
  transition: color 0.2s ease;
}

.tag-item:hover .tag-close {
  color: #4338CA;
}

/* 调整多选模式下的选择框样式 */
.animated-select .selected-value .tags-container {
  margin-top: -0.125rem;
}

/* 移动设备触摸优化 */
@media (max-width: 768px) {
  .options-container {
    /* 在小屏幕上增大最大高度，但确保不超出视口 */
    max-height: calc(100vh - 120px);
    /* 在移动设备上增加边距 */
    margin: 0.5rem;
    /* 增加圆角 */
    border-radius: 0.75rem;
  }
  
  .options-container .option-item {
    /* 增大触摸目标 */
    min-height: 48px;
    padding: 0.75rem 1rem;
    font-size: 0.9375rem;
  }
  
  .options-container .group-label {
    /* 增大触摸目标 */
    min-height: 48px;
    padding: 0.75rem 1rem;
    font-size: 0.9375rem;
  }
  
  .options-container .option-group .option-item {
    padding: 0.75rem 1rem 0.75rem 2rem;
  }
  
  /* 优化滚动指示器 */
  .options-container::-webkit-scrollbar {
    width: 0.375rem;
  }
  
  .options-container::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.8);
    border-radius: 0.375rem;
  }
}

/* 针对高密度显示屏的优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .options-container {
    border-width: 0.5px;
  }
}

/* 防止在触摸设备上的意外滚动 */
.options-container {
  touch-action: pan-y;
  -webkit-overflow-scrolling: touch;
}
</style> 