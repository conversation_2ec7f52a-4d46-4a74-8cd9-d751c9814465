<template>
  <div class="application-timeline w-full" :class="{ 'dialog-mode': inDialog, 'compact-dialog': inDialog }">
    <!-- 入学季节标题 -->
    <div v-if="semesterTitle" class="semester-title" :class="inDialog ? 'mb-2' : 'mb-4'">
      <h3 class="font-semibold text-primary flex items-center" :class="inDialog ? 'text-sm' : 'text-lg'">
        <span class="material-icons-outlined text-primary mr-2" :class="inDialog ? 'text-sm' : ''">school</span>
        {{ semesterTitle }}
      </h3>
    </div>

    <!-- 紧凑模式：单行显示 -->
    <div v-if="compact" class="compact-timeline">
      <div class="flex items-center space-x-2" :class="inDialog ? 'text-xs' : 'text-sm'">
        <span
          v-for="(node, index) in filteredTimelineNodes"
          :key="index"
          class="inline-flex items-center"
        >
          <span
            :class="[getNodeStatusClass(node.status), 'px-2 py-1 rounded-full font-medium whitespace-nowrap text-xs']"
          >
            {{ node.name }}
            <span v-if="node.date" class="ml-1 opacity-75">
              ({{ formatDate(node.date) }})
            </span>
          </span>
          <span
            v-if="index < filteredTimelineNodes.length - 1"
            class="mx-1 text-gray-300"
          >
            |
          </span>
        </span>
      </div>
    </div>

    <!-- 完整模式：时间线展示 -->
    <div v-else class="full-timeline">
      <div class="relative">
        <!-- 时间线主线 -->
        <div class="absolute left-3 top-0 bottom-0 w-0.5 bg-gray-200" :class="inDialog ? 'left-2' : 'left-4'"></div>

        <!-- 时间节点 -->
        <div :class="inDialog ? 'space-y-2' : 'space-y-4'">
          <div
            v-for="(node, index) in filteredTimelineNodes"
            :key="index"
            class="relative flex items-start"
          >
            <!-- 节点圆点 -->
            <div class="relative z-10 flex items-center justify-center">
              <div
                :class="[getNodeDotClass(node.status), 'rounded-full border-2 flex items-center justify-center', inDialog ? 'w-6 h-6' : 'w-8 h-8']"
              >
                <span class="material-icons-outlined" :class="inDialog ? 'text-xs' : 'text-sm'">
                  {{ getNodeIcon(node.status) }}
                </span>
              </div>
            </div>

            <!-- 节点内容 -->
            <div :class="inDialog ? 'ml-2 flex-1 min-w-0' : 'ml-4 flex-1 min-w-0'">
              <div class="bg-white rounded-lg border border-gray-200 shadow-sm" :class="inDialog ? 'p-2' : 'p-3'">
                <div class="flex items-center justify-between">
                  <h4
                    :class="[getNodeTitleClass(node.status), 'font-medium', inDialog ? 'text-xs' : 'text-sm']"
                  >
                    {{ node.name }}
                  </h4>
                  <span
                    v-if="node.date"
                    :class="getNodeDateClass(node.status)"
                    class="text-xs"
                  >
                    {{ formatDate(node.date) }}
                  </span>
                </div>

                <!-- 状态标签 -->
                <div v-if="!inDialog" class="mt-2">
                  <span
                    :class="getNodeStatusClass(node.status)"
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                  >
                    <span
                      :class="getStatusDotClass(node.status)"
                      class="w-1.5 h-1.5 rounded-full mr-1.5"
                    ></span>
                    {{ getStatusText(node.status) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredTimelineNodes.length === 0 && !semesterTitle" class="text-center py-4">
      <span class="text-gray-400 text-sm">暂无申请时间信息</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props定义
const props = defineProps({
  // 申请时间字符串，支持多种格式
  applicationTime: {
    type: String,
    default: ''
  },
  // 是否使用紧凑模式
  compact: {
    type: Boolean,
    default: false
  },
  // 自定义样式类
  customClass: {
    type: String,
    default: ''
  },
  // 是否在弹窗中使用（调整样式以匹配弹窗设计）
  inDialog: {
    type: Boolean,
    default: false
  }
})

// 清理HTML标签和特殊字符
const cleanHtmlText = (text) => {
  if (!text) return ''
  
  return text
    .replace(/<br\s*\/?>/gi, ' ') // 替换<br/>为空格
    .replace(/<[^>]*>/g, '') // 移除所有HTML标签
    .replace(/&nbsp;/g, ' ') // 替换&nbsp;为空格
    .replace(/&amp;/g, '&') // 替换&amp;为&
    .replace(/&lt;/g, '<') // 替换&lt;为<
    .replace(/&gt;/g, '>') // 替换&gt;为>
    .replace(/\s+/g, ' ') // 合并多个空格为一个
    .trim()
}

// 提取入学季节标题
const semesterTitle = computed(() => {
  if (!props.applicationTime || props.applicationTime.trim() === '') {
    return ''
  }

  const cleanedTimeStr = cleanHtmlText(props.applicationTime)

  // 匹配入学季节信息，如 "25年秋季入学"、"2025年春季入学" 等
  const semesterMatch = cleanedTimeStr.match(/^([^:：]+(?:年|季)(?:入学|申请)?)[:：]?/)

  if (semesterMatch) {
    return semesterMatch[1].trim()
  }

  return ''
})

// 解析申请时间字符串
const timelineNodes = computed(() => {
  if (!props.applicationTime || props.applicationTime.trim() === '') {
    return []
  }

  // 清理HTML标签
  const cleanedTimeStr = cleanHtmlText(props.applicationTime)

  // 如果有入学季节标题，移除它
  let processedTimeStr = cleanedTimeStr
  if (semesterTitle.value) {
    processedTimeStr = cleanedTimeStr.replace(new RegExp(`^${semesterTitle.value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}[:：]?\\s*`), '')
  }

  const nodes = []
  
  try {
    // 处理多种分隔符格式
    let parts = []
    
    // 首先尝试以 | 分隔
    if (processedTimeStr.includes('|')) {
      parts = processedTimeStr.split('|').map(part => part.trim())
    }
    // 尝试以 ; 分隔
    else if (processedTimeStr.includes(';')) {
      parts = processedTimeStr.split(';').map(part => part.trim())
    }
    // 尝试以换行分隔
    else if (processedTimeStr.includes('\n')) {
      parts = processedTimeStr.split('\n').map(part => part.trim()).filter(part => part)
    }
    // 如果没有明显分隔符，尝试识别常见模式
    else {
      // 尝试匹配多个时间节点模式
      const timeNodePattern = /([^()]+)\(([^)]+)\)/g
      let match
      while ((match = timeNodePattern.exec(processedTimeStr)) !== null) {
        parts.push(match[0])
      }

      // 如果没有匹配到模式，将整个字符串作为一个节点
      if (parts.length === 0) {
        parts = [processedTimeStr]
      }
    }
    
    for (const part of parts) {
      if (!part.trim()) continue
      
      // 匹配多种格式：
      // 1. 节点名称(日期)
      // 2. 节点名称: 日期
      // 3. 日期 节点名称
      // 4. 纯节点名称
      let name = ''
      let dateStr = ''
      
      // 格式1: 节点名称(日期)
      let match = part.match(/^(.+?)\s*\(([^)]+)\)\s*$/)
      if (match) {
        name = match[1].trim()
        dateStr = match[2].trim()
      }
      // 格式2: 节点名称: 日期
      else {
        match = part.match(/^(.+?):\s*(.+)$/)
        if (match) {
          name = match[1].trim()
          dateStr = match[2].trim()
        }
        // 格式3: 日期 节点名称 (YYYY-MM-DD 开头)
        else {
          match = part.match(/^(\d{4}-\d{1,2}-\d{1,2})\s+(.+)$/)
          if (match) {
            dateStr = match[1].trim()
            name = match[2].trim()
          }
          // 格式4: 纯节点名称
          else {
            name = part.trim()
            dateStr = ''
          }
        }
      }
      
      // 解析日期
      let date = null
      if (dateStr) {
        date = parseDate(dateStr)
      }
      
      // 确定节点状态
      const status = determineNodeStatus(name, date)
      
      nodes.push({
        name,
        date,
        status,
        originalText: part
      })
    }
  } catch (error) {
    console.warn('解析申请时间失败:', error)
    // 如果解析失败，返回原始文本作为单个节点
    return [{
      name: processedTimeStr,
      date: null,
      status: 'unknown',
      originalText: processedTimeStr
    }]
  }

  return nodes.filter(node => node.name) // 过滤掉空名称的节点
})

// 过滤后的时间节点（排除入学季节信息）
const filteredTimelineNodes = computed(() => {
  return timelineNodes.value.filter(node => {
    // 过滤掉可能是入学季节信息的节点
    const name = node.name.toLowerCase()
    // 保留包含申请相关关键词的节点
    return name.includes('申请') ||
           name.includes('截止') ||
           name.includes('开放') ||
           name.includes('开始') ||
           name.includes('结束') ||
           name.includes('round') ||
           name.includes('轮') ||
           name.includes('deadline') ||
           name.includes('due') ||
           (!name.includes('入学') && !name.includes('季'))
  })
})

// 解析日期字符串
const parseDate = (dateStr) => {
  if (!dateStr) return null

  try {
    // 处理常见的日期格式
    const cleanDateStr = dateStr.trim()

    // 格式：YYYY-MM-DD
    if (/^\d{4}-\d{1,2}-\d{1,2}$/.test(cleanDateStr)) {
      return new Date(cleanDateStr)
    }

    // 格式：YYYY/MM/DD
    if (/^\d{4}\/\d{1,2}\/\d{1,2}$/.test(cleanDateStr)) {
      return new Date(cleanDateStr.replace(/\//g, '-'))
    }

    // 其他格式尝试直接解析
    const parsed = new Date(cleanDateStr)
    return isNaN(parsed.getTime()) ? null : parsed
  } catch (error) {
    return null
  }
}

// 确定节点状态
const determineNodeStatus = (name, date) => {
  const now = new Date()
  const nameStr = name.toLowerCase()

  // 如果没有日期，根据名称判断
  if (!date) {
    if (nameStr.includes('开放') || nameStr.includes('开始')) {
      return 'active'
    }
    if (nameStr.includes('截止') || nameStr.includes('结束')) {
      return 'pending'
    }
    return 'unknown'
  }

  // 根据日期和当前时间判断状态
  if (date < now) {
    // 已过期
    if (nameStr.includes('截止') || nameStr.includes('结束')) {
      return 'expired'
    } else {
      return 'completed'
    }
  } else {
    // 未来时间
    if (nameStr.includes('开放') || nameStr.includes('开始')) {
      return 'pending'
    } else {
      return 'active'
    }
  }
}

// 格式化日期显示
const formatDate = (date) => {
  if (!date) return ''

  try {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')

    // 如果是当年，只显示月日
    const currentYear = new Date().getFullYear()
    if (year === currentYear) {
      return `${month}-${day}`
    }

    return `${year}-${month}-${day}`
  } catch (error) {
    return ''
  }
}

// 获取节点状态样式类
const getNodeStatusClass = (status) => {
  const baseClass = 'inline-flex items-center'

  switch (status) {
    case 'completed':
      return `${baseClass} bg-green-50 text-green-700 border border-green-200`
    case 'active':
      return `${baseClass} bg-primary/10 text-primary border border-primary/20`
    case 'pending':
      return `${baseClass} bg-yellow-50 text-yellow-700 border border-yellow-200`
    case 'expired':
      return `${baseClass} bg-red-50 text-red-700 border border-red-200`
    default:
      return `${baseClass} bg-gray-50 text-gray-600 border border-gray-200`
  }
}

// 获取节点圆点样式类
const getNodeDotClass = (status) => {
  switch (status) {
    case 'completed':
      return 'bg-green-500 border-green-500 text-white'
    case 'active':
      return 'bg-primary border-primary text-white'
    case 'pending':
      return 'bg-yellow-500 border-yellow-500 text-white'
    case 'expired':
      return 'bg-red-500 border-red-500 text-white'
    default:
      return 'bg-gray-400 border-gray-400 text-white'
  }
}

// 获取节点图标
const getNodeIcon = (status) => {
  switch (status) {
    case 'completed':
      return 'check'
    case 'active':
      return 'schedule'
    case 'pending':
      return 'hourglass_empty'
    case 'expired':
      return 'close'
    default:
      return 'help_outline'
  }
}

// 获取节点标题样式类
const getNodeTitleClass = (status) => {
  switch (status) {
    case 'completed':
      return 'text-green-800'
    case 'active':
      return 'text-primary'
    case 'pending':
      return 'text-yellow-800'
    case 'expired':
      return 'text-red-800'
    default:
      return 'text-gray-700'
  }
}

// 获取节点日期样式类
const getNodeDateClass = (status) => {
  switch (status) {
    case 'completed':
      return 'text-green-600'
    case 'active':
      return 'text-primary'
    case 'pending':
      return 'text-yellow-600'
    case 'expired':
      return 'text-red-600'
    default:
      return 'text-gray-500'
  }
}

// 获取状态点样式类
const getStatusDotClass = (status) => {
  switch (status) {
    case 'completed':
      return 'bg-green-500'
    case 'active':
      return 'bg-primary'
    case 'pending':
      return 'bg-yellow-500'
    case 'expired':
      return 'bg-red-500'
    default:
      return 'bg-gray-400'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'completed':
      return '已完成'
    case 'active':
      return '进行中'
    case 'pending':
      return '待开始'
    case 'expired':
      return '已截止'
    default:
      return '未知'
  }
}
</script>

<style scoped>
.application-timeline {
  @apply w-full;
}

.semester-title {
  @apply border-b border-gray-100 pb-3;
}

.semester-title h3 {
  @apply text-primary;
}

.compact-timeline {
  @apply overflow-x-auto;
}

.full-timeline {
  @apply relative;
}

/* 弹窗模式下的紧凑样式 */
.compact-dialog .full-timeline {
  @apply text-sm;
}

.compact-dialog .semester-title {
  @apply border-b border-purple-100 pb-2;
}

.compact-dialog .semester-title h3 {
  @apply text-purple-600;
}

/* 自定义滚动条样式 */
.compact-timeline::-webkit-scrollbar {
  height: 4px;
}

.compact-timeline::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded;
}

.compact-timeline::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded;
}

.compact-timeline::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .full-timeline .space-y-4 {
    @apply space-y-3;
  }
  
  .full-timeline .bg-white {
    @apply p-2;
  }
}
</style>
