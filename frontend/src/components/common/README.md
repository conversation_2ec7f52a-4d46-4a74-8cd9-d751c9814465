# 结构化数据展示组件

本目录包含用于展示结构化数据的Vue组件，专门用于解析和美化显示申请时间、语言要求、课程设置等复杂数据格式。

## 组件列表

### 1. ApplicationTimeline.vue
**用途**: 解析和展示申请时间节点信息

**支持的数据格式**:
- `25年秋季入学: 开放申请(2024-10-24) | Round 1 截止(2024-11-18) | Round 2 截止(2025-01-13)`
- 带HTML标签的格式: `申请开放(2024-10-24)<br/>截止时间(2024-11-18)`
- 多种分隔符: `|`, `;`, 换行符

**Props**:
- `applicationTime` (String): 申请时间字符串
- `compact` (Boolean): 是否使用紧凑模式，默认false
- `customClass` (String): 自定义样式类

**特性**:
- 自动解析多种时间格式
- 根据当前时间判断节点状态（已完成、进行中、待开始、已截止）
- 支持紧凑模式和完整时间线模式
- 自动清理HTML标签
- 响应式设计

### 2. LanguageRequirements.vue
**用途**: 解析和展示语言要求信息

**支持的数据格式**:
```
雅思 | 总分要求: 6 | 小分要求: 听力: 5.5; 阅读: 5.5; 写作: 5.5; 口语: 5.5; <br/>
托福 | 总分要求: 80 | 小分要求: 听力: /; 阅读: /; 写作: /; 口语: /; <br/>
```

**Props**:
- `languageRequirements` (String): 语言要求字符串
- `compact` (Boolean): 是否使用紧凑模式，默认false
- `customClass` (String): 自定义样式类

**特性**:
- 支持雅思、托福、GRE、GMAT等多种考试类型
- 自动解析总分和小分要求
- 不同考试类型使用不同的颜色主题
- 支持紧凑徽章模式和完整卡片模式
- 自动处理缺失的小分信息（显示为"不限"）

### 3. CourseStructure.vue
**用途**: 解析和展示课程设置信息

**支持的数据格式**:
```
课程描述：<br/> 
课程由60个学分的课程组成，涵盖统计学研究方法、定量交易、生物统计学、大数据分析等...
<br/> 
课程列表：<br/> 
统计推断基础 | Fundamentals of Statistical Inference | 必修课程<br/>
高级统计建模 | Advanced Statistical Modelling | 必修课程<br/>
高级定量风险管理 | Advanced Quantitative Risk Management | 主题选修课程<br/>
```

**Props**:
- `courseStructure` (String): 课程设置字符串
- `compact` (Boolean): 是否使用紧凑模式，默认false
- `customClass` (String): 自定义样式类

**特性**:
- 自动分离课程描述和课程列表
- 解析课程中英文名称和类型（必修/选修/核心）
- 按课程类型自动分类
- 支持课程统计展示
- 可切换不同课程分类查看
- 支持展开/收起功能

## 使用方式

### 基本用法
```vue
<template>
  <!-- 申请时间展示 -->
  <ApplicationTimeline 
    :application-time="program.application_time" 
    compact 
  />
  
  <!-- 语言要求展示 -->
  <LanguageRequirements 
    :language-requirements="program.language_requirements" 
  />
  
  <!-- 课程设置展示 -->
  <CourseStructure 
    :course-structure="program.courses" 
    compact 
  />
</template>

<script setup>
import ApplicationTimeline from '@/components/common/ApplicationTimeline.vue'
import LanguageRequirements from '@/components/common/LanguageRequirements.vue'
import CourseStructure from '@/components/common/CourseStructure.vue'
</script>
```

### 在项目中的集成

1. **SchoolAssistant.vue**: 在选校匹配页面中使用紧凑模式展示申请时间
2. **ProgramDetailDialog.vue**: 在专业详情弹窗中使用完整模式展示所有结构化数据
3. **其他页面**: 根据需要选择合适的模式进行集成

## 设计原则

1. **数据容错性**: 所有组件都具备良好的错误处理能力，能够处理空数据、异常格式等情况
2. **响应式设计**: 组件在不同屏幕尺寸下都能正常显示
3. **主题一致性**: 使用项目统一的紫色主题色彩
4. **模式切换**: 支持紧凑模式和完整模式，适应不同的使用场景
5. **HTML清理**: 自动清理数据中的HTML标签，确保安全性和一致性

## 样式特性

- 使用Tailwind CSS进行样式设计
- 支持暗色模式（通过CSS变量）
- 平滑的动画过渡效果
- 一致的间距和字体大小
- 可访问性友好的颜色对比度

## 扩展性

组件设计时考虑了扩展性，可以轻松添加新的数据格式支持或新的展示模式。如需添加新功能，请遵循现有的代码结构和命名规范。
