<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="handleDialogUpdate"
    title="专业详情"
    width="1000px"
    :before-close="handleClose"
    class="program-detail-dialog"
    :show-close="false"
    top="5vh"
  >
    <!-- 自定义头部 -->
    <template #header="{ titleId, titleClass }">
      <div class="dialog-header-enhanced">
        <div class="header-content">
          <div class="header-icon">
            <span class="material-icons-outlined">school</span>
          </div>
          <div class="header-text">
            <span :id="titleId" :class="titleClass" class="header-title">专业详情</span>
            <span class="header-subtitle">详细了解专业信息与申请要求</span>
          </div>
        </div>
        <button @click="handleClose" class="close-button-enhanced">
          <span class="material-icons-outlined">close</span>
        </button>
      </div>
    </template>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="loading-content">
        <div class="loading-spinner">
          <div class="spinner-ring"></div>
          <div class="spinner-core"></div>
        </div>
        <h3 class="loading-title">正在加载专业详情</h3>
        <p class="loading-subtitle">请稍候，正在获取最新数据...</p>
      </div>
      <el-skeleton animated class="skeleton-enhanced">
        <template #template>
          <div class="skeleton-header">
            <el-skeleton-item variant="h1" style="width: 60%; height: 32px; margin-bottom: 8px;" />
            <el-skeleton-item variant="h3" style="width: 40%; height: 20px; margin-bottom: 16px;" />
          </div>
          <div class="skeleton-cards">
            <div class="skeleton-card" v-for="i in 3" :key="i">
              <el-skeleton-item variant="h3" style="width: 30%; height: 20px; margin-bottom: 12px;" />
              <el-skeleton-item variant="text" style="width: 100%; margin-bottom: 8px;" />
              <el-skeleton-item variant="text" style="width: 80%; margin-bottom: 8px;" />
              <el-skeleton-item variant="text" style="width: 60%;" />
            </div>
          </div>
        </template>
      </el-skeleton>
    </div>

    <!-- 主要内容 -->
    <div v-else-if="program" class="program-content">
      <!-- 核心信息卡片 -->
      <div class="hero-card">
        <div class="hero-background">
          <div class="background-pattern"></div>
        </div>
        <div class="hero-content">
          <!-- 学校信息 -->
          <div class="school-info">
            <div class="school-header">
              <h1 class="school-name">{{ program.school_name_cn }}</h1>
              <p v-if="program.school_name_en" class="school-name-en">{{ program.school_name_en }}</p>
            </div>
            <div class="school-badges">
              <div class="badge-row">
                <span class="location-badge">
                  <span class="material-icons-outlined">location_on</span>
                  {{ program.school_region }}
                </span>
                <span v-if="program.school_qs_rank" class="rank-badge">
                  <span class="material-icons-outlined">star</span>
                  QS排名 {{ program.school_qs_rank }}
                </span>
                <span class="degree-badge">
                  <span class="material-icons-outlined">school</span>
                  {{ program.degree }}
                </span>
              </div>
            </div>
          </div>

          <!-- 专业信息 -->
          <div class="program-info">
            <div class="program-header">
              <h2 class="program-name">{{ program.program_name_cn }}</h2>
              <p v-if="program.program_name_en" class="program-name-en">{{ program.program_name_en }}</p>
            </div>
            <div class="program-meta">
              <span v-if="program.program_direction" class="meta-tag direction">
                <span class="material-icons-outlined">trending_up</span>
                {{ program.program_direction }}
              </span>
              <span v-if="program.program_category" class="meta-tag category">
                <span class="material-icons-outlined">category</span>
                {{ program.program_category }}
              </span>
              <span v-if="program.faculty" class="meta-tag faculty">
                <span class="material-icons-outlined">domain</span>
                {{ program.faculty }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 申请时间线 - 独立横向排布 -->
      <div v-if="program.application_time" class="timeline-card-container">
        <div class="info-card timeline timeline-compact">
          <div class="card-header">
            <div class="header-icon">
              <span class="material-icons-outlined">event</span>
            </div>
            <h3 class="card-title">申请时间</h3>
          </div>
          <div class="card-content timeline-content">
            <ApplicationTimeline :application-time="program.application_time" :compact="false" in-dialog />
          </div>
        </div>
      </div>

      <!-- 信息网格 -->
      <div class="info-grid">
        <!-- 基本信息 -->
        <div class="info-card essential">
          <div class="card-header">
            <div class="header-icon">
              <span class="material-icons-outlined">info</span>
            </div>
            <h3 class="card-title">基本信息</h3>
          </div>
          <div class="card-content">
            <div class="info-items">
              <div v-if="program.program_duration" class="info-item">
                <div class="item-icon">
                  <span class="material-icons-outlined">schedule</span>
                </div>
                <div class="item-content">
                  <div class="item-label">学制</div>
                  <div class="item-value">{{ program.program_duration }}</div>
                </div>
              </div>
              
              <div v-if="program.program_tuition" class="info-item">
                <div class="item-icon">
                  <span class="material-icons-outlined">payments</span>
                </div>
                <div class="item-content">
                  <div class="item-label">学费</div>
                  <div class="item-value highlight">{{ program.program_tuition }}</div>
                </div>
              </div>

              <div v-if="program.other_cost" class="info-item">
                <div class="item-icon">
                  <span class="material-icons-outlined">receipt_long</span>
                </div>
                <div class="item-content">
                  <div class="item-label">其他费用</div>
                  <div class="item-value">{{ program.other_cost }}</div>
                </div>
              </div>

              <div v-if="program.degree_evaluation" class="info-item">
                <div class="item-icon">
                  <span class="material-icons-outlined">verified</span>
                </div>
                <div class="item-content">
                  <div class="item-label">学位认证</div>
                  <div class="item-value">{{ program.degree_evaluation }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 申请要求 -->
        <div class="info-card requirements">
          <div class="card-header">
            <div class="header-icon">
              <span class="material-icons-outlined">assignment</span>
            </div>
            <h3 class="card-title">申请要求</h3>
          </div>
          <div class="card-content">
            <div class="requirements-content">
              <div v-if="program.gpa_requirements" class="requirement-item">
                <div class="requirement-header">
                  <span class="material-icons-outlined">grade</span>
                  <span class="requirement-title">学术要求</span>
                </div>
                <div class="requirement-value">{{ program.gpa_requirements }}</div>
              </div>
              
              <div v-if="program.language_requirements" class="requirement-item">
                <div class="requirement-header">
                  <span class="material-icons-outlined">language</span>
                  <span class="requirement-title">语言要求</span>
                </div>
                <div class="requirement-value">
                  <LanguageRequirements :language-requirements="program.language_requirements" compact in-dialog />
                </div>
              </div>
              
              <div v-if="program.application_requirements" class="requirement-item full-width">
                <div class="requirement-header">
                  <span class="material-icons-outlined">description</span>
                  <span class="requirement-title">详细申请要求</span>
                </div>
                <div class="requirement-description">{{ program.application_requirements }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 详细信息卡片 -->
      <div class="details-section">
        <!-- 专业目标 -->
        <div v-if="program.program_objectives" class="detail-card">
          <div class="card-header">
            <div class="header-icon">
              <span class="material-icons-outlined">flag</span>
            </div>
            <h3 class="card-title">专业目标</h3>
          </div>
          <div class="card-content">
            <div class="content-text rich-text">{{ program.program_objectives }}</div>
          </div>
        </div>

        <!-- 课程设置 -->
        <div v-if="program.courses" class="detail-card course-card">
          <div class="card-header">
            <div class="header-icon">
              <span class="material-icons-outlined">menu_book</span>
            </div>
            <h3 class="card-title">课程设置</h3>
          </div>
          <div class="card-content">
            <CourseStructure :course-structure="program.courses" in-dialog />
          </div>
        </div>

        
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-state">
      <div class="error-content">
        <div class="error-icon">
          <span class="material-icons-outlined">error_outline</span>
        </div>
        <h3 class="error-title">加载失败</h3>
        <p class="error-message">无法获取专业详情信息，请检查网络连接后重试</p>
        <el-button type="primary" @click="loadProgramDetail" class="retry-button">
          <span class="material-icons-outlined">refresh</span>
          重新加载
        </el-button>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <template #footer>
      <div class="footer-enhanced">
        <div class="footer-info">
          <span v-if="program?.program_website" class="website-hint">
            <span class="material-icons-outlined">info</span>
            点击查看官网了解更多详情
          </span>
        </div>
        <div class="footer-actions">
          <el-button 
            v-if="program?.program_website" 
            @click="handleViewWebsite" 
            class="website-button"
            :icon="'language'"
          >
            <span class="material-icons-outlined">language</span>
            查看官网
          </el-button>
          <el-button 
            type="primary" 
            @click="handleCollect" 
            class="collect-button"
          >
            <span class="material-icons-outlined">bookmark_add</span>
            收藏专业
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getProgramDetail } from '@/api/programs'
import ApplicationTimeline from '@/components/common/ApplicationTimeline.vue'
import LanguageRequirements from '@/components/common/LanguageRequirements.vue'
import CourseStructure from '@/components/common/CourseStructure.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  programId: {
    type: [String, Number],
    default: null
  }
})

const emit = defineEmits(['update:visible', 'collect'])

const loading = ref(false)
const program = ref(null)

// 处理弹窗显示/隐藏更新
const handleDialogUpdate = (value) => {
  emit('update:visible', value)
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.programId) {
    loadProgramDetail()
  } else if (!newVal) {
    // 弹窗关闭时延迟清空数据，避免短暂显示错误状态
    setTimeout(() => {
      program.value = null
    }, 300)
  }
})

// 监听程序ID变化
watch(() => props.programId, (newVal) => {
  if (newVal && props.visible) {
    loadProgramDetail()
  }
})

// 加载专业详情
const loadProgramDetail = async () => {
  if (!props.programId) return
  
  loading.value = true
  try {
    program.value = await getProgramDetail(props.programId)
  } catch (error) {
    console.error('加载专业详情失败:', error)
    ElMessage.error('加载专业详情失败')
    program.value = null
  } finally {
    loading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
  // 延迟清空数据，避免短暂显示加载失败画面
  setTimeout(() => {
    program.value = null
  }, 300)
}

// 收藏专业
const handleCollect = () => {
  if (program.value) {
    emit('collect', program.value)
  }
}

// 查看官网
const handleViewWebsite = () => {
  if (program.value?.program_website) {
    let url = program.value.program_website
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url
    }
    window.open(url, '_blank')
  }
}
</script>

<style scoped>
/* === 全局变量覆盖 === */
:root {
  --el-color-primary: #4F46E5 !important;
  --el-color-primary-light-3: #6366F1 !important;
  --el-color-primary-light-5: #818CF8 !important;
  --el-color-primary-light-7: #C7D2FE !important;
  --el-color-primary-light-9: #EEF2FF !important;
  --el-color-primary-dark-2: #4338CA !important;
}

/* === 弹窗系统 === */
:deep(.program-detail-dialog) {
  box-shadow: none !important;
  border: 1px solid #E4E7ED;
}

:deep(.program-detail-dialog .el-dialog__header) {
  padding: 12px 20px;
  border-bottom: 1px solid #F1F5F9;
  font-size: 14px;
}

:deep(.program-detail-dialog .el-dialog__body) {
  padding: 16px;
  max-height: 80vh;
  overflow-y: auto;
}

:deep(.program-detail-dialog .el-dialog__footer) {
  padding: 12px 20px;
  border-top: 1px solid #F1F5F9;
}

/* === 加载状态 === */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  text-align: center;
}

.loading-content {
  margin-bottom: 1.5rem;
}

.loading-spinner {
  position: relative;
  width: 40px;
  height: 40px;
  margin: 0 auto 10px;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 4px solid #F3F3F3;
  border-top: 4px solid #4F46E5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-core {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 4px solid #F3F3F3;
  border-top: 4px solid #4F46E5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-title {
  font-size: 1.125rem;
  color: #1F2937;
  margin-bottom: 0.5rem;
}

.loading-subtitle {
  font-size: 0.875rem;
  color: #6B7280;
}

.skeleton-enhanced {
  width: 100%;
  margin-top: 1.5rem;
}

.skeleton-header {
  margin-bottom: 1.5rem;
}

.skeleton-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.skeleton-card {
  padding: 1rem;
  background: #F9FAFB;
  border-radius: 0.375rem;
  border: 1px solid #E5E7EB;
}

/* === 对话框头部 === */
.dialog-header-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0;
}

.header-content {
  display: flex;
  align-items: center;
}

.header-icon {
  margin-right: 0.75rem;
  color: #4F46E5;
  font-size: 1.5rem;
}

.header-text {
  display: flex;
  flex-direction: column;
}

.header-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1F2937;
  margin-bottom: 0.25rem;
}

.header-subtitle {
  font-size: 0.75rem;
  color: #6B7280;
}

.close-button-enhanced {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 50%;
  background: #4F46E5;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(79, 70, 229, 0.2);
  flex-shrink: 0;
}

.close-button-enhanced:hover {
  background: #4338CA;
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(79, 70, 229, 0.3);
}

.close-button-enhanced:active {
  background: #3730A3;
  transform: scale(0.95);
}

.close-button-enhanced .material-icons-outlined {
  font-size: 16px;
  color: white;
}

/* === 主要内容布局 === */
.program-content {
  padding: 0;
}

/* === 核心信息卡片 === */
.hero-card {
  position: relative;
  background: linear-gradient(135deg, #4F46E5 0%, #6366F1 50%, #8B5CF6 100%);
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 1.5rem;
  overflow: hidden;
  color: white;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
}

.background-pattern {
  position: absolute;
  width: 200%;
  height: 200%;
  background-image: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
                    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.hero-content {
  position: relative;
  z-index: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: start;
}

/* === 学校信息 === */
.school-info {
  padding-right: 1rem;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.school-header {
  margin-bottom: 1rem;
}

.school-name {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.school-name-en {
  font-size: 0.875rem;
  opacity: 0.8;
  font-style: italic;
  margin-bottom: 0;
}

.school-badges {
  margin-top: 1rem;
}

.badge-row {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.location-badge, .rank-badge, .degree-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  padding: 0.375rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.rank-badge {
  background: rgba(251, 191, 36, 0.2);
  border-color: rgba(251, 191, 36, 0.3);
}

/* === 专业信息 === */
.program-info {
  padding-left: 1rem;
}

.program-header {
  margin-bottom: 1rem;
}

.program-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.program-name-en {
  font-size: 0.875rem;
  opacity: 0.8;
  font-style: italic;
  margin-bottom: 0;
}

.program-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.375rem;
  margin-top: 1rem;
}

.meta-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  padding: 0.25rem 0.5rem;
  border-radius: 0.75rem;
  font-size: 0.7rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.meta-tag.category {
  background: rgba(14, 165, 233, 0.2);
  border-color: rgba(14, 165, 233, 0.3);
}

.meta-tag.faculty {
  background: rgba(34, 197, 94, 0.2);
  border-color: rgba(34, 197, 94, 0.3);
}

/* === 申请时间卡片容器 === */
.timeline-card-container {
  margin-bottom: 1.5rem;
}

.timeline-card-container .info-card.timeline {
  border-left: 4px solid #8B5CF6;
}

.timeline-card-container .header-icon {
  background: #8B5CF6;
}

/* === 时间线紧凑样式 === */
.timeline-compact {
  max-width: 100%;
}

.timeline-content {
  padding: 0.75rem !important;
}

.timeline-compact .card-content {
  overflow-x: auto;
}

/* === 信息网格布局 === */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

/* === 信息卡片 === */
.info-card {
  background: #FFFFFF;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #E5E7EB;
  overflow: hidden;
  transition: all 0.3s ease;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
}

.info-card.essential {
  border-left: 4px solid #8B5CF6;
}

.info-card.requirements {
  border-left: 4px solid #8B5CF6;
}

/* === 卡片头部 === */
.card-header {
  background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
  padding: 1rem 1.25rem;
  border-bottom: 1px solid #E5E7EB;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-icon {
  width: 2rem;
  height: 2rem;
  background: #8B5CF6;
  color: white;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.info-card.requirements .header-icon {
  background: #8B5CF6;
}

.card-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1F2937;
  margin: 0;
}

/* === 卡片内容 === */
.card-content {
  padding: 1rem;
}

/* === 基本信息项目 === */
.info-items {
  display: grid;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #F9FAFB;
  border-radius: 0.5rem;
  border: 1px solid #E5E7EB;
  transition: all 0.2s ease;
}

.info-item:hover {
  background: #F3F4F6;
  border-color: #8B5CF6;
}

.item-icon {
  width: 2rem;
  height: 2rem;
  background: linear-gradient(135deg, #8B5CF6, #A855F7);
  color: white;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.item-content {
  flex: 1;
}

.item-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: #6B7280;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  margin-bottom: 0.25rem;
}

.item-value {
  font-size: 0.875rem;
  color: #1F2937;
  font-weight: 600;
}

.item-value.highlight {
  color: #8B5CF6;
  font-weight: 700;
}



/* === 申请要求内容 === */
.requirements-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.requirement-item {
  padding: 1rem;
  background: #F9FAFB;
  border-radius: 0.5rem;
  border: 1px solid #E5E7EB;
  transition: all 0.2s ease;
}

.requirement-item:hover {
  background: #F3F4F6;
  border-color: #8B5CF6;
}

.requirement-item.full-width {
  background: linear-gradient(135deg, #F0F9FF 0%, #EEF2FF 100%);
  border-color: #C7D2FE;
}

.requirement-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 600;
}

.requirement-header .material-icons-outlined {
  color: #8B5CF6;
  font-size: 1rem;
}

.requirement-title {
  color: #1F2937;
}

.requirement-value {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.requirement-description {
  font-size: 0.875rem;
  color: #374151;
  line-height: 1.5;
  padding: 0.75rem;
  background: white;
  border-radius: 0.375rem;
  border: 1px solid #E5E7EB;
}

/* === 详细信息区域 === */
.details-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-card {
  background: #FFFFFF;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #E5E7EB;
  overflow: hidden;
  transition: all 0.3s ease;
}

.detail-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.detail-card.course-card {
  border-left: 4px solid #8B5CF6;
}

.detail-card .card-header {
  background: linear-gradient(135deg, #FAFBFF 0%, #F4F6FF 100%);
}

.detail-card.course-card .header-icon {
  background: #8B5CF6;
}

/* === 内容文本 === */
.content-text {
  color: #374151;
  line-height: 1.6;
  font-size: 0.875rem;
}

.rich-text {
  font-size: 0.875rem;
  line-height: 1.7;
  padding: 0.75rem;
  background: #F9FAFB;
  border-radius: 0.5rem;
  border: 1px solid #E5E7EB;
}

/* === 图标系统 === */
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-size: 16px;
  line-height: 1;
  transition: color 0.2s ease;
}

.footer-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #E5E7EB;
}

.footer-info {
  font-size: 0.75rem;
  color: #6B7280;
}

.website-hint {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #8B5CF6;
  font-size: 0.75rem;
}

.footer-actions {
  display: flex;
  gap: 0.5rem;
}

.website-button,
.collect-button {
  background-color: #8B5CF6 !important;
  color: #FFFFFF !important;
  border: none !important;
  border-radius: 0.375rem !important;
  font-size: 0.8rem !important;
  font-weight: 500 !important;
  padding: 0.375rem 0.75rem !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.25rem !important;
}

.website-button:hover {
  background-color: #7C3AED !important;
}

.website-button:active {
  background-color: #6D28D9 !important;
}

.collect-button:hover {
  background-color: #7C3AED !important;
}

.collect-button:active {
  background-color: #6D28D9 !important;
}

/* === 错误状态 === */
.error-state {
  padding: 1rem 0;
}

.error-content {
  text-align: center;
  padding: 1.5rem;
}

.error-icon {
  color: #F56565;
  font-size: 3rem;
  margin-bottom: 0.75rem;
}

.error-title {
  font-size: 1.125rem;
  color: #1F2937;
  margin-bottom: 0.5rem;
}

.error-message {
  font-size: 0.875rem;
  color: #6B7280;
  margin-bottom: 1.5rem;
}

.retry-button {
  background-color: #8B5CF6 !important;
  color: #FFFFFF !important;
  border: none !important;
  border-radius: 0.375rem !important;
  font-size: 0.8rem !important;
  font-weight: 500 !important;
  padding: 0.375rem 0.75rem !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.25rem !important;
}

.retry-button:hover {
  background-color: #7C3AED !important;
}

.retry-button:active {
  background-color: #6D28D9 !important;
}

/* === 响应式设计 === */
@media (max-width: 1024px) {
  :deep(.program-detail-dialog) {
    width: 95vw !important;
    max-width: 900px !important;
  }
  
  .hero-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .school-info {
    padding-right: 0;
    border-right: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 1.5rem;
  }
  
  .program-info {
    padding-left: 0;
    padding-top: 1.5rem;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  :deep(.program-detail-dialog) {
    width: 100vw !important;
    height: 100vh !important;
    max-width: none !important;
    margin: 0 !important;
    top: 0 !important;
    border-radius: 0 !important;
  }
  
  :deep(.program-detail-dialog .el-dialog__body) {
    padding: 1rem;
    max-height: calc(100vh - 120px);
  }
  
  .hero-card {
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-radius: 0.75rem;
  }
  
  .timeline-card-container {
    margin-bottom: 1rem;
  }
  
  .school-name {
    font-size: 1.25rem;
  }
  
  .program-name {
    font-size: 1.125rem;
  }
  
  .badge-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.375rem;
  }
  
  .program-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .info-items {
    gap: 0.75rem;
  }
  
  .footer-enhanced {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }
  
  .footer-actions {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .website-button,
  .collect-button {
    width: 100% !important;
    justify-content: center !important;
  }
}

/* === 滚动条样式 === */
:deep(.program-detail-dialog .el-dialog__body)::-webkit-scrollbar {
  width: 6px;
}

:deep(.program-detail-dialog .el-dialog__body)::-webkit-scrollbar-track {
  background: #F8FAFC;
  border-radius: 3px;
}

:deep(.program-detail-dialog .el-dialog__body)::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #C7D2FE, #A5B4FC);
  border-radius: 3px;
}

:deep(.program-detail-dialog .el-dialog__body)::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #A5B4FC, #8B5CF6);
}

/* === 过渡动画 === */
.info-card,
.detail-card,
.hero-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.info-item,
.requirement-item {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* === 加载动画优化 === */
.spinner-ring {
  animation: spin 1s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
}

.loading-content {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 