<template>
  <div class="pro-card h-full flex flex-col overflow-hidden">
    <div class="pro-card-header">
      <div class="flex items-center space-x-2">
        <el-button 
          @click="handleSave" 
          type="primary" 
          :loading="isSaving"
          class="primary-btn transition-standard hover-scale"
        >
          保存
        </el-button>
        <el-dropdown @command="handleExport" class="transition-standard">
          <el-button class="secondary-btn flex items-center">
            <span class="material-icons-outlined text-base mr-1.5">save_alt</span>
            <span>导出</span>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="pdf">导出为PDF</el-dropdown-item>
              <el-dropdown-item command="docx">导出为Word</el-dropdown-item>
              <el-dropdown-item command="txt">导出为文本</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button @click="handleCopyAll" class="secondary-btn transition-standard">
          复制全部
        </el-button>
        <el-button @click="handleDetectAIRate" :loading="isDetecting" :disabled="isDetecting" class="secondary-btn transition-standard">
          <span class="material-icons-outlined text-base mr-1">policy</span>
          AI率检测
        </el-button>
        <div v-if="detectionResult" class="ml-2 text-sm font-semibold" 
             :class="{'text-green-600': detectionResult.aiRate < 40, 'text-orange-500': detectionResult.aiRate >= 40}">
          AI率: {{ detectionResult.aiRate }}%
        </div>
        <div class="flex-grow"></div>
      </div>
      <div class="flex items-center space-x-4 text-sm text-gray-500">
        <span class="text-gray-600">{{ wordCount }} 单词</span>
        <span class="text-gray-600">{{ characterCount }} 字符</span>
        <span v-if="lastSaved" class="text-gray-600">上次保存: <span class="font-medium text-gray-800">{{ lastSaved }}</span></span>
      </div>
    </div>

    <!-- 编辑器工具栏 - 贯穿整行 -->
    <div v-if="editor" class="editor-toolbar">
      <!-- 文本格式组 -->
      <div class="toolbar-group">
        <button
          @click="editor.chain().focus().toggleBold().run()"
          :class="{ 'is-active': editor.isActive('bold') }"
          class="toolbar-btn"
          title="粗体"
        >
          <span class="material-icons-outlined">format_bold</span>
        </button>
        <button
          @click="editor.chain().focus().toggleItalic().run()"
          :class="{ 'is-active': editor.isActive('italic') }"
          class="toolbar-btn"
          title="斜体"
        >
          <span class="material-icons-outlined">format_italic</span>
        </button>
        <button
          @click="editor.chain().focus().toggleUnderline().run()"
          :class="{ 'is-active': editor.isActive('underline') }"
          class="toolbar-btn"
          title="下划线"
        >
          <span class="material-icons-outlined">format_underlined</span>
        </button>
        <button
          @click="editor.chain().focus().toggleStrike().run()"
          :class="{ 'is-active': editor.isActive('strike') }"
          class="toolbar-btn"
          title="删除线"
        >
          <span class="material-icons-outlined">format_strikethrough</span>
        </button>
      </div>

      <!-- 标题组 -->
      <div class="toolbar-group">
        <button
          @click="editor.chain().focus().toggleHeading({ level: 1 }).run()"
          :class="{ 'is-active': editor.isActive('heading', { level: 1 }) }"
          class="toolbar-btn"
          title="标题1"
        >
          H1
        </button>
        <button
          @click="editor.chain().focus().toggleHeading({ level: 2 }).run()"
          :class="{ 'is-active': editor.isActive('heading', { level: 2 }) }"
          class="toolbar-btn"
          title="标题2"
        >
          H2
        </button>
        <button
          @click="editor.chain().focus().toggleHeading({ level: 3 }).run()"
          :class="{ 'is-active': editor.isActive('heading', { level: 3 }) }"
          class="toolbar-btn"
          title="标题3"
        >
          H3
        </button>
        <button
          @click="editor.chain().focus().setParagraph().run()"
          :class="{ 'is-active': editor.isActive('paragraph') }"
          class="toolbar-btn"
          title="正文"
        >
          P
        </button>
      </div>

      <!-- 对齐组 -->
      <div class="toolbar-group">
        <button
          @click="editor.chain().focus().setTextAlign('left').run()"
          :class="{ 'is-active': editor.isActive({ textAlign: 'left' }) }"
          class="toolbar-btn"
          title="左对齐"
        >
          <span class="material-icons-outlined">format_align_left</span>
        </button>
        <button
          @click="editor.chain().focus().setTextAlign('center').run()"
          :class="{ 'is-active': editor.isActive({ textAlign: 'center' }) }"
          class="toolbar-btn"
          title="居中对齐"
        >
          <span class="material-icons-outlined">format_align_center</span>
        </button>
        <button
          @click="editor.chain().focus().setTextAlign('right').run()"
          :class="{ 'is-active': editor.isActive({ textAlign: 'right' }) }"
          class="toolbar-btn"
          title="右对齐"
        >
          <span class="material-icons-outlined">format_align_right</span>
        </button>
        <button
          @click="editor.chain().focus().setTextAlign('justify').run()"
          :class="{ 'is-active': editor.isActive({ textAlign: 'justify' }) }"
          class="toolbar-btn"
          title="两端对齐"
        >
          <span class="material-icons-outlined">format_align_justify</span>
        </button>
      </div>

      <!-- 列表组 -->
      <div class="toolbar-group">
        <button
          @click="editor.chain().focus().toggleBulletList().run()"
          :class="{ 'is-active': editor.isActive('bulletList') }"
          class="toolbar-btn"
          title="无序列表"
        >
          <span class="material-icons-outlined">format_list_bulleted</span>
        </button>
        <button
          @click="editor.chain().focus().toggleOrderedList().run()"
          :class="{ 'is-active': editor.isActive('orderedList') }"
          class="toolbar-btn"
          title="有序列表"
        >
          <span class="material-icons-outlined">format_list_numbered</span>
        </button>
        <button
          @click="editor.chain().focus().toggleBlockquote().run()"
          :class="{ 'is-active': editor.isActive('blockquote') }"
          class="toolbar-btn"
          title="引用"
        >
          <span class="material-icons-outlined">format_quote</span>
        </button>
      </div>

      <!-- 操作组 -->
      <div class="toolbar-group">
        <button
          @click="editor.chain().focus().undo().run()"
          :disabled="!editor.can().chain().focus().undo().run()"
          class="toolbar-btn"
          title="撤销"
        >
          <span class="material-icons-outlined">undo</span>
        </button>
        <button
          @click="editor.chain().focus().redo().run()"
          :disabled="!editor.can().chain().focus().redo().run()"
          class="toolbar-btn"
          title="重做"
        >
          <span class="material-icons-outlined">redo</span>
        </button>
      </div>
    </div>

    <!-- 主内容区：编辑器工具栏下方的并排布局 -->
    <div class="flex-grow flex flex-row h-full min-h-0">
      <!-- 左侧：编辑器内容 -->
      <div class="transition-all duration-300 ease-in-out flex flex-col min-h-0" :class="[detectionResult ? 'w-1/2 pr-2' : 'w-full']">
        <div class="editor-content flex-1 min-h-0">
          <EditorContent :editor="editor" class="prose max-w-none h-full" />
        </div>
      </div>

      <!-- 右侧：AI检测结果看板 -->
      <div v-if="detectionResult" class="w-1/2 flex flex-col pl-2 min-h-0">
        <div class="detection-content flex-1 min-h-0">
          <!-- 模拟A4页面的白板 -->
          <div class="simulated-a4-page">
            <!-- 关闭按钮（右上角） -->
            <div class="flex justify-end mb-4">
              <button @click="detectionResult = null" class="text-gray-400 hover:text-gray-600 text-xl">&times;</button>
            </div>

            <!-- 高亮文本内容 -->
            <div class="highlighted-content" v-html="detectionResult.highlighted_html"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Typography from '@tiptap/extension-typography'
import Placeholder from '@tiptap/extension-placeholder'
import TextAlign from '@tiptap/extension-text-align'
import Underline from '@tiptap/extension-underline'
import { marked } from 'marked'
import TurndownService from 'turndown'
import { detectAIContent } from '@/api/aidetection'; // 引入AI检测API

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  documentType: {
    type: String,
    required: true // 'cv', 'ps', 'recommendation'
  },
  placeholder: {
    type: String,
    default: '开始写作...'
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'save', 'export'])

// 响应式数据
const editor = ref(null)
const isSaving = ref(false)
const lastSaved = ref('')
const originalMarkdown = ref('') // 存储原始Markdown内容
const isDetecting = ref(false);
const detectionResult = ref(null);

// 计算字数
const wordCount = computed(() => {
  if (!editor.value) return 0
  const text = editor.value.getText()
  return text.trim().split(/\s+/).filter(word => word.length > 0).length
})

// 计算字符数
const characterCount = computed(() => {
  if (!editor.value) return 0
  const text = editor.value.getText()
  return text.length
})

// Markdown 转 HTML 的配置
const markedOptions = {
  breaks: true,        // 将换行符转换为 <br>
  gfm: true,          // 启用 GitHub Flavored Markdown
  pedantic: false,    // 不使用严格的 Markdown 解析
  sanitize: false,    // 不清理 HTML
  smartLists: true,   // 智能列表处理
  smartypants: false  // 不转换引号和破折号
}

// HTML 转 Markdown 的配置
const turndownService = new TurndownService({
  headingStyle: 'atx',
  hr: '---',
  bulletListMarker: '-',
  codeBlockStyle: 'fenced',
  fence: '```',
  emDelimiter: '*',
  strongDelimiter: '**',
  linkStyle: 'inlined',
  linkReferenceStyle: 'full'
})

// 将 Markdown 转换为 HTML
const convertMarkdownToHtml = (markdown) => {
  if (!markdown) return ''
  try {
    console.log('🔍 原始Markdown内容:', markdown)
    console.log('🔍 Markdown长度:', markdown.length)
    console.log('🔍 包含换行符数量:', (markdown.match(/\n/g) || []).length)

    const htmlResult = marked(markdown, markedOptions)
    console.log('✅ 转换后HTML:', htmlResult)
    console.log('✅ HTML长度:', htmlResult.length)

    return htmlResult
  } catch (error) {
    console.error('Markdown 转换失败:', error)
    return markdown
  }
}

// 将 HTML 转换为 Markdown
const convertHtmlToMarkdown = (html) => {
  if (!html) return ''
  try {
    console.log('🔄 开始HTML转Markdown:', html.substring(0, 100))
    const markdownResult = turndownService.turndown(html)
    console.log('✅ HTML转Markdown完成:', markdownResult.substring(0, 100))
    return markdownResult
  } catch (error) {
    console.error('HTML转Markdown失败:', error)
    return html
  }
}

// 初始化编辑器
const initEditor = () => {
  editor.value = new Editor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3, 4, 5, 6],
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Underline,
      Typography,
      Placeholder.configure({
        placeholder: props.placeholder,
      }),
    ],
    content: '',
    editable: true,
    autofocus: false,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      console.log('编辑器内容变化，HTML长度:', html.length)

      // 将HTML转换为Markdown并更新originalMarkdown
      const markdown = convertHtmlToMarkdown(html)
      originalMarkdown.value = markdown

      // 修复：不要发出HTML内容，避免循环更新
      // 只更新内部的Markdown状态，不触发v-model更新
      console.log('✅ 已更新内部Markdown状态，长度:', markdown.length)
    },
  })
  
  console.log('Tiptap编辑器初始化完成')
}

// 保存功能
const handleSave = async () => {
  if (!editor.value) return

  isSaving.value = true
  try {
    const htmlContent = editor.value.getHTML()
    // 使用实时更新的Markdown内容
    const markdownContent = originalMarkdown.value || convertHtmlToMarkdown(htmlContent)

    emit('save', {
      content: htmlContent, // HTML内容
      markdownContent: markdownContent, // 实时转换的Markdown内容
      type: props.documentType,
      wordCount: wordCount.value,
      characterCount: characterCount.value
    })
    
    // 模拟保存API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    lastSaved.value = new Date().toLocaleTimeString()
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
    console.error('Save error:', error)
  } finally {
    isSaving.value = false
  }
}

// 导出功能
const handleExport = async (format) => {
  if (!editor.value) return
  
  try {
    const htmlContent = editor.value.getHTML()
    const textContent = editor.value.getText()
    
    emit('export', {
      content: htmlContent,
      textContent: textContent,
      format,
      type: props.documentType,
      wordCount: wordCount.value,
      characterCount: characterCount.value
    })
    
    ElMessage.success(`正在生成${format.toUpperCase()}文件...`)
  } catch (error) {
    ElMessage.error('导出失败')
    console.error('Export error:', error)
  }
}

// 复制全部内容
const handleCopyAll = async () => {
  if (!editor.value) return
  
  try {
    const content = editor.value.getText()
    
    if (!content.trim()) {
      ElMessage.warning('没有内容可复制')
      return
    }
    
    // 使用现代浏览器的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(content)
      ElMessage.success('内容已复制到剪贴板')
    } else {
      // 备用方案：使用传统方法
      const textArea = document.createElement('textarea')
      textArea.value = content
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      
      try {
        const successful = document.execCommand('copy')
        if (successful) {
          ElMessage.success('内容已复制到剪贴板')
        } else {
          ElMessage.error('复制失败，请手动选择文本复制')
        }
      } catch (err) {
        console.error('复制失败:', err)
        ElMessage.error('复制失败，请手动选择文本复制')
      } finally {
        document.body.removeChild(textArea)
      }
    }
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动选择文本复制')
  }
}

// 监听内容变化
watch(() => props.modelValue, (newValue) => {
  if (!editor.value || !newValue) return
  
  console.log('🔄 TiptapEditor watch触发')
  console.log('📏 新值长度:', newValue.length)
  console.log('📝 新值前100字符:', newValue.substring(0, 100))
  
  const currentHtml = editor.value.getHTML()
  
  // 检测是否是 Markdown 内容
  const isMarkdown = newValue.includes('#') || newValue.includes('**') || newValue.includes('*') || newValue.includes('- ') || newValue.includes('1. ') || newValue.includes('\n\n')

  if (isMarkdown) {
    console.log('🔄 检测到Markdown内容，开始转换')
    // 保存原始Markdown内容
    originalMarkdown.value = newValue
    const htmlContent = convertMarkdownToHtml(newValue)
    console.log('✅ Markdown转换完成，HTML长度:', htmlContent.length)

    // 避免循环更新
    if (htmlContent !== currentHtml) {
      editor.value.commands.setContent(htmlContent, false)
    }
  } else {
    // 如果是HTML内容，直接设置
    if (newValue !== currentHtml) {
      editor.value.commands.setContent(newValue, false)
    }
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  console.log('📱 TiptapEditor mounted')
  initEditor()
})

onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
  }
})

const getAiRateColor = (rate) => {
  if (rate >= 70) return 'text-red-500';
  if (rate >= 40) return 'text-yellow-500';
  return 'text-green-500';
};

const getArcColor = (rate) => {
  if (rate >= 70) return '#ef4444';
  if (rate >= 40) return '#f59e0b';
  return '#10b981';
};

const getArcPath = (rate) => {
  const angle = (rate / 100) * 180;
  const radian = (angle * Math.PI) / 180;
  const centerX = 125;
  const centerY = 140;
  const radius = 100;
  const x = centerX + radius * Math.cos(Math.PI - radian);
  const y = centerY - radius * Math.sin(Math.PI - radian);
  const largeArc = angle > 90 ? 1 : 0;
  return `M 25 140 A 100 100 0 ${largeArc} 1 ${x} ${y}`;
};

const handleDetectAIRate = async () => {
  const content = editor.value.getText();
  if (!content.trim()) {
    ElMessage.warning('内容为空，无法进行AI检测');
    return;
  }
  
  isDetecting.value = true;
  detectionResult.value = null; // 重置结果
  try {
    const response = await detectAIContent(content);
    if (response && response.result) {
      detectionResult.value = {
        aiRate: Math.round(response.result.fake_percentage),
        highlighted_html: response.result.highlighted_html,
      };
      ElMessage.success('AI率检测完成！');
    } else {
      throw new Error('API响应格式异常');
    }
  } catch (error) {
    console.error('AI检测失败:', error);
    ElMessage.error('AI检测失败，请稍后重试');
  } finally {
    isDetecting.value = false;
  }
};
</script>

<style scoped>
/* 按钮样式 - 确保工具栏按钮正确显示 */
.primary-btn {
  background-color: #4F46E5 !important;
  color: #FFFFFF !important;
  border: none !important;
  border-radius: 0.5rem !important;
  padding: 0.5rem 1rem !important;
  font-weight: 500 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.primary-btn:hover {
  background-color: #4338CA !important;
}

.secondary-btn {
  background-color: transparent !important;
  color: #6B7280 !important;
  border: 1px solid #E5E7EB !important;
  border-radius: 0.5rem !important;
  padding: 0.5rem 1rem !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.secondary-btn:hover {
  color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  background-color: rgba(79, 70, 229, 0.05) !important;
}

/* 卡片样式 - 确保容器正确显示 */
.pro-card {
  background-color: #FFFFFF;
  border: none;
  border-radius: 0;
  box-shadow: none;
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
  overflow: hidden !important;
}

.pro-card-header {
  height: 3.5rem;
  padding: 0 1rem;
  border-bottom: 1px solid #E5E7EB;
  display: flex !important;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  background-color: #FFFFFF;
  z-index: 10;
  position: relative;
}

.pro-card-body {
  padding: 0;
  flex: 1;
  min-height: 0;
}

/* 动画效果 */
.transition-standard {
  transition: all 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* 编辑器工具栏样式 */
.editor-toolbar {
  border-bottom: 1px solid #E5E7EB;
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
  flex-shrink: 0;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding-right: 0.5rem;
  border-right: 1px solid #E5E7EB;
}

.toolbar-group:last-child {
  border-right: none;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  background-color: transparent;
  color: #6B7280;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
}

.toolbar-btn:hover {
  background-color: #F3F4F6;
  color: #374151;
}

.toolbar-btn.is-active {
  background-color: #4F46E5;
  color: #FFFFFF;
  border-color: #4F46E5;
}

.toolbar-btn:disabled {
  background-color: transparent;
  color: #D1D5DB;
  cursor: not-allowed;
  opacity: 0.5;
}

.toolbar-btn:disabled:hover {
  background-color: transparent;
  color: #D1D5DB;
}

.toolbar-btn .material-icons-outlined {
  font-size: 1rem;
}

/* 正文按钮样式 */
.toolbar-btn:has-text("A") {
  font-size: 0.875rem;
  font-weight: 600;
}

/* 编辑器容器样式 */
.editor-wrapper {
  position: relative;
  height: 100%;
  overflow: hidden;
}

.editor-content {
  height: 100%;
  max-height: 100%;
  overflow-y: auto;
  background-color: #f3f4f6; /* Add a canvas background */
  padding: 2rem 1rem 6rem 1rem; /* 增加底部padding，确保内容可以完全滚动到 */
  /* 确保滚动条样式与全局一致 */
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
  /* 关键修复：确保flex项目可以缩小 */
  min-height: 0;
  flex-shrink: 1;
  /* 确保容器可以包含所有内容 */
  contain: layout style;
}

/* 右侧AI检测结果区域的滚动样式 */
.detection-content {
  height: 100%;
  max-height: 100%;
  overflow-y: auto;
  background-color: #f3f4f6;
  padding: 2rem 1rem 6rem 1rem; /* 增加底部padding，确保内容可以完全滚动到 */
  /* 确保滚动条样式与全局一致 */
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
  /* 关键修复：确保flex项目可以缩小 */
  min-height: 0;
  flex-shrink: 1;
  /* 确保容器可以包含所有内容 */
  contain: layout style;
}

/* Tiptap编辑器样式覆盖 */
:deep(.ProseMirror) {
  /* --- A4 Page Simulation --- */
  width: 210mm;
  margin: 0 auto 6rem auto; /* 增加底部留白，确保内容可以完全显示 */
  padding: 20mm;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12);
  border-radius: 3px;
  min-height: auto; /* 移除固定最小高度，让内容自然扩展 */
  /* --- End A4 Simulation --- */

  outline: none;
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  /* 确保文本可选择 */
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  cursor: text;
  
  /* 确保编辑器内容可以正常换行和滚动 */
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
}

/* 增强：确保列表项正确显示项目符号 */
:deep(.ProseMirror ul) {
  list-style-type: disc;
  padding-left: 1.75rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

:deep(.ProseMirror ol) {
  list-style-type: decimal;
  padding-left: 1.75rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

:deep(.ProseMirror li) {
  display: list-item;
  margin-top: 0.25rem;
}

:deep(.ProseMirror p.is-editor-empty:first-child::before) {
  content: attr(data-placeholder);
  float: left;
  color: #9CA3AF;
  pointer-events: none;
  height: 0;
}

/* 标题样式 */
:deep(.ProseMirror h1) {
  font-size: 1.875rem;
  font-weight: 700;
  margin: 2rem 0 1rem 0;
  line-height: 1.2;
  color: #1F2937;
  border-bottom: 2px solid #E5E7EB;
  padding-bottom: 0.5rem;
}

:deep(.ProseMirror h2) {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.5rem 0 0.75rem 0;
  line-height: 1.3;
  color: #374151;
}

:deep(.ProseMirror h3) {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1.25rem 0 0.5rem 0;
  line-height: 1.4;
  color: #4B5563;
}

/* 段落样式 */
:deep(.ProseMirror p) {
  margin: 0.875rem 0;
  line-height: 1.7;
  color: #374151;
  text-align: justify;
}

/* 引用样式 */
:deep(.ProseMirror blockquote) {
  border-left: 4px solid #4F46E5;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6B7280;
}

/* 代码样式 */
:deep(.ProseMirror code) {
  background-color: #F3F4F6;
  color: #EF4444;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
}

:deep(.ProseMirror pre) {
  background-color: #1F2937;
  color: #F9FAFB;
  padding: 1rem;
  border-radius: 0.5rem;
  margin: 1rem 0;
  overflow-x: auto;
}

:deep(.ProseMirror pre code) {
  background-color: transparent;
  color: inherit;
  padding: 0;
}

/* 选择高亮 */
:deep(.ProseMirror)::selection {
  background-color: rgba(79, 70, 229, 0.2);
}

:deep(.ProseMirror) *::selection {
  background-color: rgba(79, 70, 229, 0.2);
}

/* Element Plus覆盖 */
:deep(.el-input.is-focus .el-input__wrapper) {
  border-color: #4F46E5 !important;
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.8) !important;
}

/* Material Icons */
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  line-height: 1;
}

/* 自定义导出下拉框悬浮样式 */
:deep(.el-dropdown-menu__item:not(.is-disabled):hover) {
  background-color: #4F46E5 !important;
  color: #FFFFFF !important;
}


/* 确保EditorContent组件正确显示 */
:deep(.prose) {
  max-width: none !important;
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.prose:focus) {
  outline: none;
}

/* 确保EditorContent的内部容器正确处理高度 */
:deep(.ProseMirror-wrapper) {
  height: 100%;
  overflow-y: auto;
}

/* 确保EditorContent组件本身有正确的高度 */
:deep(.ProseMirror-focused) {
  outline: none;
}

/* 处理EditorContent组件的滚动容器 */
:deep(.editor-content .prose) {
  height: 100% !important;
  overflow-y: auto;
  display: block;
}



/* WebKit浏览器滚动条样式 - 与全局样式保持一致 */
.editor-content::-webkit-scrollbar,
.detection-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.editor-content::-webkit-scrollbar-track,
.detection-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.editor-content::-webkit-scrollbar-thumb,
.detection-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.editor-content::-webkit-scrollbar-thumb:hover,
.detection-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 右侧看板样式 - 模拟左侧编辑器的A4页面效果 */
.simulated-a4-page {
  /* 与左侧编辑器完全相同的A4页面样式 */
  width: 210mm;
  margin: 0 auto 6rem auto; /* 增加底部留白，确保内容可以完全显示 */
  padding: 20mm;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12);
  border-radius: 3px;
  min-height: auto; /* 移除固定最小高度，让内容自然扩展 */

  /* 与ProseMirror编辑器完全相同的字体设置 */
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  /* 确保文本可选择，与编辑器一致 */
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  cursor: text;
}

.highlighted-content {
  /* 与ProseMirror编辑器完全相同的样式 */
  font-size: 14px;
  line-height: 1.6; /* 与ProseMirror保持一致 */
  color: #374151;
  text-align: justify;
}

/* 确保高亮内容中的段落样式与ProseMirror编辑器完全一致 */
.highlighted-content :deep(p) {
  margin: 0.875rem 0;
  line-height: 1.7;
  color: #374151;
  text-align: justify;
}

/* 确保标题样式与ProseMirror编辑器完全一致 */
.highlighted-content :deep(h1) {
  font-size: 1.875rem;
  font-weight: 700;
  margin: 2rem 0 1rem 0;
  line-height: 1.2;
  color: #1F2937;
  border-bottom: 2px solid #E5E7EB;
  padding-bottom: 0.5rem;
}

.highlighted-content :deep(h2) {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.5rem 0 0.75rem 0;
  line-height: 1.3;
  color: #374151;
}

.highlighted-content :deep(h3) {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1.25rem 0 0.5rem 0;
  line-height: 1.4;
  color: #4B5563;
}

.highlighted-content :deep(h4) {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem 0;
  line-height: 1.4;
  color: #4B5563;
}

.highlighted-content :deep(h5) {
  font-size: 1rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem 0;
  line-height: 1.4;
  color: #4B5563;
}

.highlighted-content :deep(h6) {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem 0;
  line-height: 1.4;
  color: #4B5563;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* 确保列表样式与ProseMirror编辑器一致 */
.highlighted-content :deep(ul) {
  list-style-type: disc;
  padding-left: 1.75rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.highlighted-content :deep(ol) {
  list-style-type: decimal;
  padding-left: 1.75rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.highlighted-content :deep(li) {
  display: list-item;
  margin-top: 0.25rem;
}

/* 确保引用样式与ProseMirror编辑器一致 */
.highlighted-content :deep(blockquote) {
  border-left: 4px solid #4F46E5;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6B7280;
}

/* 确保代码样式与ProseMirror编辑器一致 */
.highlighted-content :deep(code) {
  background-color: #F3F4F6;
  color: #EF4444;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
}

.highlighted-content :deep(pre) {
  background-color: #1F2937;
  color: #F9FAFB;
  padding: 1rem;
  border-radius: 0.5rem;
  margin: 1rem 0;
  overflow-x: auto;
}

.highlighted-content :deep(pre code) {
  background-color: transparent;
  color: inherit;
  padding: 0;
}

/* 确保文本格式样式与ProseMirror编辑器一致 */
.highlighted-content :deep(strong) {
  font-weight: 700;
  color: #1F2937;
}

.highlighted-content :deep(b) {
  font-weight: 700;
  color: #1F2937;
}

.highlighted-content :deep(em) {
  font-style: italic;
  color: #374151;
}

.highlighted-content :deep(i) {
  font-style: italic;
  color: #374151;
}

.highlighted-content :deep(u) {
  text-decoration: underline;
  text-decoration-color: #6B7280;
}

.highlighted-content :deep(s) {
  text-decoration: line-through;
  text-decoration-color: #9CA3AF;
  color: #6B7280;
}

.highlighted-content :deep(del) {
  text-decoration: line-through;
  text-decoration-color: #9CA3AF;
  color: #6B7280;
}

/* 确保链接样式与ProseMirror编辑器一致 */
.highlighted-content :deep(a) {
  color: #4F46E5;
  text-decoration: underline;
  text-decoration-color: rgba(79, 70, 229, 0.3);
  transition: all 0.2s ease;
}

.highlighted-content :deep(a:hover) {
  color: #4338CA;
  text-decoration-color: rgba(67, 56, 202, 0.6);
}

/* 确保表格样式与ProseMirror编辑器一致 */
.highlighted-content :deep(table) {
  border-collapse: collapse;
  margin: 1rem 0;
  width: 100%;
  border: 1px solid #E5E7EB;
}

.highlighted-content :deep(th) {
  background-color: #F9FAFB;
  border: 1px solid #E5E7EB;
  padding: 0.5rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
}

.highlighted-content :deep(td) {
  border: 1px solid #E5E7EB;
  padding: 0.5rem;
  color: #374151;
}

/* 确保水平线样式与ProseMirror编辑器一致 */
.highlighted-content :deep(hr) {
  border: none;
  border-top: 2px solid #E5E7EB;
  margin: 2rem 0;
}

/* 确保选择高亮样式与ProseMirror编辑器一致 */
.highlighted-content ::selection {
  background-color: rgba(79, 70, 229, 0.2);
}

.highlighted-content *::selection {
  background-color: rgba(79, 70, 229, 0.2);
}

/* 增强AI高亮显示 - 确保在复杂内容中也能清晰可见 */
.highlighted-content :deep(.ai-highlight) {
  background-color: #fef08a !important; /* 黄色高亮，提高优先级 */
  padding: 0.1em 0.2em;
  border-radius: 3px;
  box-decoration-break: clone; /* 确保跨行高亮正确显示 */
  -webkit-box-decoration-break: clone;
}
</style> 