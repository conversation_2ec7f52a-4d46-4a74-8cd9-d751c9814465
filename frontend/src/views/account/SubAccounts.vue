<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-4xl mx-auto p-4">
      <h1 class="text-xl font-bold text-gray-800 mb-6">子账户管理</h1>
      <div class="bg-white p-5 rounded-xl shadow-sm">
        <p class="text-sm text-gray-600">子账户管理内容将在这里显示</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 这里可以添加组件的逻辑
</script>

<style scoped>
/* 组件样式 */
</style> 