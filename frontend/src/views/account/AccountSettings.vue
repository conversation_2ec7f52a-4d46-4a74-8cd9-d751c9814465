<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面主体 -->
    <div class="max-w-4xl mx-auto p-4">
      <h1 class="text-xl font-bold text-gray-800 mb-6">账户设置</h1>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- 左侧导航 -->
        <div class="md:col-span-1">
          <div class="bg-white rounded-xl shadow-sm p-3 sticky top-4">
            <div class="space-y-0.5">
              <button
                @click="activeTab = 'profile'"
                class="w-full text-left px-3 py-2.5 rounded-lg transition-colors duration-200 text-sm"
                :class="activeTab === 'profile' ? 'bg-primary bg-opacity-10 text-primary font-medium' : 'hover:bg-gray-100 text-gray-700'"
              >
                <div class="flex items-center space-x-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                  </svg>
                  <span>个人信息</span>
                </div>
              </button>

              <button
                @click="activeTab = 'password'"
                class="w-full text-left px-3 py-2.5 rounded-lg transition-colors duration-200 text-sm"
                :class="activeTab === 'password' ? 'bg-primary bg-opacity-10 text-primary font-medium' : 'hover:bg-gray-100 text-gray-700'"
              >
                <div class="flex items-center space-x-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 116 0z" clip-rule="evenodd" />
                  </svg>
                  <span>安全设置</span>
                </div>
              </button>
            </div>

            <div class="mt-6 p-3 bg-blue-50 rounded-lg">
              <div class="flex items-center space-x-2 mb-2">
                <div class="w-8 h-8 rounded-full bg-gradient-to-r from-primary to-primary-light flex items-center justify-center text-white font-bold text-sm" style="color: white;">
                  {{ userInitial }}
                </div>
                <div>
                  <p class="text-xs font-medium text-gray-900">{{ userInfo.nickname || userInfo.username }}</p>
                  <p class="text-xs text-gray-500">{{ userInfo.email }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧内容 -->
        <div class="md:col-span-2">
          <!-- 个人信息表单 -->
          <transition name="fade" mode="out-in">
            <div v-if="activeTab === 'profile'" class="bg-white rounded-xl shadow-sm p-5">
              <div class="flex items-center justify-between mb-5">
                <h2 class="text-base font-semibold text-gray-800">个人信息</h2>
                <div class="text-xs text-gray-500">上次更新: {{ lastUpdated }}</div>
              </div>

              <form @submit.prevent="handleUpdateProfile" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="group">
                    <label class="block text-xs font-medium text-gray-700 mb-1">用户名</label>
                    <div class="relative">
                      <span class="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                        </svg>
                      </span>
                      <input
                        v-model="userInfo.username"
                        type="text"
                        class="w-full pl-9 pr-3 py-2 rounded-lg bg-gray-50 border border-gray-200 text-gray-500 cursor-not-allowed text-sm"
                        disabled
                      />
                    </div>
                    <p class="mt-1 text-xs text-gray-500">用户名不可更改</p>
                  </div>

                  <div class="group">
                    <label class="block text-xs font-medium text-gray-700 mb-1">邮箱</label>
                    <div class="relative">
                      <span class="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                          <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                        </svg>
                      </span>
                      <input
                        v-model="userInfo.email"
                        type="email"
                        class="w-full pl-9 pr-3 py-2 rounded-lg bg-gray-50 border border-gray-200 text-gray-500 cursor-not-allowed text-sm"
                        disabled
                      />
                    </div>
                    <p class="mt-1 text-xs text-gray-500">邮箱地址不可更改</p>
                  </div>
                </div>

                <div class="group">
                  <label class="block text-xs font-medium text-gray-700 mb-1">昵称</label>
                  <div class="relative">
                    <span class="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 005 10a6 6 0 0012 0c0-.526-.077-1.034-.196-1.514A5.001 5.001 0 0010 11z" clip-rule="evenodd" />
                      </svg>
                    </span>
                    <input
                      v-model="userInfo.nickname"
                      type="text"
                      placeholder="请输入昵称"
                      class="w-full pl-9 pr-3 py-2 rounded-lg bg-white border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-20 transition-colors duration-200 group-hover:bg-gray-50 text-sm"
                    />
                  </div>
                  <p class="mt-1 text-xs text-gray-500">用于显示的名称，可随时修改</p>
                </div>

                <div class="flex justify-end pt-3">
                  <button
                    type="submit"
                    class="px-4 py-2 bg-gradient-to-r from-primary to-primary-light text-white rounded-md shadow hover:shadow-md hover:translate-y-[-1px] active:translate-y-[0px] transition-all duration-200 font-medium flex items-center space-x-2 text-sm"
                    :disabled="updating"
                  >
                    <span v-if="!updating">保存修改</span>
                    <span v-else class="flex items-center space-x-2">
                      <svg class="animate-spin h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>保存中...</span>
                    </span>
                  </button>
                </div>
              </form>
            </div>

            <!-- 修改密码表单 -->
            <div v-else-if="activeTab === 'password'" class="bg-white rounded-xl shadow-sm p-5">
              <div class="flex items-center justify-between mb-5">
                <h2 class="text-base font-semibold text-gray-800">安全设置</h2>
              </div>

              <form @submit.prevent="handleChangePassword" class="space-y-4">
                <div class="group">
                  <label class="block text-xs font-medium text-gray-700 mb-1">当前密码</label>
                  <div class="relative">
                    <span class="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 116 0z" clip-rule="evenodd" />
                      </svg>
                    </span>
                    <input
                      v-model="passwordForm.oldPassword"
                      type="password"
                      placeholder="请输入当前密码"
                      class="w-full pl-9 pr-3 py-2 rounded-lg bg-white border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-20 transition-colors duration-200 group-hover:bg-gray-50 text-sm"
                      required
                    />
                  </div>
                </div>

                <div class="group">
                  <label class="block text-xs font-medium text-gray-700 mb-1">新密码</label>
                  <div class="relative">
                    <span class="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 116 0z" clip-rule="evenodd" />
                      </svg>
                    </span>
                    <input
                      v-model="passwordForm.newPassword"
                      type="password"
                      placeholder="请输入新密码"
                      class="w-full pl-9 pr-3 py-2 rounded-lg bg-white border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-20 transition-colors duration-200 group-hover:bg-gray-50 text-sm"
                      required
                    />
                  </div>
                  <p class="mt-1 text-xs text-gray-500">密码至少需要6位字符</p>
                </div>

                <div class="group">
                  <label class="block text-xs font-medium text-gray-700 mb-1">确认新密码</label>
                  <div class="relative">
                    <span class="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 116 0z" clip-rule="evenodd" />
                      </svg>
                    </span>
                    <input
                      v-model="passwordForm.confirmPassword"
                      type="password"
                      placeholder="请再次输入新密码"
                      class="w-full pl-9 pr-3 py-2 rounded-lg bg-white border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-20 transition-colors duration-200 group-hover:bg-gray-50 text-sm"
                      required
                    />
                  </div>
                </div>

                <div class="pt-3">
                  <div class="p-3 bg-yellow-50 rounded-lg mb-3">
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <svg class="h-4 w-4 text-yellow-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                      </div>
                      <div class="ml-2">
                        <h3 class="text-xs font-medium text-yellow-800">注意事项</h3>
                        <div class="mt-0.5 text-xs text-yellow-700">
                          <p>修改密码后，将需要重新登录系统。请确保记住新密码。</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="flex justify-end">
                    <button
                      type="submit"
                      class="px-4 py-2 bg-gradient-to-r from-primary to-primary-light text-white rounded-md shadow hover:shadow-md hover:translate-y-[-1px] active:translate-y-[0px] transition-all duration-200 font-medium flex items-center space-x-2 text-sm"
                      :disabled="changingPassword"
                    >
                      <span v-if="!changingPassword">修改密码</span>
                      <span v-else class="flex items-center space-x-2">
                        <svg class="animate-spin h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>修改中...</span>
                      </span>
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </transition>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

const authStore = useAuthStore()
const userInfo = ref({})
const updating = ref(false)
const changingPassword = ref(false)
const activeTab = ref('profile')
const lastUpdated = ref('--')

const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 获取用户首字母
const userInitial = computed(() => {
  if (!userInfo.value) return '?'
  const name = userInfo.value.nickname || userInfo.value.username
  return name ? name.charAt(0).toUpperCase() : '?'
})

// 获取用户信息
const fetchUserInfo = () => {
  const info = authStore.user

  userInfo.value = {
    username: info?.username || '',
    email: info?.email || '',
    nickname: info?.nickname || ''
  }

  // 格式化更新时间
  if (info?.updated_at) {
    try {
      // 如果updated_at是ISO格式的字符串
      const date = new Date(info.updated_at)
      if (!isNaN(date.getTime())) {
        // 使用dayjs格式化日期
        lastUpdated.value = dayjs(date).format('YYYY-MM-DD')
      } else {
        lastUpdated.value = info.updated_at
      }
    } catch (e) {
      console.error('格式化日期时出错:', e)
      lastUpdated.value = info.updated_at || '--'
    }
  } else {
    lastUpdated.value = '--'
  }
}

// 更新个人信息
const handleUpdateProfile = async () => {
  if (updating.value) return

  updating.value = true
  try {
    // 检查数据有效性
    if (!userInfo.value.nickname) {
      userInfo.value.nickname = userInfo.value.username
    }

    // 发送更新请求
    await authStore.updateUserProfile({
      nickname: userInfo.value.nickname
    });

    ElMessage({
      message: '个人信息更新成功',
      type: 'success'
    })

    // 更新上次更新时间 - 直接使用当前时间而不是依赖API返回
    const now = new Date()
    lastUpdated.value = dayjs(now).format('YYYY-MM-DD')

  } catch (error) {
    console.error('更新个人信息失败:', error)

    // 详细输出错误信息，帮助调试
    if (error.response) {
      console.error('错误状态码:', error.response.status)
      console.error('错误数据:', error.response.data)
      console.error('错误头信息:', error.response.headers)
    } else if (error.request) {
      console.error('未收到响应，请求对象:', error.request)
    } else {
      console.error('错误消息:', error.message)
    }

    let errorMessage = '更新失败'
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail
    } else if (error.message) {
      errorMessage = error.message
    }

    ElMessage({
      message: errorMessage,
      type: 'error'
    })
  } finally {
    updating.value = false
  }
}

// 修改密码
const handleChangePassword = async () => {
  if (changingPassword.value) return

  if (!passwordForm.value.oldPassword) {
    ElMessage.warning('请输入当前密码')
    return
  }

  if (!passwordForm.value.newPassword || passwordForm.value.newPassword.length < 6) {
    ElMessage.warning('新密码长度不能少于6位')
    return
  }

  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    ElMessage.error('两次输入的新密码不一致')
    return
  }

  changingPassword.value = true
  try {
    await authStore.updateUserPassword(
      passwordForm.value.oldPassword,
      passwordForm.value.newPassword
    )
    ElMessage({
      message: '密码修改成功，请重新登录',
      type: 'success'
    })
    passwordForm.value = {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    }

    // 密码修改成功后，延迟1秒后退出登录
    setTimeout(() => {
      authStore.logout()
    }, 1000)
  } catch (error) {
    console.error('修改密码失败:', error)
    let message = '密码修改失败'
    if (error.response?.data?.detail) {
      message = error.response.data.detail
    }
    ElMessage({
      message,
      type: 'error'
    })
  } finally {
    changingPassword.value = false
  }
}

onMounted(() => {
  fetchUserInfo()
})
</script>

<style scoped>
/* 页面切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>