<template>
  <div class="h-screen bg-white flex overflow-hidden">
    <!-- 左侧表单区域 -->
    <div class="w-96 flex-shrink-0 bg-white border-r border-gray-200 flex flex-col h-full">
      <div class="flex-1 overflow-y-auto p-6">
          <div class="space-y-6">
            <!-- 客户档案 -->
            <div>
              <div class="flex items-center space-x-2 mb-3">
                <span class="material-icons-outlined text-[#4F46E5] text-lg">person</span>
                <span class="text-sm font-medium text-gray-700 flex items-center">
                  <span class="text-red-500 mr-1">*</span>客户档案
                </span>
                <button 
                  type="button"
                  @click="navigateToClientList"
                  class="text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium ml-auto flex items-center"
                >
                  + 新建档案
                </button>
              </div>

              <!-- 已选择客户信息显示 -->
              <div v-if="selectedClient" class="mb-3">
                <div class="flex items-center p-3 bg-[#4F46E5]/5 rounded-lg border border-[#4F46E5]/20">
                  <div class="w-8 h-8 rounded-full bg-[#4F46E5] text-white flex items-center justify-center text-xs font-medium mr-3">
                    {{ selectedClient.name?.charAt(0)?.toUpperCase() || '?' }}
                  </div>
                  <div class="flex-1">
                    <div class="font-medium text-gray-800 text-sm">{{ selectedClient.name }}</div>
                    <div class="text-xs text-gray-500">
                      {{ selectedClient.phone || '暂无联系方式' }}
                      {{ selectedClient.location ? ' · ' + selectedClient.location : '' }}
                    </div>
                  </div>
                  <button 
                    @click="clearSelectedClient"
                    class="text-gray-500 hover:text-gray-700 p-1 rounded-md hover:bg-gray-100 transition-colors"
                  >
                    <span class="material-icons-outlined text-sm">clear</span>
                  </button>
                </div>
              </div>

              <!-- 客户选择搜索框 -->
              <div v-else class="relative client-selector">
                <AnimatedInput
                  v-model="clientSearchQuery"
                  label="搜索客户"
                  placeholder="输入客户姓名、联系方式或学生ID"
                  type="input"
                  @input="handleClientSearch"
                  @focus="handleClientSearchFocus"
                />

                <!-- 搜索结果下拉框 -->
                <Teleport to="body">
                  <div 
                    v-if="showClientSelector && (clientSearchResults.length > 0 || clientSearchLoading)" 
                    ref="clientDropdown"
                    class="fixed z-[9999] bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto client-dropdown"
                    :style="dropdownStyle"
                  >
                    <!-- 搜索结果 -->
                    <div v-if="clientSearchResults.length > 0" class="p-1">
                      <div 
                        v-for="client in clientSearchResults" 
                        :key="client.id_hashed"
                        @click="selectClient(client)"
                        class="flex items-center p-2 hover:bg-gray-50 rounded-md cursor-pointer transition-colors"
                      >
                        <div class="w-6 h-6 rounded-full bg-[#4F46E5]/10 text-[#4F46E5] flex items-center justify-center text-xs font-medium mr-2">
                          {{ client.name?.charAt(0)?.toUpperCase() || '?' }}
                        </div>
                        <div class="flex-1">
                          <div class="font-medium text-gray-800 text-xs">{{ client.name }}</div>
                          <div class="text-xs text-gray-500">
                            {{ client.phone || '暂无联系方式' }}
                            {{ client.location ? ' · ' + client.location : '' }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 无搜索结果 -->
                    <div v-else-if="clientSearchQuery && !clientSearchLoading && clientSearchResults.length === 0" class="p-3 text-center text-gray-500 text-xs">
                      未找到匹配的客户
                    </div>
                    
                    <!-- 默认提示（首次点击且没有搜索内容时） -->
                    <div v-else-if="!clientSearchQuery && !clientSearchLoading && clientSearchResults.length === 0" class="p-3 text-center text-gray-500 text-xs">
                      输入客户姓名、联系方式或学生ID进行搜索
                    </div>

                    <!-- 加载状态 -->
                    <div v-else-if="clientSearchLoading" class="p-3 text-center text-gray-500 text-xs">
                      <span class="material-icons-outlined animate-spin mr-1 text-sm">refresh</span>
                      搜索中...
                    </div>
                  </div>
                </Teleport>
              </div>
            </div>



            <!-- 申请院校与专业 -->
            <div>
              <div class="flex items-center space-x-2 mb-3">
                <span class="material-icons-outlined text-[#4F46E5] text-lg">school</span>
                <span class="text-sm font-medium text-gray-700 flex items-center">
                  <span class="text-red-500 mr-1">*</span>申请院校与专业
                </span>
              </div>

              <!-- 已选择的院校专业信息显示 -->
              <div v-if="formData.selectedProgram" class="mb-3">
                <div class="flex items-start p-3 bg-[#4F46E5]/5 rounded-lg border border-[#4F46E5]/20">
                  <div class="w-8 h-8 rounded-full bg-white border border-gray-200 flex items-center justify-center mr-3 mt-1 overflow-hidden">
                    <img
                      :src="getSchoolLogoUrl(formData.selectedProgram)"
                      :alt="formData.selectedProgram.school_name_cn || '学校'"
                      class="w-full h-full object-contain"
                      @error="handleImageError"
                    />
                  </div>
                  <div class="flex-1">
                    <div class="font-medium text-gray-800 text-sm mb-1">
                      {{ formData.selectedProgram.school_name_cn }}
                      <span v-if="formData.selectedProgram.school_qs_rank" class="text-xs text-gray-500 ml-2">
                        QS排名: {{ formData.selectedProgram.school_qs_rank }}
                      </span>
                    </div>
                    <div class="text-sm text-[#4F46E5] font-medium mb-1">
                      {{ formData.selectedProgram.program_name_cn }}
                    </div>
                    <div class="text-xs text-gray-500">
                      {{ formData.selectedProgram.degree || '学位未知' }}
                      <span v-if="formData.selectedProgram.school_region">
                        · {{ formData.selectedProgram.school_region }}
                      </span>
                    </div>
                  </div>
                  <button 
                    @click="clearSelectedProgram"
                    class="text-gray-500 hover:text-gray-700 p-1 rounded-md hover:bg-gray-100 transition-colors"
                  >
                    <span class="material-icons-outlined text-sm">clear</span>
                  </button>
                </div>
              </div>

              <!-- 院校专业下拉选择 -->
              <div v-else class="relative program-selector" :class="{ 'disabled': !selectedClient }">
                <AnimatedInput
                  v-model="programSearchQuery"
                  :label="selectedClient ? '选择申请院校与专业' : '请先选择客户档案'"
                  :placeholder="selectedClient ? '选择具体要写的院校专业信息' : '请先选择客户档案'"
                  type="input"
                  :disabled="!selectedClient"
                  @input="handleProgramSearch"
                  @focus="handleProgramSearchFocus"
                />

                <!-- 搜索结果下拉框 -->
                <Teleport to="body">
                  <div 
                    v-if="showProgramSelector && selectedClient" 
                    ref="programDropdown"
                    class="fixed z-[9999] bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto program-dropdown"
                    :style="programDropdownStyle"
                  >
                    <!-- 暂无定校书数据时的提示 -->
                    <div v-if="!programsLoading && clientPrograms.length === 0" class="p-3 text-center">
                      <div class="text-gray-500 text-xs mb-2">暂无定校书数据</div>
                      <button 
                        @click="navigateToClientSchools"
                        class="text-[#4F46E5] text-xs hover:text-[#4338CA] transition-colors duration-200 font-medium"
                      >
                        前往添加定校书
                      </button>
                    </div>

                    <!-- 有定校书数据时显示搜索结果 -->
                    <div v-else-if="clientPrograms.length > 0">
                      <!-- 搜索结果 -->
                      <div v-if="filteredPrograms.length > 0" class="p-1">
                        <div 
                          v-for="program in filteredPrograms" 
                          :key="program.id"
                          @click="selectProgram(program.program_details)"
                          class="flex items-center p-2 hover:bg-gray-50 rounded-md cursor-pointer transition-colors"
                        >
                          <div class="w-6 h-6 rounded-full bg-white border border-gray-200 flex items-center justify-center mr-2 overflow-hidden">
                            <img
                              :src="getSchoolLogoUrl(program.program_details)"
                              :alt="program.program_details?.school_name_cn || '学校'"
                              class="w-full h-full object-contain"
                              @error="handleImageError"
                            />
                          </div>
                          <div class="flex-1">
                            <div class="font-medium text-gray-800 text-xs">
                              {{ program.program_details?.school_name_cn || '学校未知' }}
                              <span v-if="program.program_details?.school_qs_rank" class="text-xs text-gray-500 ml-2">
                                QS{{ program.program_details.school_qs_rank }}
                              </span>
                            </div>
                            <div class="text-xs text-[#4F46E5]">
                              {{ program.program_details?.program_name_cn || '专业未知' }} - {{ program.program_details?.degree || '学位未知' }}
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 无搜索结果 -->
                      <div v-else-if="programSearchQuery && !programsLoading && filteredPrograms.length === 0" class="p-3 text-center text-gray-500 text-xs">
                        未找到匹配的院校专业
                      </div>

                      <!-- 首次点击时显示所有项目 -->
                      <div v-else-if="!programSearchQuery && !programsLoading" class="p-3 text-center text-gray-500 text-xs">
                        输入院校或专业名称进行搜索，或浏览下方所有项目
                      </div>
                    </div>

                    <!-- 加载状态 -->
                    <div v-if="programsLoading" class="p-3 text-center text-gray-500 text-xs">
                      <span class="material-icons-outlined animate-spin mr-1 text-sm">refresh</span>
                      加载中...
                    </div>
                  </div>
                </Teleport>
              </div>
            </div>

            <!-- CV匹配状态显示 -->
            <div v-if="formData.selectedProgram">
              <div class="flex items-center space-x-2 mb-3">
                <span class="material-icons-outlined text-[#4F46E5] text-lg">assignment</span>
                <span class="text-sm font-medium text-gray-700">CV简历匹配状态</span>
              </div>

              <!-- CV匹配成功 -->
              <div v-if="cvMatchStatus === 'success' && cvMatchInfo" class="mb-3">
                <div class="p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div class="flex items-center mb-2">
                    <span class="material-icons-outlined text-green-600 mr-2 text-sm">check_circle</span>
                    <span class="text-green-800 font-medium text-sm">找到匹配的CV简历</span>
                  </div>
                  <div class="text-sm text-green-700 space-y-1">
                    <div><span class="font-medium">CV版本：</span>{{ cvMatchInfo.version_name }}</div>
                    <div><span class="font-medium">目标专业：</span>{{ cvMatchInfo.target_major }}</div>
                    <div><span class="font-medium">创建时间：</span>{{ formatDateTime(cvMatchInfo.created_at) }}</div>
                  </div>
                  <div class="mt-2 text-xs text-green-600">
                    PS生成将基于此CV简历的内容作为数据源
                  </div>
                </div>
              </div>

              <!-- CV匹配失败 -->
              <div v-else-if="cvMatchStatus === 'not_found'" class="mb-3">
                <div class="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                  <div class="flex items-center mb-2">
                    <span class="material-icons-outlined text-orange-600 mr-2 text-sm">warning</span>
                    <span class="text-orange-800 font-medium text-sm">未找到匹配的CV简历</span>
                  </div>
                  <div class="text-sm text-orange-700 mb-2">
                    {{ cvMatchMessage }}
                  </div>
                  <button
                    @click="navigateToCVGeneration"
                    class="text-orange-600 hover:text-orange-800 text-sm font-medium transition-colors"
                  >
                    前往生成CV简历 →
                  </button>
                </div>
              </div>

              <!-- CV匹配加载中 -->
              <div v-else-if="cvMatchStatus === 'loading'" class="mb-3">
                <div class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div class="flex items-center">
                    <span class="material-icons-outlined text-blue-600 mr-2 text-sm animate-spin">refresh</span>
                    <span class="text-blue-800 text-sm">正在查找匹配的CV简历...</span>
                  </div>
                </div>
              </div>

              <!-- CV匹配错误 -->
              <div v-else-if="cvMatchStatus === 'error'" class="mb-3">
                <div class="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div class="flex items-center mb-2">
                    <span class="material-icons-outlined text-red-600 mr-2 text-sm">error</span>
                    <span class="text-red-800 font-medium text-sm">CV匹配查询失败</span>
                  </div>
                  <div class="text-sm text-red-700">
                    {{ cvMatchMessage }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 学生自身经历描述 -->
            <div>
              <div class="flex items-center space-x-2 mb-3">
                <span class="material-icons-outlined text-[#4F46E5] text-lg">description</span>
                <span class="text-sm font-medium text-gray-700 flex items-center">
                  <span class="text-red-500 mr-1">*</span>学生自身经历描述
                </span>
                <button
                  @click="handleShowUploadDialog"
                  class="text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium flex items-center ml-auto"
                >
                  <span class="material-icons-outlined text-sm mr-1">cloud_upload</span>
                  上传
                </button>
              </div>

              <!-- 如果有解析成功的文件，显示文件信息 -->
              <div v-if="motivationProfile && uploadedFile" class="mb-3">
                <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                      <span class="material-icons-outlined text-blue-600 mr-2">description</span>
                      <div>
                        <div class="font-medium text-blue-800 text-sm">{{ uploadedFile.name }}</div>
                        <div class="text-xs text-blue-600">文件解析成功，已提取申请动机信息</div>
                      </div>
                    </div>
                    <button
                      @click="clearMotivationData"
                      class="text-blue-600 hover:text-blue-800 transition-colors p-1 rounded-md hover:bg-blue-100"
                    >
                      <span class="material-icons-outlined text-sm">clear</span>
                    </button>
                  </div>

                  <!-- 申请动机信息预览 -->
                  <div class="text-sm text-blue-700 bg-white p-3 rounded border border-blue-100">
                    <div class="grid grid-cols-1 gap-3">
                      <div v-if="motivationProfile.application_motivation">
                        <div class="flex items-start justify-between">
                          <div class="flex-1">
                            <span class="font-medium">申请动机：</span>
                            <span v-if="!showFullMotivation && motivationProfile.application_motivation.length > 50">
                              {{ motivationProfile.application_motivation.substring(0, 50) }}...
                            </span>
                            <span v-else>
                              {{ motivationProfile.application_motivation }}
                            </span>
                          </div>
                          <button
                            v-if="motivationProfile.application_motivation.length > 50"
                            @click="showFullMotivation = !showFullMotivation"
                            class="ml-2 text-xs text-blue-600 hover:text-blue-800 underline flex-shrink-0"
                          >
                            {{ showFullMotivation ? '收起' : '展开' }}
                          </button>
                        </div>
                      </div>
                      <div v-if="motivationProfile.career_planning">
                        <div class="flex items-start justify-between">
                          <div class="flex-1">
                            <span class="font-medium">职业规划：</span>
                            <span v-if="!showFullPlanning && motivationProfile.career_planning.length > 50">
                              {{ motivationProfile.career_planning.substring(0, 50) }}...
                            </span>
                            <span v-else>
                              {{ motivationProfile.career_planning }}
                            </span>
                          </div>
                          <button
                            v-if="motivationProfile.career_planning.length > 50"
                            @click="showFullPlanning = !showFullPlanning"
                            class="ml-2 text-xs text-blue-600 hover:text-blue-800 underline flex-shrink-0"
                          >
                            {{ showFullPlanning ? '收起' : '展开' }}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 如果没有解析文件或者解析失败，显示文本输入框 -->
              <div v-else>
                <el-input
                  v-model="formData.personalExperience"
                  type="textarea"
                  :rows="8"
                  placeholder="1. 你为什么要选择这个专业？&#10;2. 你对该专业的哪个领域感兴趣？（举例说明）&#10;3. 你未来的职业/人生规划是什么？&#10;..."
                  class="form-textarea"
                ></el-input>
              </div>
            </div>

            <!-- 院校要求及其他说明 -->
            <div>
              <label class="form-label">
                院校要求及其他说明
                <button class="ml-2 text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium">
                  上传
                </button>
              </label>
              <el-input
                v-model="formData.schoolRequirements"
                type="textarea"
                :rows="6"
                placeholder="请输入院校要求和其他需要说明的信息"
                class="form-textarea"
              ></el-input>
            </div>

            <!-- 目标字数 -->
            <div>
              <div class="flex items-center justify-between mb-3">
                <label class="form-label mb-0">目标字数</label>
                <span class="text-sm font-medium text-[#4F46E5]">{{ getWordLimitDisplay(formData.wordLimit) }}</span>
              </div>
              <div class="px-2">
                <el-slider
                  v-model="formData.wordLimit"
                  :marks="wordLimitMarks"
                  :step="1"
                  :min="0"
                  :max="20"
                  :show-tooltip="false"
                  class="custom-slider"
                />
              </div>
            </div>

            <!-- 段落设置 -->
            <div>
              <div class="flex items-center justify-between mb-3">
                <label class="form-label mb-0">段落设置</label>
                <span class="text-sm font-medium text-[#4F46E5]">{{ getParagraphDisplay(paragraphValue) }}</span>
              </div>
              <div class="px-2">
                <el-slider
                  v-model="paragraphValue"
                  :marks="paragraphMarks"
                  :step="1"
                  :min="0"
                  :max="6"
                  :show-tooltip="false"
                  @input="handleParagraphInput"
                  @change="handleParagraphChange"
                  class="custom-slider"
                />
              </div>
            </div>

            <!-- 底部按钮区域 -->
            <div class="pt-6 border-t border-gray-100 mt-6">
              <button 
                @click="handleGeneratePS" 
                :disabled="isGenerating || !isFormValid"
                :class="[
                  'w-full py-3 px-4 rounded-lg font-medium text-white transition-colors duration-200 flex items-center justify-center',
                  isFormValid && !isGenerating
                    ? 'bg-[#4F46E5] hover:bg-[#4338CA] shadow-sm cursor-pointer'
                    : 'bg-gray-400 cursor-not-allowed'
                ]"
              >
                <span v-if="isGenerating">
                  <span class="material-icons-outlined animate-spin mr-2 text-lg">refresh</span>
                  生成中...
                </span>
                <span v-else>
                  <span class="material-icons-outlined mr-2 text-lg">auto_fix_high</span>
                  提交生成
                </span>
              </button>
            </div>
          </div>
        </div>
    </div>

    <!-- 右侧编辑器区域 -->
    <div class="flex-1 bg-white h-full overflow-hidden">
      <TiptapEditor
        v-model="psContent"
        document-type="ps"
        placeholder="开始编写您的PS，或点击左侧'提交生成'获取AI建议..."
        @save="handleSave"
        @export="handleExport"
      />
    </div>

    <!-- 文件上传对话框 -->
    <div
      v-if="showUploadDialog"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click="handleCloseUploadDialog"
    >
      <div
        class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl"
        @click.stop
      >
        <!-- 对话框标题 -->
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-800">上传申请动机文档</h3>
          <button
            @click="handleCloseUploadDialog"
            class="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <span class="material-icons-outlined text-xl">close</span>
          </button>
        </div>

        <!-- 文件上传区域 -->
        <div class="mb-6">
          <div
            class="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-[#4F46E5] transition-colors"
            :class="{ 'border-[#4F46E5] bg-[#4F46E5]/5': isDragOver }"
            @drop="handleDrop"
            @dragover="handleDragOver"
            @dragenter="handleDragEnter"
            @dragleave="handleDragLeave"
          >
            <div class="space-y-4">
              <div class="flex justify-center">
                <span class="material-icons-outlined text-gray-400 text-2xl">folder_open</span>
              </div>

              <div>
                <h4 class="text-lg font-medium mb-2" :class="isDragOver ? 'text-[#4F46E5]' : 'text-gray-800'">
                  {{ isDragOver ? '释放文件以上传' : '点击或拖拽文件到此区域上传' }}
                </h4>
                <p class="text-sm mb-4" :class="isDragOver ? 'text-[#4F46E5]' : 'text-gray-500'">
                  {{ isDragOver ? '支持 .docx 格式' : '上传包含申请动机和职业规划的文档，自动提取相关信息' }}
                </p>

                <!-- 支持的文件格式图标 -->
                <div class="flex justify-center items-center space-x-6 mb-4">
                  <div class="flex flex-col items-center">
                    <div class="w-8 h-8 bg-blue-100 rounded flex items-center justify-center mb-1">
                      <span class="material-icons-outlined text-blue-600 text-sm">description</span>
                    </div>
                    <span class="text-xs text-gray-500">.docx</span>
                  </div>
                </div>
              </div>

              <!-- 选择文件按钮 -->
              <button
                @click="triggerFileInput"
                class="bg-[#4F46E5] hover:bg-[#4338CA] text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center mx-auto"
              >
                <span class="material-icons-outlined mr-2 text-sm">upload</span>
                选择文件
              </button>

              <!-- 隐藏的文件输入 -->
              <input
                ref="fileInput"
                type="file"
                class="hidden"
                @change="handleFileSelect"
                accept=".docx"
              >
            </div>
          </div>

          <!-- 已选择文件显示 -->
          <div v-if="uploadedFile" class="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <span class="material-icons-outlined text-green-600 mr-2">check_circle</span>
                <span class="text-sm font-medium text-green-800">{{ uploadedFile.name }}</span>
              </div>
              <button
                @click="clearUploadedFile"
                class="text-green-600 hover:text-green-800 transition-colors"
              >
                <span class="material-icons-outlined text-sm">close</span>
              </button>
            </div>
          </div>

          <!-- 解析状态显示 -->
          <div v-if="parsingResult.message" :class="['mt-3 text-xs p-3 rounded-lg', parsingResult.status === 'success' ? 'bg-green-50 text-green-700 border border-green-200' : 'bg-red-50 text-red-700 border border-red-200']">
            {{ parsingResult.message }}
          </div>
        </div>

        <!-- 对话框底部按钮 -->
        <div class="flex space-x-3">
          <button
            @click="handleCloseUploadDialog"
            class="flex-1 py-2 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            取消
          </button>
          <button
            @click="handleConfirmUpload"
            :disabled="!uploadedFile || isParsing"
            :class="[
              'flex-1 py-2 px-4 rounded-lg font-medium text-white transition-colors duration-200 flex items-center justify-center',
              uploadedFile && !isParsing ? 'bg-[#4F46E5] hover:bg-[#4338CA]' : 'bg-gray-400 cursor-not-allowed'
            ]"
          >
            <span v-if="isParsing" class="material-icons-outlined animate-spin mr-2 text-sm">refresh</span>
            {{ isParsing ? '正在解析...' : '确认' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, onBeforeUnmount, Teleport } from 'vue'
import { ElMessage, ElSlider } from 'element-plus'
import { useRouter, onBeforeRouteLeave } from 'vue-router'
import TiptapEditor from '@/components/writing/TiptapEditor.vue'
import AnimatedInput from '@/components/common/AnimatedInput.vue'
import { searchClients, getClientPrograms } from '@/api/client'
import { getPSClientProfiles, getPSClientModules, parsePSDocument, generatePSStream, savePSContent, matchCVForPS } from '@/api/aiwriting'
import { getSchoolLogo } from '@/utils/schoolLogos'
import { Document, Packer, Paragraph, TextRun, HeadingLevel } from 'docx'
import html2pdf from 'html2pdf.js'
import { saveAs } from 'file-saver'

const router = useRouter()

// 客户档案相关状态
const selectedClient = ref(null)
const showClientSelector = ref(false)
const clientSearchQuery = ref('')
const clientSearchResults = ref([])
const clientSearchLoading = ref(false)
const clientDropdown = ref(null)
const dropdownStyle = ref({})

// 院校专业选择相关状态
const showProgramSelector = ref(false)
const programSearchQuery = ref('')
const filteredPrograms = ref([])
const programDropdown = ref(null)
const programDropdownStyle = ref({})

// 表单数据
const formData = reactive({
  selectedProgram: null, // 选择的院校专业信息
  documentType: 'ps',
  personalExperience: '',
  schoolRequirements: '',
  wordLimit: 0, // 滑动器数值：0=不限制, 100的倍数表示字数(如5=500词, 10=1000词)
  paragraphSetting: 0 // 滑动器数值：0=不限制, 1=智能分段, 2=4段, 3=5段, 4=6段, 5=7段, 6=8段
})

// 定校书相关状态
const clientPrograms = ref([])
const programsLoading = ref(false)

// 文件上传相关状态
const showUploadDialog = ref(false)
const uploadedFile = ref(null)
const isParsing = ref(false)
const motivationProfile = ref(null)
const parsingResult = ref({ status: '', message: '' })
const fileInput = ref(null)
const isDragOver = ref(false)

// 预览展开/收起状态
const showFullMotivation = ref(false)
const showFullPlanning = ref(false)

// 客户模块数据
const clientModules = ref(null)
const clientModuleData = ref(null)
const selectedModules = reactive({
  education: [],
  academic: [],
  work: [],
  activities: [],
  awards: [],
  skills: [],
  language_scores: []
})



// 目标字数滑动器标记
const wordLimitMarks = {
  0: '不限制',
  5: '500',
  10: '1000', 
  15: '1500',
  20: '2000'
}

// 段落设置滑动器标记
const paragraphMarks = {
  0: '不限制',
  1: '智能分段',
  2: '4段',
  3: '5段',
  4: '6段',
  5: '7段',
  6: '8段'
}

// PS内容
const psContent = ref('')
const isGenerating = ref(false)

// CV匹配状态
const cvMatchInfo = ref(null)
const cvMatchStatus = ref('') // 'loading', 'success', 'not_found', 'error'
const cvMatchMessage = ref('')

// 独立的段落设置变量
const paragraphValue = ref(0)

// 获取字数限制显示文本
const getWordLimitDisplay = (value) => {
  if (value === 0) return '不限制'
  return `${value * 100}词`
}

// 将前端滑动器值转换为后端期望的字数限制值
const convertWordLimitForBackend = (sliderValue) => {
  // 如果是0，表示不限制
  if (sliderValue === 0) {
    return 0
  }

  // 对于非零值，直接转换为实际字数
  // 前端滑动器值 * 100 = 实际字数
  // 例如：5 * 100 = 500词，10 * 100 = 1000词
  const actualWords = sliderValue * 100

  // 后端直接接收实际字数值
  return actualWords
}

// 获取段落设置显示文本
const getParagraphDisplay = (value) => {
  const displays = ['不限制', '智能分段', '4段', '5段', '6段', '7段', '8段']
  return displays[value] || '不限制'
}

// 段落设置输入处理
const handleParagraphInput = (value) => {
  console.log('段落设置实时变化:', value)
  formData.paragraphSetting = value
}

// 段落设置变化处理
const handleParagraphChange = (value) => {
  console.log('段落设置变化完成:', value, getParagraphDisplay(value))
  formData.paragraphSetting = value
}

// 验证表单是否有效
const isFormValid = computed(() => {
  return selectedClient.value &&
         formData.selectedProgram &&
         (formData.personalExperience || motivationProfile.value) &&
         cvMatchStatus.value === 'success'
})

// 计算总选择数
const totalSelectedCount = computed(() => {
  return Object.keys(selectedModules).reduce((sum, key) => {
    return sum + selectedModules[key].length;
  }, 0);
})

// 通用选择切换函数
const toggleSelection = (key, id) => {
  const selectedArray = selectedModules[key];
  const index = selectedArray.indexOf(id);
  if (index > -1) {
    selectedArray.splice(index, 1);
  } else {
    selectedArray.push(id);
  }
}

// 导航到客户列表页面
const navigateToClientList = () => {
  router.push('/clients')
}

// 导航到客户档案页面的定校书部分
const navigateToClientProfile = () => {
  if (selectedClient.value) {
    router.push(`/clients/${selectedClient.value.id_hashed}`)
  } else {
    ElMessage.warning('请先选择客户档案')
  }
}

// 导航到客户档案页面的定校书标签页
const navigateToClientSchools = () => {
  if (selectedClient.value) {
    router.push(`/clients/${selectedClient.value.id_hashed}?tab=schools`)
  } else {
    ElMessage.warning('请先选择客户档案')
  }
}

// 获取学校Logo URL（优先使用数据库中的school_logo_url）
const getSchoolLogoUrl = (programDetails) => {
  if (!programDetails) return getDefaultSchoolLogo()

  // 优先使用数据库中的school_logo_url
  if (programDetails.school_logo_url) {
    return programDetails.school_logo_url
  }

  // 如果没有数据库logo，回退到硬编码映射表
  if (programDetails.school_name_cn) {
    return getSchoolLogo(programDetails.school_name_cn)
  }

  return getDefaultSchoolLogo()
}

// 获取默认学校Logo
const getDefaultSchoolLogo = () => {
  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0iIzRGNDZFNSIvPgo8cGF0aCBkPSJNMTIgMTZIMjhWMThIMTJWMTZaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMjBIMjhWMjJIMTJWMjBaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMjRIMjRWMjZIMTJWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTAgMTBIMzBWMTRIMTBWMTBaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTggMjhIMjJWMzJIMThWMjhaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K'
}

// 处理图片加载错误
const handleImageError = (event) => {
  // 如果图片加载失败，使用默认logo
  event.target.src = getDefaultSchoolLogo()
}

// 计算下拉框位置
const calculateDropdownPosition = () => {
  const inputElement = document.querySelector('.client-selector .animated-input-container')
  if (inputElement) {
    const rect = inputElement.getBoundingClientRect()
    dropdownStyle.value = {
      position: 'fixed',
      top: `${rect.bottom + 4}px`,
      left: `${rect.left}px`,
      width: `${rect.width}px`,
      zIndex: 9999
    }
  }
}

// 计算院校专业下拉框位置
const calculateProgramDropdownPosition = () => {
  const inputElement = document.querySelector('.program-selector .animated-input-container')
  if (inputElement) {
    const rect = inputElement.getBoundingClientRect()
    programDropdownStyle.value = {
      position: 'fixed',
      top: `${rect.bottom + 4}px`,
      left: `${rect.left}px`,
      width: `${rect.width}px`,
      zIndex: 9999
    }
  }
}

// 处理客户搜索输入框 focus 事件
const handleClientSearchFocus = async () => {
  showClientSelector.value = true
  calculateDropdownPosition()
  
  // 如果没有搜索内容，加载默认客户列表
  if (!clientSearchQuery.value || clientSearchQuery.value.trim() === '') {
    await loadDefaultClientList()
  }
}

// 加载默认客户列表
const loadDefaultClientList = async () => {
  clientSearchLoading.value = true
  try {
    const response = await searchClients('')
    let clients = response.data || response || []
    
    // 按最近修改时间排序
    clients.sort((a, b) => {
      const dateA = new Date(a.updated_at || a.created_at || 0)
      const dateB = new Date(b.updated_at || b.created_at || 0)
      return dateB - dateA // 倒序排列，最新的在前面
    })
    
    clientSearchResults.value = clients
  } catch (error) {
    console.error('加载客户列表失败:', error)
    clientSearchResults.value = []
  } finally {
    clientSearchLoading.value = false
  }
}

// 处理客户搜索
const handleClientSearch = async (query) => {
  if (!query || query.trim() === '') {
    if (showClientSelector.value) {
      await loadDefaultClientList()
    } else {
      clientSearchResults.value = []
    }
    return
  }

  clientSearchLoading.value = true
  try {
    const response = await searchClients(query.trim())
    let clients = response.data || response || []
    
    // 按最近修改时间排序搜索结果
    clients.sort((a, b) => {
      const dateA = new Date(a.updated_at || a.created_at || 0)
      const dateB = new Date(b.updated_at || b.created_at || 0)
      return dateB - dateA // 倒序排列，最新的在前面
    })
    
    clientSearchResults.value = clients
  } catch (error) {
    console.error('搜索客户失败:', error)
    clientSearchResults.value = []
    ElMessage.error('搜索客户失败')
  } finally {
    clientSearchLoading.value = false
  }
}

// 选择客户
const selectClient = async (client) => {
  try {
    selectedClient.value = client
    showClientSelector.value = false
    clientSearchQuery.value = ''
    clientSearchResults.value = []

    // 清空院校专业搜索框
    programSearchQuery.value = ''

    // 重置选择状态
    Object.keys(selectedModules).forEach(key => {
      selectedModules[key] = []
    })

    ElMessage.success(`已选择客户：${client.name}`)

    // 获取客户详细模块数据
    await getClientModuleData(client.id_hashed)

    // 自动加载定校书
    await loadClientPrograms()
  } catch (error) {
    console.error('选择客户时发生错误:', error)
    ElMessage.error('选择客户失败')
  }
}

// 清除选中的客户
const clearSelectedClient = () => {
  selectedClient.value = null
  psContent.value = ''
  // 清除定校书数据
  clientPrograms.value = []
  formData.selectedProgram = null
  // 清除模块数据
  clientModuleData.value = null
  Object.keys(selectedModules).forEach(key => {
    selectedModules[key] = []
  })
}

// 获取客户模块数据
const getClientModuleData = async (clientId) => {
  try {
    const response = await getPSClientModules(clientId)
    const data = response.data || response || {}
    console.log('获取到客户模块数据:', data)
    clientModuleData.value = data

    // 默认全选所有经历
    autoSelectAllExperiences(data)
  } catch (error) {
    console.error('获取客户模块数据失败:', error)
    ElMessage.error('获取客户详细信息失败')
    throw error
  }
}

// 自动选择所有经历
const autoSelectAllExperiences = (moduleData) => {
  console.log('开始自动选择所有经历...')

  // 教育经历
  if (moduleData.education && Array.isArray(moduleData.education)) {
    selectedModules.education = moduleData.education.map(item => item.id)
    console.log('自动选择教育经历:', selectedModules.education.length, '项')
  }

  // 学术经历
  if (moduleData.academic && Array.isArray(moduleData.academic)) {
    selectedModules.academic = moduleData.academic.map(item => item.id)
    console.log('自动选择学术经历:', selectedModules.academic.length, '项')
  }

  // 工作经历
  if (moduleData.work && Array.isArray(moduleData.work)) {
    selectedModules.work = moduleData.work.map(item => item.id)
    console.log('自动选择工作经历:', selectedModules.work.length, '项')
  }

  // 课外活动
  if (moduleData.activities && Array.isArray(moduleData.activities)) {
    selectedModules.activities = moduleData.activities.map(item => item.id)
    console.log('自动选择课外活动:', selectedModules.activities.length, '项')
  }

  // 荣誉奖项
  if (moduleData.awards && Array.isArray(moduleData.awards)) {
    selectedModules.awards = moduleData.awards.map(item => item.id)
    console.log('自动选择荣誉奖项:', selectedModules.awards.length, '项')
  }

  // 技能特长
  if (moduleData.skills && Array.isArray(moduleData.skills)) {
    selectedModules.skills = moduleData.skills.map(item => item.id)
    console.log('自动选择技能特长:', selectedModules.skills.length, '项')
  }

  // 语言成绩
  if (moduleData.language_scores && Array.isArray(moduleData.language_scores)) {
    selectedModules.language_scores = moduleData.language_scores.map(item => item.id)
    console.log('自动选择语言成绩:', selectedModules.language_scores.length, '项')
  }

  console.log('自动选择完成，总计选择:', totalSelectedCount.value, '项经历')
}

// 加载客户定校书
const loadClientPrograms = async () => {
  if (!selectedClient.value) {
    ElMessage.warning('请先选择客户档案')
    return
  }

  programsLoading.value = true
  try {
    const response = await getClientPrograms(selectedClient.value.id_hashed)
    clientPrograms.value = response.data || response || []
    
    if (clientPrograms.value.length === 0) {
      ElMessage.info('该客户暂无定校书数据')
    } else {
      ElMessage.success(`成功加载 ${clientPrograms.value.length} 个定校书项目`)
    }
  } catch (error) {
    console.error('加载定校书失败:', error)
    ElMessage.error('加载定校书失败')
    clientPrograms.value = []
  } finally {
    programsLoading.value = false
  }
}

// 选择院校专业
const selectProgram = async (programDetails) => {
  if (!programDetails) {
    ElMessage.error('程序信息无效')
    return
  }

  formData.selectedProgram = programDetails
  showProgramSelector.value = false
  programSearchQuery.value = ''
  filteredPrograms.value = []
  ElMessage.success(`已选择：${programDetails.school_name_cn} - ${programDetails.program_name_cn}`)

  // 选择院校专业后自动检查CV匹配
  await checkCVMatch()
}

// 检查CV匹配
const checkCVMatch = async () => {
  if (!selectedClient.value || !formData.selectedProgram) {
    return
  }

  cvMatchStatus.value = 'loading'
  cvMatchInfo.value = null
  cvMatchMessage.value = ''

  try {
    const requestData = {
      client_id: selectedClient.value.id_hashed,
      target_school: formData.selectedProgram.school_name_cn || formData.selectedProgram.school_name_en,
      degree: formData.selectedProgram.degree || 'master',
      major: formData.selectedProgram.program_name_cn || formData.selectedProgram.program_name_en
    }

    const response = await matchCVForPS(requestData)

    if (response.status === 'success') {
      cvMatchStatus.value = 'success'
      cvMatchInfo.value = response.cv_info
      cvMatchMessage.value = response.message
    } else if (response.status === 'not_found') {
      cvMatchStatus.value = 'not_found'
      cvMatchMessage.value = response.message
    } else {
      cvMatchStatus.value = 'error'
      cvMatchMessage.value = response.message || 'CV匹配查询失败'
    }
  } catch (error) {
    console.error('CV匹配查询失败:', error)
    cvMatchStatus.value = 'error'
    cvMatchMessage.value = '网络错误，请稍后重试'
  }
}

// 导航到CV生成页面
const navigateToCVGeneration = () => {
  if (selectedClient.value && formData.selectedProgram) {
    const targetMajor = `${formData.selectedProgram.school_name_cn || formData.selectedProgram.school_name_en} - ${formData.selectedProgram.degree || 'master'} - ${formData.selectedProgram.program_name_cn || formData.selectedProgram.program_name_en}`
    router.push({
      path: '/writing/cv',
      query: {
        client_id: selectedClient.value.id_hashed,
        target_major: targetMajor
      }
    })
  }
}

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return '未知'
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return '日期格式错误'
  }
}

// 处理院校专业搜索输入框 focus 事件
const handleProgramSearchFocus = async () => {
  if (!selectedClient.value) {
    // 没有选择客户档案时，不响应任何操作
    return
  }

  showProgramSelector.value = true
  calculateProgramDropdownPosition()

  // 如果没有搜索内容，显示所有定校书项目
  if (!programSearchQuery.value || programSearchQuery.value.trim() === '') {
    filteredPrograms.value = clientPrograms.value
  }
}

// 处理院校专业搜索
const handleProgramSearch = async (query) => {
  if (!selectedClient.value) {
    return
  }

  if (!query || query.trim() === '') {
    filteredPrograms.value = clientPrograms.value
    return
  }

  const searchTerm = query.trim().toLowerCase()
  filteredPrograms.value = clientPrograms.value.filter(program => {
    const schoolName = program.program_details?.school_name_cn?.toLowerCase() || ''
    const programName = program.program_details?.program_name_cn?.toLowerCase() || ''
    const degree = program.program_details?.degree?.toLowerCase() || ''
    
    return schoolName.includes(searchTerm) || 
           programName.includes(searchTerm) || 
           degree.includes(searchTerm)
  })
}

// 处理下拉框选择变化
const handleProgramChange = (programDetails) => {
  if (programDetails) {
    ElMessage.success(`已选择：${programDetails.school_name_cn} - ${programDetails.program_name_cn}`)
  }
}

// 清除选择的院校专业
const clearSelectedProgram = () => {
  formData.selectedProgram = null
  filteredPrograms.value = []
}



// 点击外部关闭下拉框
const handleClickOutside = (event) => {
  // 客户档案下拉框
  const dropdown = clientDropdown.value
  const clientSelector = document.querySelector('.client-selector')
  
  if (dropdown && clientSelector && 
      !dropdown.contains(event.target) && 
      !clientSelector.contains(event.target)) {
    showClientSelector.value = false
  }

  // 院校专业下拉框
  const programDropdownEl = programDropdown.value
  const programSelector = document.querySelector('.program-selector')
  
  if (programDropdownEl && programSelector && 
      !programDropdownEl.contains(event.target) && 
      !programSelector.contains(event.target)) {
    showProgramSelector.value = false
  }
}

// 监听窗口滚动和调整大小，更新下拉框位置
const handleWindowEvent = () => {
  if (showClientSelector.value) {
    calculateDropdownPosition()
  }
  if (showProgramSelector.value) {
    calculateProgramDropdownPosition()
  }
}

// 页面切换时自动取消生成
onBeforeRouteLeave((to, from, next) => {
  if (isGenerating.value) {
    isGenerating.value = false
    ElMessage.info('检测到页面切换，已自动取消生成')
  }
  next()
})

// 组件卸载时自动取消生成
onBeforeUnmount(() => {
  if (isGenerating.value) {
    isGenerating.value = false
  }
})

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  window.addEventListener('scroll', handleWindowEvent, true)
  window.addEventListener('resize', handleWindowEvent)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  window.removeEventListener('scroll', handleWindowEvent, true)
  window.removeEventListener('resize', handleWindowEvent)
})

// 文件上传相关方法
const handleShowUploadDialog = () => {
  showUploadDialog.value = true
  parsingResult.value = { status: '', message: '' }
}

const handleCloseUploadDialog = () => {
  showUploadDialog.value = false
  isDragOver.value = false
}

const triggerFileInput = () => {
  fileInput.value?.click()
}

const clearUploadedFile = () => {
  uploadedFile.value = null
  parsingResult.value = { status: '', message: '' }
  motivationProfile.value = null
  isDragOver.value = false
  showFullMotivation.value = false
  showFullPlanning.value = false
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const clearMotivationData = () => {
  uploadedFile.value = null
  parsingResult.value = { status: '', message: '' }
  motivationProfile.value = null
  formData.personalExperience = ''
  showFullMotivation.value = false
  showFullPlanning.value = false
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 拖拽事件处理
const handleDragOver = (event) => {
  event.preventDefault()
  event.stopPropagation()
}

const handleDragEnter = (event) => {
  event.preventDefault()
  event.stopPropagation()
  isDragOver.value = true
}

const handleDragLeave = (event) => {
  event.preventDefault()
  event.stopPropagation()
  isDragOver.value = false
}

const handleDrop = (event) => {
  event.preventDefault()
  event.stopPropagation()
  isDragOver.value = false

  const files = event.dataTransfer.files
  if (files.length > 0) {
    const file = files[0]

    // 验证文件类型
    const allowedExtensions = ['.docx']
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase()

    if (!allowedExtensions.includes(fileExtension)) {
      ElMessage.error('不支持的文件格式，请上传 .docx 文件')
      return
    }

    // 验证文件大小（限制为10MB）
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      ElMessage.error('文件大小不能超过10MB')
      return
    }

    uploadedFile.value = file
    parsingResult.value = { status: '', message: '' }
    motivationProfile.value = null

    ElMessage.success(`已选择文件: ${file.name}`)
  }
}

const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    // 验证文件类型
    const allowedExtensions = ['.docx']
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase()

    if (!allowedExtensions.includes(fileExtension)) {
      ElMessage.error('不支持的文件格式，请上传 .docx 文件')
      if (fileInput.value) {
        fileInput.value.value = ''
      }
      return
    }

    // 验证文件大小（限制为10MB）
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      ElMessage.error('文件大小不能超过10MB')
      if (fileInput.value) {
        fileInput.value.value = ''
      }
      return
    }

    uploadedFile.value = file
    parsingResult.value = { status: '', message: '' }
    motivationProfile.value = null
    ElMessage.success(`已选择文件: ${file.name}`)
  }
}

const handleConfirmUpload = async () => {
  if (!uploadedFile.value) {
    ElMessage.warning('请先选择一个文件')
    return
  }

  await handleParseFile()

  // 如果解析成功，关闭对话框
  if (parsingResult.value.status === 'success' && motivationProfile.value) {
    handleCloseUploadDialog()
  }
}

const handleParseFile = async () => {
  if (!uploadedFile.value) {
    ElMessage.warning('请先选择一个文件')
    return
  }

  isParsing.value = true
  parsingResult.value = { status: '', message: '' }
  motivationProfile.value = null
  const uploadFormData = new FormData()
  uploadFormData.append('file', uploadedFile.value)

  try {
    const response = await parsePSDocument(uploadFormData)
    motivationProfile.value = response.motivation_profile
    parsingResult.value = { status: 'success', message: '文件解析成功！' }
    // 重置展开状态
    showFullMotivation.value = false
    showFullPlanning.value = false
    ElMessage.success('申请动机信息解析成功')
  } catch (error) {
    const detail = error?.response?.data?.detail || '解析失败，请检查文件内容或联系管理员'
    parsingResult.value = { status: 'error', message: detail }
    ElMessage.error(detail)
    console.error('Parse file error:', error)
  } finally {
    isParsing.value = false
  }
}

// 生成PS初稿
const handleGeneratePS = async () => {
  if (!isFormValid.value) {
    ElMessage.warning('请先选择客户档案并完成所有必填项目')
    return
  }

  // 检查CV匹配状态
  if (cvMatchStatus.value !== 'success') {
    ElMessage.warning('请先确保找到匹配的CV简历')
    return
  }

  isGenerating.value = true
  psContent.value = ''

  try {
    ElMessage.info('正在基于CV简历生成PS，请稍候...')

    // 构建请求数据（基于CV模式，不再需要经历选择）
    const requestBody = {
      client_id: selectedClient.value.id_hashed,
      target_school: formData.selectedProgram.school_name_cn || formData.selectedProgram.school_name_en,
      degree: formData.selectedProgram.degree || 'master',
      major: formData.selectedProgram.program_name_cn || formData.selectedProgram.program_name_en,
      personal_experience: formData.personalExperience || '基于上传文档解析的申请动机信息',
      school_requirements: formData.schoolRequirements || '',
      word_limit: convertWordLimitForBackend(formData.wordLimit),
      paragraph_setting: formData.paragraphSetting,
      motivation_profile: motivationProfile.value
    }

    // 调用流式生成API
    const response = await generatePSStream(requestBody)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: '无法解析错误响应' }))
      throw new Error(errorData.detail || `生成失败: ${response.status}`)
    }

    if (!response.body) {
      throw new Error('浏览器不支持流式响应')
    }

    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let fullMarkdownContent = ''

    while (true) {
      const { done, value } = await reader.read()
      if (done) break

      const chunk = decoder.decode(value, { stream: true })
      fullMarkdownContent += chunk

      // 调试日志
      console.log('📥 接收到chunk:', chunk)
      console.log('📝 当前完整内容长度:', fullMarkdownContent.length)
      console.log('📝 当前换行符数量:', (fullMarkdownContent.match(/\n/g) || []).length)

      // 实时更新编辑器内容
      psContent.value = fullMarkdownContent
    }

    // 最终内容调试
    console.log('🎯 最终Markdown内容:', fullMarkdownContent)
    console.log('🎯 最终内容长度:', fullMarkdownContent.length)
    console.log('🎯 最终换行符数量:', (fullMarkdownContent.match(/\n/g) || []).length)

    ElMessage.success('PS生成完成，您可以继续编辑')
  } catch (error) {
    ElMessage.error(`生成失败: ${error.message}`)
    console.error('Generate PS error:', error)
  } finally {
    isGenerating.value = false
  }
}

// 生成PS模板
const generatePSTemplate = () => {
  const currentDate = new Date().toLocaleDateString('zh-CN')
  const docTypeTitle = formData.documentType === 'ps' ? 'Personal Statement' : 'Statement of Purpose'
  const wordLimitText = getWordLimitDisplay(formData.wordLimit)
  const paragraphText = getParagraphDisplay(formData.paragraphSetting)
  
  const schoolName = formData.selectedProgram?.school_name_cn || '目标院校'
  const programName = formData.selectedProgram?.program_name_cn || '目标专业'
  const degree = formData.selectedProgram?.degree || '硕士'
  const region = formData.selectedProgram?.school_region || ''
  
  return `
    <div style="margin-bottom: 30px; color: #374151;">
      <p style="margin-bottom: 20px; font-weight: 500; color: #4F46E5; font-size: 16px; text-align: center;">
        ${docTypeTitle}
      </p>
      <p style="margin-bottom: 10px;">日期: ${currentDate}</p>
      <p style="margin-bottom: 10px;">申请院校: <strong style="color: #4F46E5;">${schoolName}</strong>${region ? ` (${region})` : ''}</p>
      <p style="margin-bottom: 10px;">申请学位: <strong style="color: #4F46E5;">${degree}</strong></p>
      <p style="margin-bottom: 10px;">申请专业: <strong style="color: #4F46E5;">${programName}</strong></p>
      <p style="margin-bottom: 10px;">文书类型: <strong style="color: #4F46E5;">${formData.documentType.toUpperCase()}</strong></p>
      <p style="margin-bottom: 10px;">目标字数: ${wordLimitText}</p>
      <p style="margin-bottom: 20px;">段落设置: ${paragraphText}</p>
    </div>
    
    <h1 style="text-align: center; font-size: 20px; font-weight: bold; margin-bottom: 30px; color: #1F2937;">
      ${docTypeTitle}
    </h1>
    
    <p style="margin-bottom: 15px; line-height: 1.6; color: #374151;">
      ${formData.personalExperience}
    </p>
    
    ${formData.schoolRequirements ? `
    <p style="margin-bottom: 15px; line-height: 1.6; color: #374151;">
      <strong>院校要求及其他说明：</strong><br>
      ${formData.schoolRequirements.replace(/\n/g, '<br>')}
    </p>
    ` : ''}
    
    <p style="margin-bottom: 15px; line-height: 1.6; color: #374151;">
      我对<strong style="color: #4F46E5;">${programName}</strong>专业的学术兴趣和热情使我坚定地选择了这个领域。通过深入的学习和实践，我相信自己具备了在贵校继续深造的能力和决心。
    </p>
    
    <p style="margin-bottom: 15px; line-height: 1.6; color: #374151;">
      我特别向往<strong style="color: #4F46E5;">${schoolName}</strong>，因为该校在<strong style="color: #4F46E5;">${programName}</strong>领域的卓越声誉和丰富的研究机会与我的学术目标完美契合。
    </p>
    
    <p style="line-height: 1.6; color: #374151;">
      我确信我的学术背景、实践经验和对该领域的热情使我成为贵校项目的优秀候选人。我期待为学术社区做出贡献，并向杰出的教师和同学学习。
    </p>
  `
}

// 将HTML内容转换为docx文档段落
const htmlToDocxParagraphs = (htmlContent) => {
  // 简单的HTML解析，提取文本和基本格式
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = htmlContent
  
  const paragraphs = []
  
  const processNode = (node) => {
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent.trim()
      if (text) {
        return new TextRun({
          text: text,
          bold: false,
          italics: false
        })
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      const tagName = node.tagName.toLowerCase()
      const text = node.textContent.trim()
      
      if (text) {
        switch (tagName) {
          case 'h1':
            paragraphs.push(new Paragraph({
              text: text,
              heading: HeadingLevel.HEADING_1
            }))
            return null
          case 'h2':
            paragraphs.push(new Paragraph({
              text: text,
              heading: HeadingLevel.HEADING_2
            }))
            return null
          case 'h3':
            paragraphs.push(new Paragraph({
              text: text,
              heading: HeadingLevel.HEADING_3
            }))
            return null
          case 'p':
            paragraphs.push(new Paragraph({
              children: [new TextRun(text)]
            }))
            return null
          case 'strong':
          case 'b':
            return new TextRun({
              text: text,
              bold: true
            })
          case 'em':
          case 'i':
            return new TextRun({
              text: text,
              italics: true
            })
          case 'li':
            paragraphs.push(new Paragraph({
              children: [new TextRun(`• ${text}`)]
            }))
            return null
          default:
            return new TextRun(text)
        }
      }
    }
    return null
  }
  
  const walkNodes = (node) => {
    const textRuns = []
    for (let child of node.childNodes) {
      const result = processNode(child)
      if (result) {
        textRuns.push(result)
      } else if (child.nodeType === Node.ELEMENT_NODE) {
        walkNodes(child)
      }
    }
    if (textRuns.length > 0) {
      paragraphs.push(new Paragraph({
        children: textRuns
      }))
    }
  }
  
  walkNodes(tempDiv)
  
  // 如果没有解析出段落，创建一个默认段落
  if (paragraphs.length === 0) {
    const plainText = tempDiv.textContent || ''
    const lines = plainText.split('\n').filter(line => line.trim())
    lines.forEach(line => {
      paragraphs.push(new Paragraph({
        children: [new TextRun(line.trim())]
      }))
    })
  }
  
  return paragraphs
}

// 保存处理
const handleSave = async (data) => {
  console.log('Saving PS:', data)
  if (!selectedClient.value) {
    ElMessage.warning('请先选择客户档案')
    return
  }
  if (!formData.selectedProgram) {
    ElMessage.warning('请先选择申请院校和专业')
    return
  }

  // 优先使用传递的Markdown内容，如果没有则使用psContent.value
  const contentToSave = data.markdownContent || psContent.value
  if (!contentToSave || !contentToSave.trim()) {
    ElMessage.warning('PS内容不能为空')
    return
  }

  const now = new Date()
  const versionName = `版本 ${now.getFullYear()}/${String(now.getMonth() + 1).padStart(2, '0')}/${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`

  try {
    ElMessage.info('正在保存PS...')

    const requestBody = {
      client_id: selectedClient.value.id_hashed,
      target_school: formData.selectedProgram.school_name_cn || formData.selectedProgram.school_name_en,
      major: formData.selectedProgram.program_name_cn || formData.selectedProgram.program_name_en,
      degree: formData.selectedProgram.degree || 'master',
      content: contentToSave, // 使用Markdown内容
      version_name: versionName,
      word_limit: convertWordLimitForBackend(formData.wordLimit),
      paragraph_setting: formData.paragraphSetting
    }

    await savePSContent(requestBody)
    ElMessage.success('PS保存成功')
  } catch (error) {
    ElMessage.error('保存失败，请重试')
    console.error('Save PS error:', error)
  }
}

// 导出处理
const handleExport = async (data) => {
  const { content, textContent, format } = data;
  const clientName = selectedClient.value?.name || 'PS';
  // 使用 yyyy-mm-dd 格式的日期
  const dateStr = new Date().toLocaleDateString('sv');
  const fileName = `${clientName}_PS_${dateStr}`;

  if (!content) {
    ElMessage.warning('没有可导出的内容');
    return;
  }

  try {
    if (format === 'pdf') {
      ElMessage.info('正在生成PDF文件...')
      
      // 创建一个临时容器用于PDF生成
      const element = document.createElement('div')
      element.innerHTML = content
      element.style.cssText = `
        font-family: Arial, sans-serif;
        line-height: 1.6;
        max-width: 210mm;
        margin: 0 auto;
        padding: 20mm;
        color: #333;
        background: white;
      `
      
      // 添加PDF专用样式
      const style = document.createElement('style')
      style.textContent = `
        h1, h2, h3 { margin-top: 1.5em; margin-bottom: 0.5em; page-break-after: avoid; }
        h1 { font-size: 24px; }
        h2 { font-size: 20px; }
        h3 { font-size: 16px; }
        p { margin: 0.75em 0; }
        ul, ol { margin: 0.75em 0; padding-left: 2em; }
        li { margin: 0.25em 0; }
        blockquote { 
          border-left: 4px solid #ddd; 
          padding-left: 1em; 
          margin: 1em 0; 
          font-style: italic; 
        }
        strong { font-weight: bold; }
        em { font-style: italic; }
      `
      element.appendChild(style)
      
      const opt = {
        margin: [10, 10, 10, 10],
        filename: `${fileName}.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { 
          scale: 2,
          useCORS: true,
          letterRendering: true
        },
        jsPDF: { 
          unit: 'mm', 
          format: 'a4', 
          orientation: 'portrait' 
        }
      }
      
      await html2pdf().set(opt).from(element).save()
      ElMessage.success(`PDF文件 ${fileName}.pdf 已下载完成`)

    } else if (format === 'txt') {
      // 导出为纯文本文件
      const finalFileName = `${fileName}.txt`;
      const finalTextContent = textContent || content.replace(/<[^>]*>/g, '').replace(/\n\s*\n/g, '\n\n').trim();
      const blob = new Blob([finalTextContent], { type: 'text/plain;charset=utf-8' });
      saveAs(blob, finalFileName);
      ElMessage.success(`TXT文件 ${finalFileName} 已下载完成`);
      
    } else if (format === 'docx') {
      ElMessage.info('正在生成DOCX文件...')
      
      // 使用docx库创建真正的Word文档
      const paragraphs = htmlToDocxParagraphs(content)
      
      const doc = new Document({
        sections: [{
          properties: {},
          children: paragraphs
        }]
      })
      
      const buffer = await Packer.toBlob(doc)
      saveAs(buffer, `${fileName}.docx`)
      ElMessage.success(`DOCX文件 ${fileName}.docx 已下载完成`)
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error(`导出${format.toUpperCase()}文件失败，请重试`)
  }
}
</script> 

<style scoped>
/* 表单标签样式 */
.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

/* 表单输入框样式 - 统一边框颜色与AnimatedInput一致 */
.form-input :deep(.el-input__wrapper) {
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.form-input :deep(.el-input__wrapper:focus-within) {
  border-color: #4F46E5 !important;
  box-shadow: none !important;
}

.form-textarea :deep(.el-textarea__inner) {
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.form-textarea :deep(.el-textarea__inner:focus) {
  border-color: #4F46E5 !important;
  box-shadow: none !important;
}

/* 选择框样式 */
.form-input :deep(.el-select) {
  @apply w-full;
}

.form-input :deep(.el-select__wrapper) {
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.form-input :deep(.el-select__wrapper:focus-within) {
  border-color: #4F46E5 !important;
  box-shadow: none !important;
}

/* 客户选择器样式 */
.client-selector {
  position: relative;
}

.client-dropdown {
  min-width: 250px;
  max-width: 400px;
}

/* 院校专业选择器样式 */
.program-selector {
  position: relative;
}

.program-dropdown {
  min-width: 250px;
  max-width: 400px;
}

/* 禁用状态的输入框样式 */
.program-selector.disabled {
  pointer-events: none !important;
  cursor: not-allowed !important;
}

.program-selector.disabled :deep(.animated-input-wrapper) {
  background-color: #f9fafb !important;
  border-color: #e5e7eb !important;
  opacity: 0.8 !important;
  pointer-events: none !important;
  cursor: not-allowed !important;
}

.program-selector.disabled :deep(.animated-input) {
  background-color: transparent !important;
  color: #9ca3af !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
}

.program-selector.disabled :deep(.animated-label) {
  color: #9ca3af !important;
}

.program-selector.disabled :deep(.animated-input-wrapper:hover) {
  border-color: #e5e7eb !important;
  box-shadow: none !important;
  background-color: #f9fafb !important;
}

.program-selector.disabled :deep(.animated-input-wrapper:focus) {
  border-color: #e5e7eb !important;
  box-shadow: none !important;
  background-color: #f9fafb !important;
}

.program-selector.disabled :deep(.animated-input-wrapper:active) {
  border-color: #e5e7eb !important;
  box-shadow: none !important;
  background-color: #f9fafb !important;
}



/* 单选按钮样式 */
input[type="radio"] {
  @apply w-4 h-4;
  accent-color: #4F46E5;
}

/* Material Icons */
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  line-height: 1;
  transition: color 0.2s ease;
}

/* 按钮悬停效果 */
button:hover {
  @apply transform transition-transform duration-200;
}

/* 覆盖 Element Plus 主题色 */
:deep(.el-input) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-textarea) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-select) {
  --el-color-primary: #4F46E5 !important;
}

/* Element Plus 下拉选择框紫色主题 */
:deep(.el-select-dropdown__item.is-hovering) {
  background-color: rgba(79, 70, 229, 0.1) !important;
  color: #4F46E5 !important;
}

:deep(.el-select-dropdown__item.is-selected) {
  color: #4F46E5 !important;
  font-weight: 500 !important;
}

/* 滑动器样式 */
.custom-slider {
  margin: 15px 0 35px 0;
  height: 50px;
  position: relative;
}

.custom-slider :deep(.el-slider__runway) {
  background-color: #E5E7EB;
  height: 6px;
  border-radius: 3px;
  cursor: pointer;
}

.custom-slider :deep(.el-slider__bar) {
  background-color: #4F46E5;
  height: 6px;
  border-radius: 3px;
}

.custom-slider :deep(.el-slider__button) {
  background-color: #4F46E5;
  border: 2px solid #FFFFFF;
  width: 20px;
  height: 20px;
  box-shadow: 0 2px 6px rgba(79, 70, 229, 0.3);
  cursor: grab;
  transition: all 0.2s ease;
  position: relative;
  z-index: 10;
}

.custom-slider :deep(.el-slider__button:hover) {
  background-color: #4338CA;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.custom-slider :deep(.el-slider__button:active) {
  cursor: grabbing;
  transform: scale(0.95);
}

.custom-slider :deep(.el-slider__button-wrapper) {
  cursor: pointer;
  z-index: 10;
  position: relative;
}

.custom-slider :deep(.el-slider__marks) {
  top: 20px;
  position: absolute;
  width: 100%;
}

.custom-slider :deep(.el-slider__marks-text) {
  font-size: 12px;
  color: #6B7280;
  white-space: nowrap;
  cursor: pointer;
  user-select: none;
  position: absolute;
  transform: translateX(-50%);
  line-height: 1;
  margin-top: 5px;
}

.custom-slider :deep(.el-slider__marks .el-slider__marks-text:hover) {
  color: #4F46E5;
  font-weight: 500;
}

/* 确保滑动器可以正常交互 */
.custom-slider :deep(.el-slider) {
  pointer-events: all;
  height: 20px;
  position: relative;
}

.custom-slider :deep(.el-slider__runway) {
  margin: 0;
}

/* 强制确保滑动器交互 */
.custom-slider :deep(.el-slider__button-wrapper) {
  z-index: 999 !important;
  pointer-events: auto !important;
}

.custom-slider :deep(.el-slider__runway) {
  pointer-events: auto !important;
}

.custom-slider :deep(.el-slider) {
  z-index: 10 !important;
}

/* 禁用任何可能阻止交互的父级样式 */
.custom-slider {
  overflow: visible !important;
  z-index: 10;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .w-96 {
    @apply w-full;
  }
  
  .flex {
    @apply flex-col;
  }
  
  .h-screen {
    @apply min-h-screen;
  }
  
  /* 移动端调整 */
  .w-96 {
    @apply w-full;
  }
  
  .px-6 {
    @apply px-4;
  }
  
  .p-6 {
    @apply p-4;
  }
}

/* 经历选择相关样式 */
.experience-section {
  @apply mb-4 last:mb-0;
}

.section-title {
  @apply text-xs font-semibold text-gray-600 mb-2 px-1;
}

.section-items {
  @apply space-y-1;
}

.experience-item {
  @apply flex items-start p-2 hover:bg-white rounded-md cursor-pointer transition-colors duration-200 border border-transparent hover:border-gray-200;
}

.item-content {
  @apply ml-2 text-xs text-gray-700 select-none leading-relaxed flex-1;
}

/* 覆盖 Element Plus 主题色 */
:deep(.el-input) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-textarea) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-checkbox) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-slider) {
  --el-color-primary: #4F46E5 !important;
}
</style>

<style scoped>
/* 移除了重复的样式块 */



/* 单选按钮样式 */
input[type="radio"] {
  @apply w-4 h-4;
  accent-color: #4F46E5;
}

/* Material Icons */
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  line-height: 1;
  transition: color 0.2s ease;
}

/* 按钮悬停效果 */
button:hover {
  @apply transform transition-transform duration-200;
}

/* 覆盖 Element Plus 主题色 */
:deep(.el-input) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-textarea) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-select) {
  --el-color-primary: #4F46E5 !important;
}

/* Element Plus 下拉选择框紫色主题 */
:deep(.el-select-dropdown__item.is-hovering) {
  background-color: rgba(79, 70, 229, 0.1) !important;
  color: #4F46E5 !important;
}

:deep(.el-select-dropdown__item.is-selected) {
  color: #4F46E5 !important;
  font-weight: 500 !important;
}

/* 滑动器样式 */
.custom-slider {
  margin: 15px 0 35px 0;
  height: 50px;
  position: relative;
}

.custom-slider :deep(.el-slider__runway) {
  background-color: #E5E7EB;
  height: 6px;
  border-radius: 3px;
  cursor: pointer;
}

.custom-slider :deep(.el-slider__bar) {
  background-color: #4F46E5;
  height: 6px;
  border-radius: 3px;
}

.custom-slider :deep(.el-slider__button) {
  background-color: #4F46E5;
  border: 2px solid #FFFFFF;
  width: 20px;
  height: 20px;
  box-shadow: 0 2px 6px rgba(79, 70, 229, 0.3);
  cursor: grab;
  transition: all 0.2s ease;
  position: relative;
  z-index: 10;
}

.custom-slider :deep(.el-slider__button:hover) {
  background-color: #4338CA;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.custom-slider :deep(.el-slider__button:active) {
  cursor: grabbing;
  transform: scale(0.95);
}

.custom-slider :deep(.el-slider__button-wrapper) {
  cursor: pointer;
  z-index: 10;
  position: relative;
}

.custom-slider :deep(.el-slider__marks) {
  top: 20px;
  position: absolute;
  width: 100%;
}

.custom-slider :deep(.el-slider__marks-text) {
  font-size: 12px;
  color: #6B7280;
  white-space: nowrap;
  cursor: pointer;
  user-select: none;
  position: absolute;
  transform: translateX(-50%);
  line-height: 1;
  margin-top: 5px;
}

.custom-slider :deep(.el-slider__marks .el-slider__marks-text:hover) {
  color: #4F46E5;
  font-weight: 500;
}

/* 确保滑动器可以正常交互 */
.custom-slider :deep(.el-slider) {
  pointer-events: all;
  height: 20px;
  position: relative;
}

.custom-slider :deep(.el-slider__runway) {
  margin: 0;
}

/* 强制确保滑动器交互 */
.custom-slider :deep(.el-slider__button-wrapper) {
  z-index: 999 !important;
  pointer-events: auto !important;
}

.custom-slider :deep(.el-slider__runway) {
  pointer-events: auto !important;
}

.custom-slider :deep(.el-slider) {
  z-index: 10 !important;
}

/* 禁用任何可能阻止交互的父级样式 */
.custom-slider {
  overflow: visible !important;
  z-index: 10;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .w-96 {
    @apply w-full;
  }
  
  .flex {
    @apply flex-col;
  }
  
  .h-screen {
    @apply min-h-screen;
  }
  
  /* 移动端调整 */
  .w-96 {
    @apply w-full;
  }
  
  .px-6 {
    @apply px-4;
  }
  
  .p-6 {
    @apply p-4;
  }
}
</style> 