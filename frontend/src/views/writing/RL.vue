<template>
  <div class="h-screen bg-white flex overflow-hidden">
    <!-- 左侧表单区域 -->
    <div class="w-96 flex-shrink-0 bg-white border-r border-gray-200 flex flex-col h-full">
      <div class="flex-1 overflow-y-auto p-6">
          <div class="space-y-6">
            <!-- 客户档案 -->
            <div>
              <div class="flex items-center space-x-2 mb-3">
                <span class="material-icons-outlined text-[#4F46E5] text-lg">person</span>
                <span class="text-sm font-medium text-gray-700 flex items-center">
                  <span class="text-red-500 mr-1">*</span>客户档案
                </span>
                <button 
                  type="button"
                  @click="navigateToClientList"
                  class="text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium ml-auto flex items-center"
                >
                  + 新建档案
                </button>
              </div>

              <!-- 已选择客户信息显示 -->
              <div v-if="selectedClient" class="mb-3">
                <div class="flex items-center p-3 bg-[#4F46E5]/5 rounded-lg border border-[#4F46E5]/20">
                  <div class="w-8 h-8 rounded-full bg-[#4F46E5] text-white flex items-center justify-center text-xs font-medium mr-3">
                    {{ selectedClient.name?.charAt(0)?.toUpperCase() || '?' }}
                  </div>
                  <div class="flex-1">
                    <div class="font-medium text-gray-800 text-sm">{{ selectedClient.name }}</div>
                    <div class="text-xs text-gray-500">
                      {{ selectedClient.phone || '暂无联系方式' }}
                      {{ selectedClient.location ? ' · ' + selectedClient.location : '' }}
                    </div>
                  </div>
                  <button 
                    @click="clearSelectedClient"
                    class="text-gray-500 hover:text-gray-700 p-1 rounded-md hover:bg-gray-100 transition-colors"
                  >
                    <span class="material-icons-outlined text-sm">clear</span>
                  </button>
                </div>
              </div>

              <!-- 客户选择搜索框 -->
              <div v-else class="relative client-selector">
                <AnimatedInput
                  v-model="clientSearchQuery"
                  label="搜索客户"
                  placeholder="输入客户姓名、联系方式或学生ID"
                  type="input"
                  @input="handleClientSearch"
                  @focus="handleClientSearchFocus"
                />

                <!-- 搜索结果下拉框 -->
                <Teleport to="body">
                  <div 
                    v-if="showClientSelector && (clientSearchResults.length > 0 || clientSearchLoading)" 
                    ref="clientDropdown"
                    class="fixed z-[9999] bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto client-dropdown"
                    :style="dropdownStyle"
                  >
                    <!-- 搜索结果 -->
                    <div v-if="clientSearchResults.length > 0" class="p-1">
                      <div 
                        v-for="client in clientSearchResults" 
                        :key="client.id_hashed"
                        @click="selectClient(client)"
                        class="flex items-center p-2 hover:bg-gray-50 rounded-md cursor-pointer transition-colors"
                      >
                        <div class="w-6 h-6 rounded-full bg-[#4F46E5]/10 text-[#4F46E5] flex items-center justify-center text-xs font-medium mr-2">
                          {{ client.name?.charAt(0)?.toUpperCase() || '?' }}
                        </div>
                        <div class="flex-1">
                          <div class="font-medium text-gray-800 text-xs">{{ client.name }}</div>
                          <div class="text-xs text-gray-500">
                            {{ client.phone || '暂无联系方式' }}
                            {{ client.location ? ' · ' + client.location : '' }}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else-if="clientSearchLoading" class="p-3 text-center text-gray-500 text-xs">
                      <span class="material-icons-outlined animate-spin mr-1 text-sm">refresh</span>
                      搜索中...
                    </div>
                  </div>
                </Teleport>
              </div>
            </div>

            <!-- 推荐人信息 -->
            <div>
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center space-x-3">
                  <span class="text-sm font-medium text-gray-700">
                    <span class="text-red-500 mr-1">*</span>推荐人信息
                  </span>
                  <button 
                    @click="handleDownloadTemplate"
                    class="text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium flex items-center"
                  >
                    <span class="material-icons-outlined text-sm mr-1">download</span>
                    下载模板
                  </button>
                </div>
                <button 
                  @click="handleShowUploadDialog"
                  class="text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium flex items-center"
                >
                  <span class="material-icons-outlined text-sm mr-1">cloud_upload</span>
                  上传
                </button>
              </div>
              
              <!-- 推荐人信息显示 -->
              <!-- 如果有解析成功的文件，显示文件信息 -->
              <div v-if="recommenderProfile && uploadedFile" class="mb-3">
                <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                      <span class="material-icons-outlined text-blue-600 mr-2">description</span>
                      <div>
                        <div class="font-medium text-blue-800 text-sm">{{ uploadedFile.name }}</div>
                        <div class="text-xs text-blue-600">文件解析成功，已提取推荐人信息</div>
                      </div>
                    </div>
                    <button 
                      @click="clearRecommenderData"
                      class="text-blue-600 hover:text-blue-800 transition-colors p-1 rounded-md hover:bg-blue-100"
                    >
                      <span class="material-icons-outlined text-sm">clear</span>
                    </button>
                  </div>
                  
                  <!-- 推荐人信息预览 -->
                  <div class="text-sm text-blue-700 bg-white p-3 rounded border border-blue-100">
                    <div class="grid grid-cols-1 gap-1">
                      <div v-if="recommenderProfile.first_name_en || recommenderProfile.last_name_en">
                        <span class="font-medium">姓名：</span>{{ `${recommenderProfile.last_name_en || ''} ${recommenderProfile.first_name_en || ''}`.trim() }}
                      </div>
                      <div v-if="recommenderProfile.university">
                        <span class="font-medium">学校：</span>{{ recommenderProfile.university }}
                      </div>
                      <div v-if="recommenderProfile.position">
                        <span class="font-medium">职位：</span>{{ recommenderProfile.position }}
                      </div>
                      <div v-if="recommenderProfile.department">
                        <span class="font-medium">院系：</span>{{ recommenderProfile.department }}
                      </div>
                      <div v-if="recommenderProfile.email">
                        <span class="font-medium">邮箱：</span>{{ recommenderProfile.email }}
                      </div>
                      <div v-if="recommenderProfile.phone">
                        <span class="font-medium">电话：</span>{{ recommenderProfile.phone }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 如果没有解析文件或者解析失败，显示文本输入框 -->
              <div v-else>
                <el-input
                  v-model="formData.recommenderInfo"
                  type="textarea"
                  :rows="6"
                  placeholder="请输入推荐人名字、学校名称、学校职位、联系方式等信息"
                  class="form-textarea"
                ></el-input>
              </div>
            </div>

            <!-- 额外信息 -->
            <div>
              <label class="form-label">额外信息 (可选)</label>
              <el-input
                v-model="formData.additionalInfo"
                type="textarea"
                :rows="4"
                placeholder="输入希望在推荐信中额外强调的内容，或对AI的特定指示"
                class="form-textarea"
              ></el-input>
            </div>

            <!-- 目标字数 -->
            <div>
              <div class="flex items-center justify-between mb-3">
                <label class="form-label mb-0">目标字数</label>
                <span class="text-sm font-medium text-[#4F46E5]">{{ getWordLimitDisplay(formData.wordLimit) }}</span>
              </div>
              <div class="px-2">
                <el-slider
                  v-model="formData.wordLimit"
                  :marks="wordLimitMarks"
                  :step="1"
                  :min="0"
                  :max="20"
                  :show-tooltip="false"
                  class="custom-slider"
                />
              </div>
            </div>

            <!-- 底部按钮区域 -->
            <div class="pt-6 border-t border-gray-100 mt-6">
              <!-- 生成按钮 -->
              <button 
                v-if="!isGenerating"
                @click="handleGenerateRecommendation" 
                :disabled="!isFormValid"
                :class="[
                  'w-full py-3 px-4 rounded-lg font-medium text-white transition-all duration-300 flex items-center justify-center relative overflow-hidden',
                  isFormValid
                    ? 'bg-[#4F46E5] hover:bg-[#4338CA] shadow-sm cursor-pointer hover:shadow-lg'
                    : 'bg-gray-400 cursor-not-allowed'
                ]"
              >
                <span class="flex items-center">
                  <span class="material-icons-outlined mr-2 text-lg">auto_fix_high</span>
                  开始智能生成
                </span>
              </button>
              
              <!-- 生成中状态和取消按钮 -->
              <div v-else class="space-y-3">
                <!-- 生成状态显示 -->
                <div class="w-full py-3 px-4 rounded-lg bg-[#4F46E5] text-white font-medium flex items-center justify-center relative overflow-hidden">
                  <span class="flex items-center">
                    <span class="material-icons-outlined animate-spin mr-2 text-lg">auto_fix_high</span>
                    <span class="typing-text">AI正在智能生成中</span>
                    <span class="dots">
                      <span class="dot">.</span>
                      <span class="dot">.</span>
                      <span class="dot">.</span>
                    </span>
                  </span>
                  <!-- 生成中的进度条效果 -->
                  <div class="absolute bottom-0 left-0 h-1 bg-white/30 rounded-full progress-bar"></div>
                </div>
                
                <!-- 取消按钮 -->
                <button 
                  @click="handleCancelGeneration"
                  class="w-full py-2 px-4 rounded-lg border border-gray-300 text-gray-600 hover:text-gray-800 hover:border-gray-400 transition-colors duration-200 flex items-center justify-center"
                >
                  <span class="material-icons-outlined mr-2 text-sm">stop</span>
                  取消生成
                </button>
              </div>

              <!-- 温馨提示 -->
              <div class="mt-3 text-xs text-gray-500 text-center">
                <span class="material-icons-outlined text-xs align-middle mr-1">info</span>
                <span v-if="!isGenerating">AI将根据您选择的客户和推荐人信息生成推荐信</span>
                <span v-else>正在使用先进AI技术为您量身定制推荐信内容...</span>
              </div>
            </div>
          </div>
        </div>
    </div>

    <!-- 右侧编辑器区域 -->
    <div class="flex-1 bg-white h-full overflow-hidden">
      <TiptapEditor
        v-model="recommendationContent"
        document-type="recommendation"
        placeholder="开始编写推荐信，或点击左侧'提交生成'获取AI建议..."
        @save="handleSave"
        @export="handleExport"
      />
    </div>

    <!-- 文件上传对话框 -->
    <div 
      v-if="showUploadDialog" 
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click="handleCloseUploadDialog"
    >
      <div 
        class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl"
        @click.stop
      >
        <!-- 对话框标题 -->
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-800">上传文件</h3>
          <button 
            @click="handleCloseUploadDialog"
            class="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <span class="material-icons-outlined text-xl">close</span>
          </button>
        </div>

        <!-- 文件上传区域 -->
        <div class="mb-6">
          <div 
            class="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-[#4F46E5] transition-colors"
            :class="{ 'border-[#4F46E5] bg-[#4F46E5]/5': isDragOver }"
            @drop="handleDrop"
            @dragover="handleDragOver"
            @dragenter="handleDragEnter"
            @dragleave="handleDragLeave"
          >
            <div class="space-y-4">
              <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto">
                <span class="material-icons-outlined text-gray-400 text-2xl">folder_open</span>
              </div>
              
              <div>
                <h4 class="text-lg font-medium mb-2" :class="isDragOver ? 'text-[#4F46E5]' : 'text-gray-800'">
                  {{ isDragOver ? '释放文件以上传' : '点击或拖拽文件到此区域上传' }}
                </h4>
                <p class="text-sm mb-4" :class="isDragOver ? 'text-[#4F46E5]' : 'text-gray-500'">
                  {{ isDragOver ? '支持 .docx、.doc、.pdf、.txt 格式' : '上传推荐人简历、个人介绍等文件，自动提取推荐人信息' }}
                </p>
                
                <!-- 支持的文件格式图标 -->
                <div class="flex justify-center items-center space-x-6 mb-4">
                  <div class="flex flex-col items-center">
                    <div class="w-8 h-8 bg-red-100 rounded flex items-center justify-center mb-1">
                      <span class="material-icons-outlined text-red-600 text-sm">picture_as_pdf</span>
                    </div>
                    <span class="text-xs text-gray-500">.pdf</span>
                  </div>
                  <div class="flex flex-col items-center">
                    <div class="w-8 h-8 bg-blue-100 rounded flex items-center justify-center mb-1">
                      <span class="material-icons-outlined text-blue-600 text-sm">description</span>
                    </div>
                    <span class="text-xs text-gray-500">.doc</span>
                  </div>
                  <div class="flex flex-col items-center">
                    <div class="w-8 h-8 bg-blue-100 rounded flex items-center justify-center mb-1">
                      <span class="material-icons-outlined text-blue-600 text-sm">description</span>
                    </div>
                    <span class="text-xs text-gray-500">.docx</span>
                  </div>
                  <div class="flex flex-col items-center">
                    <div class="w-8 h-8 bg-gray-100 rounded flex items-center justify-center mb-1">
                      <span class="material-icons-outlined text-gray-600 text-sm">description</span>
                    </div>
                    <span class="text-xs text-gray-500">.txt</span>
                  </div>
                </div>
              </div>
              
              <!-- 选择文件按钮 -->
              <button 
                @click="triggerFileInput"
                class="bg-[#4F46E5] hover:bg-[#4338CA] text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center mx-auto"
              >
                <span class="material-icons-outlined mr-2 text-sm">upload</span>
                选择文件
              </button>
              
              <!-- 隐藏的文件输入 -->
              <input 
                ref="fileInput"
                type="file" 
                class="hidden" 
                @change="handleFileSelect" 
                accept=".docx,.doc,.pdf,.txt"
              >
            </div>
          </div>
          
          <!-- 已选择文件显示 -->
          <div v-if="uploadedFile" class="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <span class="material-icons-outlined text-green-600 mr-2">check_circle</span>
                <span class="text-sm font-medium text-green-800">{{ uploadedFile.name }}</span>
              </div>
              <button 
                @click="clearUploadedFile"
                class="text-green-600 hover:text-green-800 transition-colors"
              >
                <span class="material-icons-outlined text-sm">close</span>
              </button>
            </div>
          </div>
          
          <!-- 解析状态显示 -->
          <div v-if="parsingResult.message" :class="['mt-3 text-xs p-3 rounded-lg', parsingResult.status === 'success' ? 'bg-green-50 text-green-700 border border-green-200' : 'bg-red-50 text-red-700 border border-red-200']">
            {{ parsingResult.message }}
          </div>
        </div>

        <!-- 对话框底部按钮 -->
        <div class="flex space-x-3">
          <button 
            @click="handleCloseUploadDialog"
            class="flex-1 py-2 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            取消
          </button>
          <button 
            @click="handleConfirmUpload"
            :disabled="!uploadedFile || isParsing"
            :class="[
              'flex-1 py-2 px-4 rounded-lg font-medium text-white transition-colors duration-200 flex items-center justify-center',
              uploadedFile && !isParsing ? 'bg-[#4F46E5] hover:bg-[#4338CA]' : 'bg-gray-400 cursor-not-allowed'
            ]"
          >
            <span v-if="isParsing" class="material-icons-outlined animate-spin mr-2 text-sm">refresh</span>
            {{ isParsing ? '正在解析...' : '确认' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, onBeforeUnmount } from 'vue'
import { ElMessage, ElSlider } from 'element-plus'
import { useRouter, useRoute, onBeforeRouteLeave } from 'vue-router'
import TiptapEditor from '@/components/writing/TiptapEditor.vue'
import AnimatedInput from '@/components/common/AnimatedInput.vue'
import { searchClients } from '@/api/client'
import { parseRLTemplate, saveRL } from '@/api/aiwriting'
import { Document, Packer, Paragraph, TextRun, HeadingLevel } from 'docx'
import html2pdf from 'html2pdf.js'
import { saveAs } from 'file-saver'

const router = useRouter()
const route = useRoute()

// --- State Management ---
const selectedClient = ref(null)
const showClientSelector = ref(false)
const clientSearchQuery = ref('')
const clientSearchResults = ref([])
const clientSearchLoading = ref(false)
const clientDropdown = ref(null)
const dropdownStyle = ref({})

const uploadedFile = ref(null)
const isParsing = ref(false)
const recommenderProfile = ref(null)
const parsingResult = ref({ status: '', message: '' })

const formData = reactive({
  wordLimit: 0,
  additionalInfo: '',
  recommenderInfo: ''
})

const recommendationContent = ref('')
const isGenerating = ref(false)
const abortController = ref(null)

// 文件上传对话框相关状态
const showUploadDialog = ref(false)
const fileInput = ref(null)
const isDragOver = ref(false)

// --- Computed Properties ---
const isFormValid = computed(() => {
  return selectedClient.value && (recommenderProfile.value || formData.recommenderInfo.trim())
})

const wordLimitMarks = { 0: '不限制', 5: '500', 10: '1000', 15: '1500', 20: '2000' }
const getWordLimitDisplay = (value) => {
  if (value === 0) return '不限制'
  return `${value * 100}词`
}

// 将前端滑动器值转换为后端期望的字数限制值
const convertWordLimitForBackend = (sliderValue) => {
  // 如果是0，表示不限制
  if (sliderValue === 0) {
    return 0
  }

  // 对于非零值，直接转换为实际字数
  // 前端滑动器值 * 100 = 实际字数
  const actualWords = sliderValue * 100

  // 后端直接接收实际字数值
  return actualWords
}

// --- Methods ---

// 客户选择相关
const navigateToClientList = () => router.push('/clients')

const calculateDropdownPosition = () => {
  const inputElement = document.querySelector('.client-selector .animated-input-container')
  if (inputElement) {
    const rect = inputElement.getBoundingClientRect()
    dropdownStyle.value = {
      position: 'fixed',
      top: `${rect.bottom + 4}px`,
      left: `${rect.left}px`,
      width: `${rect.width}px`,
      zIndex: 9999
    }
  }
}

const handleClientSearchFocus = async () => {
  showClientSelector.value = true
  calculateDropdownPosition()
  if (!clientSearchQuery.value) await handleClientSearch('')
}

const handleClientSearch = async (query) => {
  clientSearchLoading.value = true
  try {
    const response = await searchClients(query || '')
    clientSearchResults.value = response.data || response || []
  } catch (error) {
    console.error('搜索客户失败:', error)
    clientSearchResults.value = []
  } finally {
    clientSearchLoading.value = false
  }
}

const selectClient = (client) => {
    selectedClient.value = client
    showClientSelector.value = false
    clientSearchQuery.value = ''
    clientSearchResults.value = []
    ElMessage.success(`已选择客户：${client.name}`)
}

const clearSelectedClient = () => {
  selectedClient.value = null
  recommendationContent.value = ''
  clearRecommenderData()
}

// 文件上传与解析
const handleDownloadTemplate = () => {
    const link = document.createElement('a')
  link.href = '/templates/推荐人信息收集表格.docx'
  link.download = '推荐人信息收集表格.docx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  ElMessage.success('模板下载链接已触发')
  }

// 文件上传对话框相关方法
const handleShowUploadDialog = () => {
  showUploadDialog.value = true
  // 重置状态
  parsingResult.value = { status: '', message: '' }
}

const handleCloseUploadDialog = () => {
  showUploadDialog.value = false
  isDragOver.value = false
}

const triggerFileInput = () => {
  fileInput.value?.click()
}

const clearUploadedFile = () => {
  uploadedFile.value = null
  parsingResult.value = { status: '', message: '' }
  recommenderProfile.value = null
  isDragOver.value = false
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 清除推荐人数据（包括文件和手动输入）
const clearRecommenderData = () => {
  uploadedFile.value = null
  parsingResult.value = { status: '', message: '' }
  recommenderProfile.value = null
  formData.recommenderInfo = ''
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 拖拽事件处理
const handleDragOver = (event) => {
  event.preventDefault()
  event.stopPropagation()
}

const handleDragEnter = (event) => {
  event.preventDefault()
  event.stopPropagation()
  isDragOver.value = true
}

const handleDragLeave = (event) => {
  event.preventDefault()
  event.stopPropagation()
  // 只有当离开整个拖拽区域时才设置为false
  if (!event.currentTarget.contains(event.relatedTarget)) {
    isDragOver.value = false
  }
}

const handleDrop = (event) => {
  event.preventDefault()
  event.stopPropagation()
  isDragOver.value = false
  
  const files = event.dataTransfer.files
  if (files.length > 0) {
    const file = files[0]
    
    // 验证文件类型
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
      'application/msword', // .doc
      'application/pdf', // .pdf
      'text/plain' // .txt
    ]
    
    const allowedExtensions = ['.docx', '.doc', '.pdf', '.txt']
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase()
    
    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
      ElMessage.error('不支持的文件格式，请上传 .docx、.doc、.pdf 或 .txt 文件')
      return
    }
    
    // 验证文件大小（限制为10MB）
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      ElMessage.error('文件大小不能超过10MB')
      return
    }
    
    uploadedFile.value = file
    parsingResult.value = { status: '', message: '' }
    recommenderProfile.value = null
    
    ElMessage.success(`已选择文件: ${file.name}`)
  }
}

const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    // 验证文件类型
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
      'application/msword', // .doc
      'application/pdf', // .pdf
      'text/plain' // .txt
    ]
    
    const allowedExtensions = ['.docx', '.doc', '.pdf', '.txt']
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase()
    
    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
      ElMessage.error('不支持的文件格式，请上传 .docx、.doc、.pdf 或 .txt 文件')
      // 清空文件输入
      if (fileInput.value) {
        fileInput.value.value = ''
      }
      return
    }
    
    // 验证文件大小（限制为10MB）
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      ElMessage.error('文件大小不能超过10MB')
      // 清空文件输入
      if (fileInput.value) {
        fileInput.value.value = ''
      }
      return
    }
    
    uploadedFile.value = file
    parsingResult.value = { status: '', message: '' }
    recommenderProfile.value = null
    ElMessage.success(`已选择文件: ${file.name}`)
  }
}

const handleConfirmUpload = async () => {
  if (!uploadedFile.value) {
    ElMessage.warning('请先选择一个文件')
    return
  }

  await handleParseFile()
  
  // 如果解析成功，关闭对话框（不填入文本框，保持文件解析状态）
  if (parsingResult.value.status === 'success' && recommenderProfile.value) {
    handleCloseUploadDialog()
  }
}

const handleParseFile = async () => {
  if (!uploadedFile.value) {
    ElMessage.warning('请先选择一个文件')
    return
  }

  isParsing.value = true
  parsingResult.value = { status: '', message: '' }
  recommenderProfile.value = null
  const uploadFormData = new FormData()
  uploadFormData.append('file', uploadedFile.value)

  try {
    const response = await parseRLTemplate(uploadFormData)
    recommenderProfile.value = response.parsed_data
    parsingResult.value = { status: 'success', message: '文件解析成功！' }
    ElMessage.success('推荐人信息解析成功')
  } catch (error) {
    const detail = error?.response?.data?.detail || '解析失败，请检查文件内容或联系管理员'
    parsingResult.value = { status: 'error', message: detail }
    ElMessage.error(detail)
    console.error('Parse file error:', error)
  } finally {
    isParsing.value = false
  }
}

// 将推荐人档案对象转换为文本格式
const formatRecommenderProfileToText = (profile) => {
  const parts = []
  
  if (profile.first_name_en || profile.last_name_en) {
    parts.push(`姓名：${profile.last_name_en || ''} ${profile.first_name_en || ''}`.trim())
  }
  
  if (profile.university) {
    parts.push(`学校：${profile.university}`)
  }
  
  if (profile.position) {
    parts.push(`职位：${profile.position}`)
  }
  
  if (profile.department) {
    parts.push(`院系：${profile.department}`)
  }
  
  if (profile.email) {
    parts.push(`邮箱：${profile.email}`)
  }
  
  if (profile.phone) {
    parts.push(`电话：${profile.phone}`)
  }
  
  if (profile.office_address) {
    parts.push(`办公地址：${profile.office_address}`)
  }
  
  if (profile.research_interests && profile.research_interests.length > 0) {
    parts.push(`研究兴趣：${profile.research_interests.join(', ')}`)
  }
  
  return parts.join('\n')
}


// 生成推荐信
const handleGenerateRecommendation = async () => {
  if (!isFormValid.value) {
    ElMessage.warning('请先选择客户并填写推荐人信息');
    return;
  }

  isGenerating.value = true;
  recommendationContent.value = '';
  abortController.value = new AbortController();

  // 优先使用解析后的结构化数据，如果没有则使用文本输入
  const recommenderData = recommenderProfile.value || {
    raw_text: formData.recommenderInfo
  };

  const requestBody = {
    client_id: selectedClient.value.id_hashed,
    recommender_profile: recommenderData,
    word_limit: convertWordLimitForBackend(formData.wordLimit),
    additional_info: formData.additionalInfo,
    selected_education_ids: [],
    selected_academic_ids: [],
    selected_work_ids: [],
    selected_activity_ids: [],
    selected_award_ids: [],
    selected_skill_ids: [],
    selected_language_score_ids: []
  };

  try {
    const response = await fetch('/api/ai-writing/rl/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
      signal: abortController.value.signal,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: '无法解析错误响应' }));
      throw new Error(errorData.detail || `生成失败: ${response.status}`);
    }

    if (!response.body) {
      throw new Error('浏览器不支持流式响应');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let fullMarkdownContent = '';

    ElMessage.info('✨ AI正在智能生成中...');
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const chunk = decoder.decode(value, { stream: true });
      fullMarkdownContent += chunk;
      
      // 直接在每个块上更新，以实现最流畅的打字机效果
      recommendationContent.value = fullMarkdownContent;

      // 短暂延迟使动画更自然
      await new Promise(resolve => setTimeout(resolve, 20));
    }
    
    if (fullMarkdownContent.trim()) {
        recommendationContent.value = fullMarkdownContent;
    }
    
    ElMessage.success('推荐信生成完成！');

  } catch (error) {
    if (error.name === 'AbortError') {
      ElMessage.warning('生成已取消');
    } else {
      ElMessage.error(error.message || '生成过程中发生未知错误');
      console.error('Generate Recommendation error:', error);
    }
  } finally {
    isGenerating.value = false;
    abortController.value = null;
  }
};

const handleCancelGeneration = () => {
  if (abortController.value) {
    abortController.value.abort();
    ElMessage.info('正在取消生成...');
  }
};

// 生命周期与事件监听
const handleClickOutside = (event) => {
  const dropdown = clientDropdown.value
  const clientSelector = document.querySelector('.client-selector')
  if (dropdown && clientSelector && !dropdown.contains(event.target) && !clientSelector.contains(event.target)) {
    showClientSelector.value = false
  }
}

onMounted(async () => {
  document.addEventListener('click', handleClickOutside)
  window.addEventListener('scroll', calculateDropdownPosition, true)
  window.addEventListener('resize', calculateDropdownPosition)
  
  // 检查是否为编辑模式
  await handleEditMode()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  window.removeEventListener('scroll', calculateDropdownPosition, true)
  window.removeEventListener('resize', calculateDropdownPosition)
})

// 处理编辑模式
const handleEditMode = async () => {
  const clientId = route.query.client
  const editId = route.query.edit

  // 如果有客户ID，自动选择客户
  if (clientId) {
    try {
      // 首先获取客户信息
      const response = await fetch(`/api/ai-writing/rl/clients/${clientId}`)
      if (response.ok) {
        const client = await response.json()
        await selectClient(client)
      }
    } catch (error) {
      console.error('获取客户信息失败:', error)
    }
  }

  // 如果有编辑ID，加载RL内容
  if (editId) {
    try {
      const response = await fetch(`/api/ai-writing/rl/documents/${editId}`)
      if (response.ok) {
        const document = await response.json()
        // 优先使用content_markdown字段，如果没有则使用content字段
        const content = document.content_markdown || document.content || ''
        recommendationContent.value = content

        // 如果有推荐人信息，设置到表单中
        if (document.recommender_name) {
          formData.recommenderInfo = document.recommender_name
        }

        if (document.version_name) {
          // 可以显示版本信息或其他处理
          console.log('加载的推荐信版本:', document.version_name)
        }

        ElMessage.success(`推荐信内容已加载${document.version_name ? ` - ${document.version_name}` : ''}`)
      } else {
        ElMessage.error('加载推荐信内容失败')
      }
    } catch (error) {
      console.error('加载推荐信内容失败:', error)
      ElMessage.error('加载推荐信内容失败')
    }
  }
}

onBeforeRouteLeave(() => {
  if (isGenerating.value) {
    handleCancelGeneration()
    ElMessage.info('检测到页面切换，已自动取消生成')
  }
})

onBeforeUnmount(() => {
  if (isGenerating.value) {
    handleCancelGeneration()
    }
})


// --- Tiptap Editor Handlers ---

const handleSave = async (data) => {
  console.log('Saving Recommendation:', data);
  if (!selectedClient.value) {
    ElMessage.warning('请先选择客户档案');
    return;
  }
  if (!recommenderProfile.value && !formData.recommenderInfo.trim()) {
    ElMessage.warning('请先填写推荐人信息或上传推荐人文件');
    return;
  }

  // 优先使用传递的Markdown内容，如果没有则使用recommendationContent.value
  const contentToSave = data.markdownContent || recommendationContent.value
  if (!contentToSave || !contentToSave.trim()) {
    ElMessage.warning('推荐信内容不能为空');
    return;
  }

  const recommenderName = recommenderProfile.value 
    ? `${recommenderProfile.value.last_name_en || ''} ${recommenderProfile.value.first_name_en || ''}`.trim() || 'Unknown Recommender'
    : formData.recommenderInfo.split('\n')[0] || '手动输入推荐人';

  const now = new Date();
  const versionName = `版本 ${now.getFullYear()}/${String(now.getMonth() + 1).padStart(2, '0')}/${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;

  try {
    ElMessage.info('正在保存推荐信...');

    const requestBody = {
      client_id: selectedClient.value.id_hashed,
      content_markdown: contentToSave, // 使用Markdown内容
      version_name: versionName,
      recommender_name: recommenderName,
    };

    const response = await saveRL(requestBody);
    
    ElMessage.success(`✅ 推荐信已成功保存 - ${response.client_name} (${versionName})`);

  } catch (error) {
    const detail = error?.response?.data?.detail || '保存失败，请检查网络连接后重试';
    ElMessage.error(detail);
    console.error('Save RL error:', error);
  }
};

const handleExport = async (data) => {
  const { content, textContent, format } = data;
  const clientName = selectedClient.value?.name || 'Recommendation';
  const dateStr = new Date().toLocaleDateString('sv');
  const fileName = `${clientName}_Recommendation_${dateStr}`;

  if (!content) {
    ElMessage.warning('没有可导出的内容');
    return;
  }

  try {
    if (format === 'pdf') {
      ElMessage.info('正在生成PDF文件...')

      // 创建临时DOM元素来渲染HTML内容
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = content;
      tempDiv.style.cssText = `
        width: 210mm;
        min-height: 297mm;
        padding: 20mm;
        margin: 0 auto;
        background: white;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        line-height: 1.6;
        color: #333;
        box-sizing: border-box;
      `;

      // 添加完整的PDF样式
      const style = document.createElement('style');
      style.textContent = `
        h1 { font-size: 24px; font-weight: bold; margin: 0 0 16px 0; color: #1a1a1a; }
        h2 { font-size: 20px; font-weight: bold; margin: 16px 0 12px 0; color: #1a1a1a; }
        h3 { font-size: 18px; font-weight: bold; margin: 12px 0 8px 0; color: #1a1a1a; }
        p { margin: 8px 0; }
        ul, ol { margin: 8px 0; padding-left: 20px; }
        li { margin: 4px 0; }
        strong { font-weight: bold; }
        em { font-style: italic; }
        hr { border: none; border-top: 1px solid #ddd; margin: 16px 0; }
        blockquote { margin: 16px 0; padding-left: 16px; border-left: 4px solid #ddd; font-style: italic; }
      `;

      tempDiv.appendChild(style);
      document.body.appendChild(tempDiv);

      const opt = {
        margin: [10, 10, 10, 10],
        filename: `${fileName}.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: {
          scale: 2,
          useCORS: true,
          letterRendering: true,
          width: tempDiv.offsetWidth,
          height: tempDiv.offsetHeight
        },
        jsPDF: {
          unit: 'mm',
          format: 'a4',
          orientation: 'portrait'
        },
        pagebreak: { mode: ['css', 'legacy'] }
      };

      try {
        await html2pdf().set(opt).from(tempDiv).save();
        ElMessage.success(`PDF文件 ${fileName}.pdf 已下载完成`);
      } finally {
        // 清理临时元素
        document.body.removeChild(tempDiv);
      }
    } else if (format === 'txt') {
      const finalTextContent = textContent || content.replace(/<[^>]*>/g, '').replace(/\n\s*\n/g, '\n\n').trim();
      const blob = new Blob([finalTextContent], { type: 'text/plain;charset=utf-8' });
      saveAs(blob, `${fileName}.txt`);
      ElMessage.success(`TXT文件下载完成`);
    } else if (format === 'docx') {
      ElMessage.info('正在生成DOCX文件...')
      const paragraphs = htmlToDocxParagraphs(content)
      const doc = new Document({ sections: [{ children: paragraphs }] })
      const buffer = await Packer.toBlob(doc)
      saveAs(buffer, `${fileName}.docx`)
      ElMessage.success(`DOCX文件下载完成`)
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error(`导出${format.toUpperCase()}文件失败`)
  }
}

const htmlToDocxParagraphs = (html) => {
    const plainText = html.replace(/<[^>]*>/g, '\n').replace(/\n\s*\n/g, '\n\n').trim();
    const lines = plainText.split('\n');
    return lines.map(line => new Paragraph({ children: [new TextRun(line)] }));
};

</script> 

<style scoped>
/* 表单标签样式 */
.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

/* 表单输入框样式 - 统一边框颜色与AnimatedInput一致 */
.form-textarea :deep(.el-textarea__inner) {
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.form-textarea :deep(.el-textarea__inner:focus) {
  border-color: #4F46E5 !important;
  box-shadow: none !important;
}

/* 客户选择器样式 */
.client-selector {
  position: relative;
}

.client-dropdown {
  min-width: 250px;
  max-width: 400px;
}

/* Material Icons */
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  line-height: 1;
  transition: color 0.2s ease;
}

/* 覆盖 Element Plus 主题色 */
:deep(.el-input) {
  --el-color-primary: #4F46E5 !important;
}
:deep(.el-textarea) {
  --el-color-primary: #4F46E5 !important;
}
:deep(.el-select) {
  --el-color-primary: #4F46E5 !important;
}
:deep(.el-select-dropdown__item.is-hovering) {
  background-color: rgba(79, 70, 229, 0.1) !important;
  color: #4F46E5 !important;
}
:deep(.el-select-dropdown__item.is-selected) {
  color: #4F46E5 !important;
  font-weight: 500 !important;
}

/* 滑动器样式 */
.custom-slider {
  margin: 15px 0 35px 0;
}
.custom-slider :deep(.el-slider__runway) {
  background-color: #E5E7EB;
  height: 6px;
}
.custom-slider :deep(.el-slider__bar) {
  background-color: #4F46E5;
  height: 6px;
}
.custom-slider :deep(.el-slider__button) {
  background-color: #4F46E5;
  border: 2px solid #FFFFFF;
  width: 20px;
  height: 20px;
}
.custom-slider :deep(.el-slider__marks-text) {
  font-size: 12px;
  color: #6B7280;
  margin-top: 15px;
}
.custom-slider :deep(.el-slider__marks-text:hover) {
  color: #4F46E5;
}

/* 强制Tiptap编辑器内容保留换行符，解决流式渲染格式问题 */
:deep(.ProseMirror) {
  white-space: pre-wrap;
}

/* 生成按钮动画效果 */
.typing-text {
  @apply font-medium;
}

.dots {
  @apply ml-1 flex;
}

.dot {
  @apply inline-block w-1 h-1 bg-white rounded-full mx-0.5;
  animation: typing 1.4s infinite;
}

.dot:nth-child(1) {
  animation-delay: 0s;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
  }
  
.progress-bar {
  animation: progress 2s ease-in-out infinite;
  width: 0%;
  }
  
@keyframes progress {
  0% {
    width: 0%;
    opacity: 0.6;
  }
  50% {
    width: 70%;
    opacity: 1;
  }
  100% {
    width: 100%;
    opacity: 0.3;
  }
}
</style> 