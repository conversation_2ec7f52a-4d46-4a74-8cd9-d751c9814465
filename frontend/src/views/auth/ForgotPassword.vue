<template>
  <div class="min-h-screen bg-tech-gradient relative overflow-hidden flex items-center justify-center p-4">
    <!-- 科技背景元素 -->
    <div class="absolute inset-0 tech-background">
      <!-- 网格背景 -->
      <div class="absolute inset-0 tech-grid opacity-30"></div>
      
      <!-- 浮动几何元素 -->
      <div class="absolute top-1/4 left-1/4 w-32 h-32 tech-hexagon opacity-20 animate-float-slow"></div>
      <div class="absolute top-3/4 right-1/4 w-24 h-24 tech-circle opacity-15 animate-float-medium"></div>
      <div class="absolute bottom-1/4 left-1/3 w-16 h-16 tech-triangle opacity-25 animate-float-fast"></div>
      
      <!-- 光晕效果 -->
      <div class="absolute top-0 left-0 w-96 h-96 bg-gradient-radial from-primary/5 to-transparent blur-3xl animate-pulse-slow"></div>
      <div class="absolute bottom-0 right-0 w-96 h-96 bg-gradient-radial from-indigo-300/5 to-transparent blur-3xl animate-pulse-slow delay-2000"></div>
      
      <!-- 粒子效果 -->
      <div class="particles">
        <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
        <div class="particle" style="left: 20%; animation-delay: 2s;"></div>
        <div class="particle" style="left: 30%; animation-delay: 4s;"></div>
        <div class="particle" style="left: 40%; animation-delay: 1s;"></div>
        <div class="particle" style="left: 50%; animation-delay: 3s;"></div>
        <div class="particle" style="left: 60%; animation-delay: 5s;"></div>
        <div class="particle" style="left: 70%; animation-delay: 1.5s;"></div>
        <div class="particle" style="left: 80%; animation-delay: 3.5s;"></div>
        <div class="particle" style="left: 90%; animation-delay: 0.5s;"></div>
      </div>
    </div>
    
    <div class="w-full max-w-md relative z-10">
      <!-- 忘记密码卡片 -->
      <div class="bg-white rounded-2xl shadow-xl p-8 space-y-6 border border-gray-100 relative z-10">
        <!-- Logo区域 -->
        <div class="flex flex-col items-center space-y-3">
          <div class="logo-gradient rounded-lg p-3 shadow-lg transform hover:scale-105 transition-transform duration-300">
            <h1 class="text-xl font-bold text-white">TunshuEdu</h1>
          </div>
          <h2 class="text-gray-600 font-medium">留学行业的AI工具箱</h2>
        </div>

        <!-- 忘记密码表单 -->
        <div class="text-center">
          <h3 class="text-lg font-semibold text-gray-800 mb-2">忘记密码</h3>
          <p class="text-sm text-gray-600 mb-4">请输入您的注册邮箱，我们将向您发送重置密码的链接</p>
        </div>

        <form @submit.prevent="handleSubmit" class="space-y-5" novalidate>
          <div class="group">
            <label class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
            <div class="relative">
              <input
                v-model="form.email"
                type="email"
                placeholder="请输入邮箱"
                class="w-full px-4 py-3 rounded-xl bg-gray-50 border border-gray-200 focus:border-indigo-600 focus:ring-2 focus:ring-indigo-600/20 transition-all duration-200 group-hover:bg-gray-100"
                :class="{ 'border-red-300 focus:border-red-500 focus:ring-red-500/20': errorMessage }"
                @input="clearError"
                required
              />
            </div>
            <!-- 错误提示区域 -->
            <transition name="error-fade">
              <div v-if="errorMessage" class="mt-2 text-sm text-red-600 flex items-center">
                <svg class="w-4 h-4 mr-1.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
                {{ errorMessage }}
              </div>
            </transition>
          </div>

          <button
            type="submit"
            class="w-full button-gradient text-white py-3 px-4 rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] active:translate-y-[0px] transition-all duration-200 font-medium"
            :disabled="loading"
          >
            <span v-if="!loading">发送重置链接</span>
            <span v-else class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              发送中...
            </span>
          </button>
        </form>

        <!-- 提示信息 -->
        <div class="text-xs text-gray-500 text-center">
          重置链接将在30分钟内有效，请及时查收邮件
        </div>

        <!-- 底部链接 -->
        <div class="flex items-center justify-center pt-4 border-t border-gray-100/50">
          <div class="flex items-center space-x-1">
            <span class="text-gray-500 text-sm">想起密码了？</span>
            <router-link to="/login" class="text-primary hover:text-primary-dark text-sm font-medium transition-colors">
              返回登录
            </router-link>
          </div>
        </div>
      </div>
      
      <!-- 版权信息 -->
      <div class="mt-6 text-center text-xs text-gray-500">
        <span>© 2025 囤鼠科技教育平台</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { forgotPassword } from '@/api/auth'

const router = useRouter()
const loading = ref(false)

const form = reactive({
  email: ''
})

// 添加错误消息状态
const errorMessage = ref('')

// 清除错误信息的方法
const clearError = () => {
  errorMessage.value = ''
}

const handleSubmit = async (event) => {
  // 阻止表单默认提交行为
  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }

  if (!form.email) {
    errorMessage.value = '请输入邮箱'
    return false
  }

  loading.value = true
  errorMessage.value = ''
  try {
    console.log('正在发送密码重置邮件到:', form.email)
    await forgotPassword(form.email)
    await router.push('/login')
  } catch (error) {
    console.error('密码重置邮件发送失败:', error)
    let message = '发送失败，请稍后重试'
    if (error.response?.data?.detail) {
      message = error.response.data.detail
    } else if (error.response?.data?.message) {
      message = error.response.data.message
    } else if (error.message) {
      message = error.message
    }
    errorMessage.value = message
  } finally {
    loading.value = false
  }
  
  return false
}
</script>

<style scoped>
/* 主背景渐变 */
.bg-tech-gradient {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 25%, #e2e8f0 50%, #f8fafc 75%, #f1f5f9 100%);
  background-size: 400% 400%;
  animation: gradientShift 20s ease infinite;
}

/* 网格背景 */
.tech-grid {
  background-image: 
    linear-gradient(rgba(79, 70, 229, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(79, 70, 229, 0.05) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 30s linear infinite;
}

/* 几何图形 */
.tech-hexagon {
  background: linear-gradient(45deg, rgba(79, 70, 229, 0.1), rgba(99, 102, 241, 0.05));
  clip-path: polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%);
  filter: blur(0.5px);
}

.tech-circle {
  background: radial-gradient(circle, rgba(99, 102, 241, 0.08) 0%, rgba(139, 69, 19, 0) 70%);
  border-radius: 50%;
  filter: blur(1px);
}

.tech-triangle {
  background: linear-gradient(60deg, rgba(79, 70, 229, 0.06), transparent);
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  filter: blur(0.8px);
}

/* 径向渐变工具类 */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-from), var(--tw-gradient-to));
}

/* 粒子效果 */
.particles {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 3px;
  height: 3px;
  background: rgba(79, 70, 229, 0.3);
  border-radius: 50%;
  animation: particleFloat 15s infinite linear;
}

.particle:nth-child(2n) {
  width: 2px;
  height: 2px;
  background: rgba(99, 102, 241, 0.4);
}

.particle:nth-child(3n) {
  width: 4px;
  height: 4px;
  background: rgba(67, 56, 202, 0.2);
}

/* 动画定义 */
@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) translateX(50px);
    opacity: 0;
  }
}

@keyframes float-slow {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

@keyframes float-medium {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(-3deg); }
}

@keyframes float-fast {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(8deg); }
}

@keyframes pulse-slow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.7; }
}

/* 动画应用类 */
.animate-float-slow {
  animation: float-slow 8s ease-in-out infinite;
}

.animate-float-medium {
  animation: float-medium 6s ease-in-out infinite;
}

.animate-float-fast {
  animation: float-fast 4s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

.delay-2000 {
  animation-delay: 2s;
}

/* 渐变样式 */
.logo-gradient {
  background: linear-gradient(135deg, #4F46E5, #7C3AED);
}

.button-gradient {
  background: linear-gradient(135deg, #4F46E5, #7C3AED);
}

/* 输入框样式优化 */
input::placeholder {
  color: #9CA3AF;
}

/* 错误提示动画 */
.error-fade-enter-active,
.error-fade-leave-active {
  transition: all 0.3s ease;
}

.error-fade-enter-from,
.error-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style> 