<template>
  <div class="application-materials space-y-5">
    <!-- 文件上传区域 -->
    <div class="pro-card">
      <div class="pro-card-header">
        <div class="pro-card-title">
          <span class="material-icons-outlined icon">cloud_upload</span>
          文件上传
        </div>
        <el-button type="primary" size="small" class="add-btn flex items-center" @click="handleShowUploadDialog">
          <span class="material-icons-outlined text-xs mr-1">add</span>
          上传文件
        </el-button>
      </div>
      <div class="pro-card-body">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          <div v-for="category in fileCategories" :key="category.key" 
               class="file-category-card p-4 border border-gray-200 rounded-lg hover:border-primary hover:shadow-md transition-all duration-200">
            <div class="flex items-center mb-3">
              <div class="w-10 h-10 bg-primary bg-opacity-10 rounded-lg flex items-center justify-center mr-3">
                <span class="material-icons-outlined text-primary text-lg">{{ category.icon }}</span>
              </div>
              <div>
                <h3 class="text-sm font-medium text-gray-900">{{ category.name }}</h3>
                <p class="text-xs text-gray-500">{{ getFileCount(category.key) }} 个文件</p>
              </div>
            </div>
            <div class="space-y-2">
              <div v-for="file in getFilesByCategory(category.key)" :key="file.id" 
                   class="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                <div class="flex items-center flex-1 min-w-0">
                  <span class="material-icons-outlined text-gray-400 mr-2 text-sm">{{ getFileIcon(file.type) }}</span>
                  <div class="flex-1 min-w-0">
                    <p class="text-xs font-medium text-gray-900 truncate">{{ file.name }}</p>
                    <p class="text-xs text-gray-500">{{ formatFileSize(file.size) }} • {{ formatDate(file.uploadDate) }}</p>
                  </div>
                </div>
                <div class="flex items-center space-x-1 ml-2">
                  <el-button type="primary" class="icon-btn" link @click="handlePreviewFile(file)">
                    <span class="material-icons-outlined text-xs">visibility</span>
                  </el-button>
                  <el-button type="primary" class="icon-btn" link @click="handleDownloadFile(file)">
                    <span class="material-icons-outlined text-xs">download</span>
                  </el-button>
                  <el-button type="danger" class="icon-btn" link @click="handleDeleteFile(file)">
                    <span class="material-icons-outlined text-xs">delete</span>
                  </el-button>
                </div>
              </div>
              <div v-if="getFilesByCategory(category.key).length === 0" 
                   class="text-center py-4 text-gray-400 text-xs">
                暂无文件
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 所有文件列表 -->
    <div class="pro-card">
      <div class="pro-card-header">
        <div class="pro-card-title">
          <span class="material-icons-outlined icon">folder</span>
          所有文件
          <span class="ml-2 px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">{{ allFiles.length }}</span>
        </div>
        <div class="flex items-center space-x-2">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索文件..."
            size="small"
            class="w-48"
            clearable
          >
            <template #prefix>
              <span class="material-icons-outlined text-gray-400 text-sm">search</span>
            </template>
          </el-input>
          <el-select v-model="filterCategory" placeholder="筛选分类" size="small" class="w-32" clearable>
            <el-option label="全部" value="" />
            <el-option v-for="category in fileCategories" :key="category.key" 
                       :label="category.name" :value="category.key" />
          </el-select>
        </div>
      </div>
      <div class="pro-card-body">
        <div class="space-y-3">
          <div v-for="file in filteredFiles" :key="file.id" 
               class="flex items-center justify-between p-3 border border-gray-100 rounded-lg hover:bg-gray-50 transition-colors duration-200">
            <div class="flex items-center flex-1 min-w-0">
              <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                <span class="material-icons-outlined text-gray-500 text-lg">{{ getFileIcon(file.type) }}</span>
              </div>
              <div class="flex-1 min-w-0">
                <div class="flex items-center space-x-2 mb-1">
                  <p class="text-sm font-medium text-gray-900 truncate">{{ file.name }}</p>
                  <span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full whitespace-nowrap">
                    {{ getCategoryName(file.category) }}
                  </span>
                </div>
                <div class="flex items-center space-x-4 text-xs text-gray-500">
                  <span>{{ formatFileSize(file.size) }}</span>
                  <span>{{ formatDate(file.uploadDate) }}</span>
                  <span v-if="file.description" class="truncate max-w-40">{{ file.description }}</span>
                </div>
              </div>
            </div>
            <div class="flex items-center space-x-1 ml-4">
              <el-button type="primary" class="icon-btn" link @click="handlePreviewFile(file)">
                <span class="material-icons-outlined text-xs">visibility</span>
              </el-button>
              <el-button type="primary" class="icon-btn" link @click="handleDownloadFile(file)">
                <span class="material-icons-outlined text-xs">download</span>
              </el-button>
              <el-button type="primary" class="icon-btn" link @click="handleEditFile(file)">
                <span class="material-icons-outlined text-xs">edit</span>
              </el-button>
              <el-button type="danger" class="icon-btn" link @click="handleDeleteFile(file)">
                <span class="material-icons-outlined text-xs">delete</span>
              </el-button>
            </div>
          </div>
          <div v-if="filteredFiles.length === 0" class="text-center py-8 text-gray-500">
            <span class="material-icons-outlined text-4xl text-gray-300 mb-2 block">folder_open</span>
            <p class="text-sm">{{ searchKeyword || filterCategory ? '没有找到匹配的文件' : '暂无申请材料，点击右上角添加' }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件上传弹窗 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传申请材料"
      width="600px"
      custom-class="custom-dialog"
    >
      <div class="px-2">
        <el-form :model="uploadForm" label-width="100px" class="text-sm">
          <el-form-item label="文件分类" required>
            <el-select v-model="uploadForm.category" class="w-full" placeholder="请选择文件分类">
              <el-option v-for="category in fileCategories" :key="category.key" 
                         :label="category.name" :value="category.key" />
            </el-select>
          </el-form-item>
          <el-form-item label="文件描述">
            <el-input v-model="uploadForm.description" placeholder="选填，可添加文件说明" />
          </el-form-item>
          <el-form-item label="选择文件" required>
            <el-upload
              ref="uploadRef"
              v-model:file-list="uploadFileList"
              :auto-upload="false"
              :limit="5"
              :accept="'.pdf,.doc,.docx,.jpg,.jpeg,.png,.zip,.rar'"
              :on-exceed="handleFileExceed"
              :on-change="handleFileChange"
              :before-remove="handleBeforeRemove"
              drag
              multiple
              class="w-full"
            >
              <div class="flex flex-col items-center justify-center py-8">
                <span class="material-icons-outlined text-4xl text-gray-400 mb-4">cloud_upload</span>
                <div class="text-center">
                  <p class="text-sm text-gray-600 mb-2">点击或拖拽文件到此区域上传</p>
                  <p class="text-xs text-gray-400">支持 PDF、Word、图片、压缩包等格式，单个文件不超过 50MB</p>
                </div>
              </div>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="flex justify-end space-x-2">
          <el-button class="cancel-btn" @click="handleCancelUpload">取消</el-button>
          <el-button 
            type="primary" 
            :disabled="!uploadForm.category || uploadFileList.length === 0 || isUploading"
            :loading="isUploading"
            @click="handleConfirmUpload"
            class="confirm-btn"
          >
            {{ isUploading ? '上传中...' : '确认上传' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 文件编辑弹窗 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑文件信息"
      width="500px"
      custom-class="custom-dialog"
    >
      <div class="px-2">
        <el-form :model="editForm" label-width="100px" class="text-sm">
          <el-form-item label="文件名称" required>
            <el-input v-model="editForm.name" />
          </el-form-item>
          <el-form-item label="文件分类" required>
            <el-select v-model="editForm.category" class="w-full">
              <el-option v-for="category in fileCategories" :key="category.key" 
                         :label="category.name" :value="category.key" />
            </el-select>
          </el-form-item>
          <el-form-item label="文件描述">
            <el-input v-model="editForm.description" type="textarea" :rows="3" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="flex justify-end space-x-2">
          <el-button class="cancel-btn" @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveEdit" class="confirm-btn">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 文件预览弹窗 -->
    <el-dialog
      v-model="previewDialogVisible"
      :title="previewFile?.name || '文件预览'"
      width="80%"
      custom-class="custom-dialog"
      style="max-width: 1000px;"
    >
      <div class="px-2">
        <div v-if="previewFile" class="text-center">
          <!-- 图片预览 -->
          <div v-if="isImageFile(previewFile.type)" class="max-h-96 overflow-hidden rounded-lg">
            <img :src="previewFile.url" :alt="previewFile.name" class="max-w-full h-auto" />
          </div>
          <!-- PDF预览 -->
          <div v-else-if="isPdfFile(previewFile.type)" class="h-96">
            <iframe :src="previewFile.url" class="w-full h-full border rounded-lg"></iframe>
          </div>
          <!-- 其他文件类型 -->
          <div v-else class="py-16">
            <span class="material-icons-outlined text-6xl text-gray-300 mb-4 block">description</span>
            <p class="text-gray-500 mb-4">此文件类型不支持在线预览</p>
            <el-button type="primary" @click="handleDownloadFile(previewFile)">
              <span class="material-icons-outlined text-sm mr-1">download</span>
              下载文件
            </el-button>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end">
          <el-button class="cancel-btn" @click="previewDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

// Props定义
interface Props {
  clientData: any
  isNewClient: boolean
}

const props = defineProps<Props>()

// 文件分类定义
const fileCategories = [
  { key: 'id_photo', name: '身份证照片', icon: 'person' },
  { key: 'passport_photo', name: '白底证件照（香港）', icon: 'portrait' },
  { key: 'passport_file', name: '护照文件', icon: 'flight' },
  { key: 'chsi_report', name: '学信网在线认证报告（中英文）', icon: 'verified' },
  { key: 'chsi_transcript', name: '学信网成绩单（中英文）', icon: 'assignment' },
  { key: 'enrollment_cert', name: '学校在读证明（中英文）', icon: 'school' },
  { key: 'transcript', name: '成绩单（中英文）', icon: 'grading' },
  { key: 'gpa_explanation', name: '绩点说明（中英文）', icon: 'info' },
  { key: 'grade_b_cert', name: '成绩B等级证明', icon: 'star' }
]

// 状态定义
const uploadDialogVisible = ref(false)
const editDialogVisible = ref(false)
const previewDialogVisible = ref(false)
const uploadFileList = ref([])
const uploadRef = ref()
const isUploading = ref(false)
const searchKeyword = ref('')
const filterCategory = ref('')

// 表单数据
const uploadForm = ref({
  category: '',
  description: ''
})

const editForm = ref({
  id: '',
  name: '',
  category: '',
  description: ''
})

const previewFile = ref(null)

// 模拟文件数据
const allFiles = ref([
  {
    id: '1',
    name: '身份证正面.jpg',
    category: 'id_photo',
    type: 'image/jpeg',
    size: 2048000,
    uploadDate: '2024-01-15',
    description: '身份证正面照片',
    url: '/placeholder-image.jpg'
  },
  {
    id: '2',
    name: '学信网认证报告.pdf',
    category: 'chsi_report',
    type: 'application/pdf',
    size: 1024000,
    uploadDate: '2024-01-14',
    description: '学信网在线认证报告中英文版',
    url: '/placeholder-pdf.pdf'
  }
])

// 计算属性
const filteredFiles = computed(() => {
  let files = allFiles.value
  
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    files = files.filter(file => 
      file.name.toLowerCase().includes(keyword) ||
      file.description?.toLowerCase().includes(keyword)
    )
  }
  
  if (filterCategory.value) {
    files = files.filter(file => file.category === filterCategory.value)
  }
  
  return files
})

// 方法定义
const getFilesByCategory = (categoryKey: string) => {
  return allFiles.value.filter(file => file.category === categoryKey)
}

const getFileCount = (categoryKey: string) => {
  return getFilesByCategory(categoryKey).length
}

const getCategoryName = (categoryKey: string) => {
  const category = fileCategories.find(cat => cat.key === categoryKey)
  return category?.name || '未知分类'
}

const getFileIcon = (fileType: string) => {
  if (fileType.includes('image')) return 'image'
  if (fileType.includes('pdf')) return 'picture_as_pdf'
  if (fileType.includes('word') || fileType.includes('document')) return 'description'
  if (fileType.includes('zip') || fileType.includes('rar')) return 'archive'
  return 'attach_file'
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD')
}

const isImageFile = (fileType: string) => {
  return fileType.includes('image')
}

const isPdfFile = (fileType: string) => {
  return fileType.includes('pdf')
}

// 事件处理方法
const handleShowUploadDialog = () => {
  uploadForm.value = {
    category: '',
    description: ''
  }
  uploadFileList.value = []
  uploadDialogVisible.value = true
}

const handleFileExceed = () => {
  ElMessage.warning('最多只能上传5个文件')
}

const handleFileChange = (file: any) => {
  if (file.size > 50 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过50MB')
    uploadFileList.value = uploadFileList.value.filter((f: any) => f.uid !== file.uid)
    return
  }
}

const handleBeforeRemove = () => {
  return true
}

const handleCancelUpload = () => {
  uploadDialogVisible.value = false
  uploadFileList.value = []
}

const handleConfirmUpload = async () => {
  if (!uploadForm.value.category) {
    ElMessage.warning('请选择文件分类')
    return
  }
  
  if (uploadFileList.value.length === 0) {
    ElMessage.warning('请选择要上传的文件')
    return
  }

  isUploading.value = true

  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    uploadFileList.value.forEach((file: any, index: number) => {
      allFiles.value.push({
        id: String(Date.now() + index),
        name: file.name,
        category: uploadForm.value.category,
        type: file.raw.type,
        size: file.size,
        uploadDate: dayjs().format('YYYY-MM-DD'),
        description: uploadForm.value.description,
        url: URL.createObjectURL(file.raw)
      })
    })

    ElMessage.success('文件上传成功')
    uploadDialogVisible.value = false
    uploadFileList.value = []
  } catch (error) {
    ElMessage.error('文件上传失败')
  } finally {
    isUploading.value = false
  }
}

const handlePreviewFile = (file: any) => {
  previewFile.value = file
  previewDialogVisible.value = true
}

const handleDownloadFile = (file: any) => {
  const link = document.createElement('a')
  link.href = file.url
  link.download = file.name
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const handleEditFile = (file: any) => {
  editForm.value = {
    id: file.id,
    name: file.name,
    category: file.category,
    description: file.description || ''
  }
  editDialogVisible.value = true
}

const handleSaveEdit = () => {
  const fileIndex = allFiles.value.findIndex(f => f.id === editForm.value.id)
  if (fileIndex !== -1) {
    allFiles.value[fileIndex] = {
      ...allFiles.value[fileIndex],
      name: editForm.value.name,
      category: editForm.value.category,
      description: editForm.value.description
    }
    ElMessage.success('文件信息已更新')
    editDialogVisible.value = false
  }
}

const handleDeleteFile = async (file: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文件 "${file.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const fileIndex = allFiles.value.findIndex(f => f.id === file.id)
    if (fileIndex !== -1) {
      allFiles.value.splice(fileIndex, 1)
      ElMessage.success('文件已删除')
    }
  } catch {
    // 用户取消删除
  }
}
</script>

<style lang="postcss" scoped>
.application-materials {
  @apply space-y-5;
}

.file-category-card {
  transition: all 0.2s ease;
}

.file-category-card:hover {
  transform: translateY(-1px);
}

.pro-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-100;
}

.pro-card-header {
  @apply border-b border-gray-100 flex items-center justify-between;
  height: 3.5rem;
  padding: 0 0.875rem;
}

.pro-card-body {
  @apply p-4;
}

.pro-card-title {
  @apply text-gray-800 font-medium flex items-center;
  font-weight: 600;
}

.pro-card-title .icon {
  @apply mr-2 text-primary text-lg;
}

:deep(.icon-btn) {
  padding: 6px !important;
  min-width: unset !important;
  width: 32px !important;
  height: 32px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.icon-btn.is-link) {
  padding: 2px !important;
  min-width: unset !important;
  width: 24px !important;
  height: 24px !important;
}

:deep(.icon-btn .material-icons-outlined) {
  margin: 0 !important;
}

.add-btn {
  @apply text-white font-medium;
  background-color: #4F46E5;
  border-color: #4F46E5;
}

.add-btn:hover {
  background-color: #4338CA;
  border-color: #4338CA;
}

.cancel-btn {
  @apply text-gray-700 border-gray-300;
}

.cancel-btn:hover {
  @apply bg-gray-50 text-gray-900;
}

.confirm-btn {
  @apply text-white font-medium;
  background-color: #4F46E5;
  border-color: #4F46E5;
}

.confirm-btn:hover {
  background-color: #4338CA;
  border-color: #4338CA;
}

.custom-dialog :deep(.el-dialog__header) {
  padding: 16px 24px;
  margin: 0;
  border-bottom: 1px solid #f1f5f9;
}

.custom-dialog :deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 500;
}

.custom-dialog :deep(.el-dialog__body) {
  padding: 24px;
}

.custom-dialog :deep(.el-dialog__footer) {
  padding: 16px 24px;
  border-top: 1px solid #f1f5f9;
}

:deep(.el-upload-dragger) {
  border-color: #d1d5db;
}

:deep(.el-upload-dragger:hover) {
  border-color: #4F46E5;
}

.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 20px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: 'liga';
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}
</style> 