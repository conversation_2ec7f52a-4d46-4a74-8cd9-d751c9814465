<template>
  <div class="client-list-page max-w-7xl mx-auto">
    <!-- 面包屑导航 -->
    <div class="mb-6">
      <!-- 如果 Breadcrumb 组件不存在，请创建或注释此行 -->
      <!-- <Breadcrumb /> -->
    </div>

    <!-- 页面标题 -->
    <div class="mb-4">
      <h2 class="text-xl font-medium">客户档案</h2>
      <p class="mt-1 text-sm text-gray-500">服务中 - 所有当前服务的客户都在这里哦</p>
    </div>

    <!-- 标签页切换 -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex gap-4">
        <button
          type="button"
          class="px-4 py-2 rounded-full text-sm transition-all duration-300 transform hover:scale-105 active:scale-95 status-tab-btn"
          :class="[
            activeTab === 'active' 
              ? 'bg-primary text-white animate-pulse-gentle shadow-lg' 
              : 'text-gray-600 hover:bg-gray-100 hover:shadow-md'
          ]"
          @click="handleTabSwitch('active')"
        >
          服务中
        </button>
        <button
          type="button"
          class="px-4 py-2 rounded-full text-sm transition-all duration-300 transform hover:scale-105 active:scale-95 status-tab-btn"
          :class="[
            activeTab === 'archived' 
              ? 'bg-primary text-white animate-pulse-gentle shadow-lg' 
              : 'text-gray-600 hover:bg-gray-100 hover:shadow-md'
          ]"
          @click="handleTabSwitch('archived')"
        >
          已归档
        </button>
      </div>
      <button
        type="button"
        class="px-4 py-2 rounded-full text-sm transition-colors bg-primary text-white hover:bg-primary-dark mr-[100px]"
        @click="handleCreateClient"
      >
        + 创建客户
      </button>
    </div>

    <!-- 搜索和操作区 -->
    <div class="flex justify-between items-center mb-4">
      <div class="flex gap-3">
        <el-input
          v-model="searchQuery"
          placeholder="搜索客户姓名"
          class="w-60"
          clearable
          @input="handleSearch"
          @clear="handleSearch"
          :suffix-icon="searching ? 'Loading' : ''"
        >
          <template #prefix>
            <span class="material-icons-outlined text-gray-400">search</span>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 客户列表 -->
    <div class="bg-white rounded-lg">
      <el-table
        :data="clientList"
        style="width: 100%"
        :header-cell-style="{
          background: '#fff',
          color: '#909399',
          fontWeight: 400,
          fontSize: '13px',
          height: '48px'
        }"
        :cell-style="{
          fontSize: '14px',
          color: '#606266'
        }"
        @row-click="handleView"
        class="client-table"
      >
        <el-table-column label="客户" min-width="240">
          <template #default="{ row }">
            <div class="flex items-center">
              <div class="w-8 h-8 rounded-full bg-primary bg-opacity-10 flex items-center justify-center text-primary mr-3">
                {{ row.name?.charAt(0)?.toUpperCase() || '?' }}
              </div>
              <div>
                <div class="font-medium">{{ row.name }}</div>
                <div class="text-sm text-gray-500">{{ row.phone || '暂无联系方式' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="location" label="居住城市" min-width="120" />
        <el-table-column prop="gender" label="性别" width="80">
          <template #default="{ row }">
            {{ genderMap[row.gender] }}
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" min-width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="updatedAt" label="修改时间" min-width="180">
          <template #default="{ row }">
            {{ formatRelativeTime(row.updatedAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <div class="flex items-center space-x-4" @click.stop>
              <el-dropdown trigger="click">
                <el-button
                  class="icon-btn bg-gray-100 hover:bg-gray-200 transition-colors !border-0"
                >
                  <span class="material-icons-outlined text-gray-500">more_horiz</span>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleView(row)" class="view-item">
                      <i class="el-icon-view mr-1"></i>查看
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleArchive(row)" class="archive-item">
                      <i class="el-icon-folder mr-1"></i>{{ row.is_archived ? '取消归档' : '归档' }}
                    </el-dropdown-item>
                    <el-dropdown-item divided @click="handleDelete(row)" class="delete-item">
                      <i class="el-icon-delete mr-1"></i>删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex justify-end py-0 px-6 -mt-2 pb-4">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="total"
          layout="prev, pager, next"
          @current-change="handleCurrentChange"
          background
          class="pagination-custom"
        />
      </div>
    </div>

    <!-- 创建客户弹窗 -->
    <el-dialog
      v-model="createDialogVisible"
      title="创建客户"
      width="520px"
      destroy-on-close
      :close-on-click-modal="false"
    >
      <div class="px-2">
        <el-form ref="createFormRef" :model="createForm" :rules="createFormRules" label-width="80px">
          <el-form-item label="昵称" prop="nickname" :required="!createForm.enableAI">
            <el-input 
              v-model="createForm.nickname" 
              :placeholder="createForm.enableAI ? '智能建档将自动识别姓名（可选）' : '请输入客户昵称'" 
            />
          </el-form-item>
          <el-form-item label="智能建档">
            <el-switch v-model="createForm.enableAI" />
          </el-form-item>

          <!-- 文件上传区域 -->
          <div v-if="createForm.enableAI" class="mt-4">
            <el-upload
              class="upload-demo"
              drag
              action="#"
              :auto-upload="false"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :multiple="false"
              accept=".pdf,.docx,.txt"
              :show-file-list="false"
            >
              <div class="flex flex-col items-center justify-center py-8">
                <span class="material-icons-outlined text-4xl text-gray-300">cloud_upload</span>
                <div class="text-gray-600 mt-2">拖拽简历文件到此处或点击上传</div>
                <div class="text-xs text-gray-400 mt-1">
                  支持 PDF、DOCX、TXT 格式，文件大小不超过 10MB
                </div>
              </div>
            </el-upload>

            <!-- 已选择的文件预览 -->
            <div v-if="uploadedFiles.length > 0" class="mt-4 p-4 bg-blue-50 rounded-md">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <span class="material-icons-outlined text-blue-600 mr-2">description</span>
                  <div>
                    <div class="text-sm font-medium text-blue-800">{{ uploadedFiles[0].name }}</div>
                    <div class="text-xs text-blue-600">{{ formatFileSize(uploadedFiles[0].size) }}</div>
                  </div>
                </div>
                <el-button
                  type="text"
                  @click="handleFileRemove"
                  class="text-red-500 hover:text-red-700"
                >
                  <span class="material-icons-outlined text-sm">close</span>
                </el-button>
              </div>
              <div class="text-xs text-blue-600 mt-2">
                <span class="material-icons-outlined text-xs mr-1">info</span>
                点击"确认创建"开始智能建档
              </div>
            </div>
          </div>
        </el-form>
      </div>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="createDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmCreate" :loading="creating">
            确认创建
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="确认删除"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="p-4">
        <p class="text-gray-600">确定要删除该客户档案吗？此操作不可恢复。</p>
      </div>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="handleConfirmDelete" :loading="deleting">
            确认删除
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 归档确认对话框 -->
    <el-dialog
      v-model="archiveDialogVisible"
      :title="selectedClient?.is_archived ? '确认取消归档' : '确认归档'"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="p-4">
        <p class="text-gray-600">{{ selectedClient?.is_archived ? '确定要取消归档该客户吗？' : '确定要归档该客户吗？' }}</p>
      </div>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="archiveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmArchive" :loading="archiving">
            确认{{ selectedClient?.is_archived ? '取消归档' : '归档' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 智能建档处理状态弹窗 -->
    <el-dialog
      v-model="processingDialogVisible"
      title="智能建档处理中"
      width="500px"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="processing-dialog"
    >
      <div class="py-4">
        <div class="flex flex-col items-center justify-center">
          <!-- 状态图标 -->
          <div class="w-16 h-16 rounded-full flex items-center justify-center mb-4"
               :class="[
                 processingStatus === 'uploading' || processingStatus === 'processing' 
                   ? 'bg-primary text-white' 
                   : processingStatus === 'success'
                   ? 'bg-green-500 text-white'
                   : processingStatus === 'error'
                   ? 'bg-red-500 text-white'
                   : 'bg-primary bg-opacity-10 text-primary'
               ]">
            <span v-if="processingStatus === 'uploading'" class="material-icons-outlined text-2xl animate-spin">sync</span>
            <span v-else-if="processingStatus === 'processing'" class="material-icons-outlined text-2xl animate-spin">auto_awesome</span>
            <span v-else-if="processingStatus === 'success'" class="material-icons-outlined text-2xl">check_circle</span>
            <span v-else-if="processingStatus === 'error'" class="material-icons-outlined text-2xl">error</span>
          </div>

          <!-- 状态标题 -->
          <h3 class="text-lg font-medium text-gray-800 mb-2">{{ getStatusTitle }}</h3>

          <!-- 状态描述 -->
          <p class="text-gray-600 text-center mb-4">{{ getStatusDescription }}</p>

          <!-- 进度指示器 -->
          <div v-if="['uploading', 'processing'].includes(processingStatus)" class="w-full max-w-xs mb-4">
            <el-progress
              :percentage="processingProgress"
              :stroke-width="8"
              :show-text="false"
              color="#6366f1"
            ></el-progress>
            <div class="text-sm text-gray-500 text-center mt-2">{{ processingProgressText }}</div>
          </div>

        </div>
      </div>

      <!-- 按钮区域 -->
      <template #footer>
        <div class="flex justify-end">
          <el-button
            v-if="processingStatus === 'error'"
            @click="processingDialogVisible = false"
          >
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/// <reference types="vite/client" />
import { ref, onMounted, watch, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'
// 导入API函数
import {
  getClientList,
  addClient,
  deleteClient,
  toggleClientArchive,
  extractBackgroundFromFile
} from '@/api/client'
// 如果 Breadcrumb 组件不存在，请创建或注释此行
// import Breadcrumb from '@/components/common/Breadcrumb.vue'

// 配置 dayjs 支持时区转换和相对时间显示
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(relativeTime)
dayjs.locale('zh-cn') // 使用中文

// 设置默认时区为北京时间
dayjs.tz.setDefault('Asia/Shanghai')

// 路由实例
const router = useRouter()

// 客户数据类型定义
interface Client {
  id_hashed: string
  name: string
  location: string
  gender: 'male' | 'female' | 'unknown'
  serviceType: 'undergraduate' | 'master'
  is_archived: boolean
  createdAt: string
  updatedAt: string
  phone?: string
  email?: string
}

// 解析后的客户信息类型
interface ParsedClientInfo {
  name?: string
  gender?: string
  dob?: string
  phone?: string
  email?: string
  location?: string
  education?: Array<{
    school: string
    major: string
    period: string
    degree?: string
    gpa?: string
  }>
  academic?: Array<{
    title?: string
    institution?: string
    date?: string
    description?: string
  }>
  work?: Array<{
    company?: string
    position?: string
    period?: string
    description?: string
  }>
  activities?: Array<{
    name?: string
    role?: string
    period?: string
    description?: string
  }>
  skills?: Array<{
    languageproficiency?: string
    [key: string]: any
  }>
  awards?: Array<{
    name?: string
    level?: string
    date?: string
    description?: string
  }>
}

// 状态定义
const activeTab = ref('active')
const searchQuery = ref('')
const filterService = ref('')
const currentPage = ref(1)
const pageSize = ref(15) // 固定每页显示15条
const total = ref(0)
const clientList = ref<Client[]>([])
const createDialogVisible = ref(false)
const deleteDialogVisible = ref(false)
const archiveDialogVisible = ref(false) // 新增归档对话框状态
const creating = ref(false)
const deleting = ref(false)
const archiving = ref(false) // 新增归档中状态
const selectedClient = ref<Client | null>(null)
const processingDialogVisible = ref(false)
const processingStatus = ref('uploading') // 'uploading', 'processing', 'success', 'error'
const processingProgress = ref(0)
const createdClientId = ref<string | null>(null)
const searching = ref(false) // 添加搜索状态指示变量

// 表单数据
const createForm = ref({
  nickname: '',
  enableAI: false
})

// 表单验证规则
const createFormRules = {
  nickname: [
    { 
      required: true, 
      message: '请输入客户昵称',
      trigger: []  // 空数组表示不自动触发验证
    },
    { 
      min: 1, 
      max: 50, 
      message: '昵称长度应在1-50个字符之间',
      trigger: []  // 空数组表示不自动触发验证
    }
  ]
}

// 表单引用
const createFormRef = ref()

// 文件上传相关状态
const uploadedFiles = ref<File[]>([])
const parsedClientInfo = ref<ParsedClientInfo | null>(null)
const uploading = ref(false)
const processing = ref(false)

// 映射表
const genderMap = {
  male: '男',
  female: '女',
  unknown: '未知'
}

const serviceTypeMap = {
  undergraduate: '本科申请',
  master: '硕士申请'
}

// 日期格式化（创建时间 YYYY-MM-DD HH:mm）
const formatDateTime = (date: string) => {
  if (!date) return '暂无记录'
  // 由于后端已经存储为北京时间，直接格式化即可，不需要时区转换
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 相对时间格式化（修改时间 几天前、几小时前等）
const formatRelativeTime = (date: string) => {
  if (!date) return '暂无更新'

  // 由于后端已经存储为北京时间，直接格式化即可，不需要时区转换
  const updateTime = dayjs(date)
  const now = dayjs()

  // 计算时间差（分钟）
  const diffMinutes = now.diff(updateTime, 'minute')

  // 根据时间差显示不同格式
  if (diffMinutes < 1) {
    return '刚刚'
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`
  } else if (now.diff(updateTime, 'hour') < 24) {
    return `${now.diff(updateTime, 'hour')}小时前`
  } else if (now.diff(updateTime, 'day') < 30) {
    return `${now.diff(updateTime, 'day')}天前`
  } else {
    return formatDateTime(date)
  }
}

// 文件移除处理
const handleFileRemove = () => {
  uploadedFiles.value = []
  parsedClientInfo.value = null
}

// 文件大小格式化
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 智能背景提取函数
const handleIntelligentExtraction = async (file: File) => {
  try {
    // 显示处理状态弹窗
    processingDialogVisible.value = true
    processingStatus.value = 'uploading'
    processingProgress.value = 20
    
    // 关闭创建对话框
    createDialogVisible.value = false
    
    // 更新状态为AI处理中
    processingStatus.value = 'processing'
    processingProgress.value = 40
    
    // 模拟进度增长
    const progressInterval = setInterval(() => {
      if (processingProgress.value < 90) {
        processingProgress.value += 5
      }
    }, 2000)
    
    try {
      // 调用新的背景提取API
      const result = await extractBackgroundFromFile(file)
      
      // 清除进度定时器
      clearInterval(progressInterval)
      processingProgress.value = 100
      
      // 处理成功
      processingStatus.value = 'success'
      
      // 检查返回的client_id
      if (result.client_id) {
        createdClientId.value = result.client_id
        
        // 刷新客户列表
        await fetchClients()
        
        ElMessage.success('智能建档成功！')
      } else {
        throw new Error('未返回客户ID')
      }
      
    } catch (error: any) {
      clearInterval(progressInterval)
      console.error('智能建档失败:', error)
      
      processingStatus.value = 'error'
      
      // 错误处理
      let errorMessage = '智能建档失败，请稍后重试'
      
      if (error.response) {
        const { status, data } = error.response
        switch (status) {
          case 400:
            errorMessage = '文件格式不支持或内容无法解析'
            break
          case 413:
            errorMessage = '文件太大，请上传小于10MB的文件'
            break
          case 500:
            errorMessage = 'AI解析失败，请检查文件内容或稍后重试'
            break
          default:
            errorMessage = data.detail || errorMessage
        }
      } else if (error.request) {
        errorMessage = '网络连接失败，请检查网络状态'
      }
      
      ElMessage.error(errorMessage)
    }
  } catch (error) {
    console.error('Unexpected error:', error)
    processingStatus.value = 'error'
    ElMessage.error('处理失败，请稍后重试')
  }
}



// 创建一个防抖函数
const debounce = (fn: Function, delay: number) => {
  let timer: number | null = null
  return function(...args: any[]) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn(...args)
    }, delay) as unknown as number
  }
}

// 使用防抖函数包装搜索处理逻辑
const debouncedSearch = debounce(() => {
  currentPage.value = 1 // 搜索时重置为第一页
  fetchClients()
}, 500) // 500ms的延迟

const handleSearch = () => {
  // 设置搜索状态
  searching.value = true
  // 调用防抖处理函数
  debouncedSearch()
}

const handleFilter = () => {
  currentPage.value = 1 // 筛选时重置为第一页
  fetchClients()
}

const handleCurrentChange = async (val: number) => {
  currentPage.value = val
  await fetchClients() // 确保等待数据加载完成
}

const handleCreateClient = () => {
  // 重置表单和状态
  createForm.value.nickname = ''
  createForm.value.enableAI = false
  uploadedFiles.value = []
  parsedClientInfo.value = null
  createDialogVisible.value = true
}

const handleFileChange = (file: any, fileList: any[]) => {
  // 只保存文件，不立即开始处理
  if (file && file.raw) {
    // 文件格式验证
    const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain']
    if (!allowedTypes.includes(file.raw.type)) {
      ElMessage.error('请上传 PDF、DOCX 或 TXT 格式的文件')
      return
    }
    
    // 文件大小验证（10MB）
    const maxSize = 10 * 1024 * 1024
    if (file.raw.size > maxSize) {
      ElMessage.error('文件大小不能超过 10MB')
      return
    }
    
    uploadedFiles.value = [file.raw]
    ElMessage.success('文件已选择，点击确认创建开始智能建档')
  }
}

const handleConfirmCreate = async () => {
  // 如果没有启用智能建档，需要验证表单
  if (!createForm.value.enableAI) {
    try {
      await createFormRef.value.validate()
    } catch (error) {
      // 表单验证失败，会自动显示中文错误信息
      return
    }
  }

  // 如果启用智能建档，但没有上传文件，提示错误
  if (createForm.value.enableAI && uploadedFiles.value.length === 0) {
    ElMessage.warning('请上传至少一个文件用于智能建档');
    return;
  }

  creating.value = true;
  try {
    if (createForm.value.enableAI && uploadedFiles.value.length > 0) {
      // 智能建档模式 - 现在开始处理文件
      await handleIntelligentExtraction(uploadedFiles.value[0]);
      return;
    } else {
      // 手动创建客户模式
      const clientData = {
        name: createForm.value.nickname || '未命名客户',
        gender: 'unknown',
        location: '未知',
        service_type: 'undergraduate'
      };

      try {
        // 调用API创建客户
        const response = await addClient(clientData);

        // 添加到客户列表
        const newClient = {
          id_hashed: response.id_hashed,
          name: response.name,
          location: response.location || '未知',
          gender: response.gender === 'male' ? 'male' : (response.gender === 'female' ? 'female' : 'unknown'),
          serviceType: response.service_type || 'undergraduate',
          phone: response.phone,
          email: response.email,
          is_archived: response.is_archived,
          createdAt: response.created_at,
          updatedAt: response.updated_at || response.created_at
        };

        clientList.value.unshift(newClient as Client);
        total.value += 1;

        ElMessage.success('客户创建成功');
        createDialogVisible.value = false;

        // 重置表单
        createForm.value.nickname = '';
        createForm.value.enableAI = false;
        uploadedFiles.value = [];
        parsedClientInfo.value = null;
      } catch (error) {
        console.error('创建客户失败:', error);
        ElMessage.error('创建客户失败');
        throw error;
      }
    }
  } catch (error) {
    console.error(error);
  } finally {
    creating.value = false;
  }
}

const handleView = (row: any) => {
  router.push(`/clients/${row.id_hashed}`)
}

const handleArchive = (row: any) => {
  selectedClient.value = row
  archiveDialogVisible.value = true
}

const handleConfirmArchive = async () => {
  if (!selectedClient.value) return
  archiving.value = true
  try {
    // 调用API切换客户归档状态
    await toggleClientArchive(selectedClient.value.id_hashed)

    // 重新加载客户列表
    fetchClients()
    archiveDialogVisible.value = false
  } catch (error) {
    console.error('操作失败:', error)
  } finally {
    archiving.value = false
  }
}

const handleDelete = (row: any) => {
  selectedClient.value = row
  deleteDialogVisible.value = true
}

const handleConfirmDelete = async () => {
  if (!selectedClient.value) return
  deleting.value = true
  try {
    // 调用API删除客户
    await deleteClient(selectedClient.value.id_hashed)

    // 从列表中移除该客户
    clientList.value = clientList.value.filter(client => client.id_hashed !== selectedClient.value?.id_hashed)
    total.value = Math.max(0, total.value - 1)

    // ElMessage.success('删除成功')
    deleteDialogVisible.value = false
  } catch (error) {
    console.error('删除客户失败:', error)

    // ElMessage.error('删除失败')
  } finally {
    deleting.value = false
  }
}

const fetchClients = async () => {
  try {
    // 设置搜索状态
    searching.value = true
    
    // 固定每页显示的条数为15
    const fixedPageSize = 15;

    // 构建查询参数
    const params: {
      page: number;
      per_page: number;
      search?: string;
      service_type?: string;
      is_archived: boolean;
      sort?: string;
    } = {
      page: currentPage.value,
      per_page: fixedPageSize,
      sort: '-updated_at', // 使用负号表示降序，确保最新更新的在最前面
      is_archived: activeTab.value === 'archived' // 根据当前标签页设置是否获取已归档客户
    }

    // 如果有搜索关键词，添加到参数中
    if (searchQuery.value) {
      params.search = searchQuery.value
    }

    // 如果有服务类型筛选，添加到参数中
    if (filterService.value) {
      params.service_type = filterService.value
    }

    try {
      // 调用API获取客户列表
      const response = await getClientList(params)

      // 更新客户列表和总数
      if (Array.isArray(response)) {
        // 如果是数组，说明是所有数据
        const sortedData = [...response].sort((a, b) => {
          const dateA = dayjs.utc(a.updated_at || a.created_at)
          const dateB = dayjs.utc(b.updated_at || b.created_at)
          return dateB.valueOf() - dateA.valueOf() // 降序排序，最新的在前面
        })

        total.value = sortedData.length
        const start = (currentPage.value - 1) * fixedPageSize
        clientList.value = sortedData.slice(start, start + fixedPageSize).map((client: any) => ({
          id_hashed: client.id_hashed,
          name: client.name,
          location: client.location || '未知',
          gender: client.gender === 'male' ? 'male' : (client.gender === 'female' ? 'female' : 'unknown'),
          serviceType: client.service_type || 'undergraduate',
          phone: client.phone,
          email: client.email,
          is_archived: client.is_archived,
          createdAt: client.created_at,
          updatedAt: client.updated_at || client.created_at
        }))
      } else if (response.items && Array.isArray(response.items)) {
        // 如果是分页对象
        total.value = response.total || response.items.length

        // 确保数据按更新时间降序排序
        const sortedItems = [...response.items].sort((a, b) => {
          const dateA = dayjs.utc(a.updated_at || a.created_at)
          const dateB = dayjs.utc(b.updated_at || b.created_at)
          return dateB.valueOf() - dateA.valueOf() // 降序排序，最新的在前面
        })

        clientList.value = sortedItems.map((client: any) => ({
          id_hashed: client.id_hashed,
          name: client.name,
          location: client.location || '未知',
          gender: client.gender === 'male' ? 'male' : (client.gender === 'female' ? 'female' : 'unknown'),
          serviceType: client.service_type || 'undergraduate',
          phone: client.phone,
          email: client.email,
          is_archived: client.is_archived,
          createdAt: client.created_at,
          updatedAt: client.updated_at || client.created_at
        }))
      } else {
        console.error('API返回的数据格式不正确:', response)
        clientList.value = []
        total.value = 0
      }

      // 根据当前标签页筛选数据
      if (activeTab.value === 'archived') {
        // 注意：后端API需要支持归档状态筛选
        // 可以通过添加参数 params.archived = true 实现
      }
    } catch (error) {
      console.error('API调用失败:', error)

      // 显示错误并清空列表
      clientList.value = []
      total.value = 0
      throw error
    } finally {
      // 重置搜索状态
      searching.value = false
    }
  } catch (error) {
    // ElMessage.error('获取客户列表失败')
    console.error(error)
    // 确保错误时也重置搜索状态
    searching.value = false
  }
}

// 监听标签页变化
watch(() => activeTab.value, () => {
  currentPage.value = 1 // 切换标签时重置页码
  fetchClients()
})

// 生命周期钩子
onMounted(() => {
  fetchClients()
})

// 监听分页变化
watch(currentPage, async (newPage) => {
  if (newPage > 0) {
    await fetchClients()
  }
})

// 监听搜索和筛选条件变化
watch([searchQuery, filterService], () => {
  currentPage.value = 1 // 重置到第一页
  fetchClients()
})

// 状态文字计算属性
const getStatusTitle = computed(() => {
  switch(processingStatus.value) {
    case 'uploading': return '正在上传文件';
    case 'processing': return '智能分析中';
    case 'success': return '智能建档成功';
    case 'error': return '处理出错';
    default: return '处理中';
  }
})

const getStatusDescription = computed(() => {
  switch(processingStatus.value) {
    case 'uploading': return '正在上传您提供的文件，请稍候...';
    case 'processing': return '正在智能分析文件内容，创建客户档案...';
    case 'success': return '客户档案已成功创建，正在自动跳转到客户详情页...';
    case 'error': return '处理过程中遇到问题，请稍后重试。';
    default: return '正在处理您的请求...';
  }
})

const processingProgressText = computed(() => {
  if (processingStatus.value === 'uploading') {
    return `上传进度: ${processingProgress.value}%`;
  } else if (processingStatus.value === 'processing') {
    return `处理进度: ${processingProgress.value}%`;
  }
  return '';
})



// 当处理状态变更时自动处理
watch(processingStatus, async (newStatus) => {
  if (newStatus === 'success' && createdClientId.value) {
    try {
      // 刷新客户列表以确保显示最新数据
      await fetchClients()

    // 等待0.3秒显示成功状态后自动跳转
    setTimeout(() => {
      processingDialogVisible.value = false;
      router.push(`/clients/${createdClientId.value}`);
    }, 300);
    } catch (error) {
      console.error('刷新客户列表失败:', error);
    }
  }
});

// 处理标签页切换动画
const handleTabSwitch = (tab: string) => {
  // 添加点击动画效果
  const clickedButton = document.activeElement as HTMLElement
  if (clickedButton) {
    clickedButton.classList.add('animate-bounce')
    setTimeout(() => {
      clickedButton.classList.remove('animate-bounce')
    }, 600)
  }
  
  // 切换标签页
  activeTab.value = tab
}
</script>

<style lang="postcss" scoped>
.el-dropdown-link {
  @apply flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 transition-colors;
}

:deep(.el-table) {
  --el-table-border-color: transparent;
  --el-table-header-bg-color: #fff;
  --el-table-row-hover-bg-color: #f5f7fa;
}

:deep(.el-table__cell) {
  @apply border-b border-gray-100;
}

:deep(.el-table__header) th {
  @apply border-b border-gray-100;
}

:deep(.el-button--primary) {
  --el-button-bg-color: #4F46E5 !important; /* 紫色 */
  --el-button-border-color: #4F46E5 !important;
  --el-button-hover-bg-color: #4338CA !important; /* 深紫色 */
  --el-button-hover-border-color: #4338CA !important;
  --el-button-active-bg-color: #3730A3 !important; /* 更深的紫色 */
  --el-button-active-border-color: #3730A3 !important;
  --el-button-text-color: #FFFFFF !important;
}

/* 修改Element Plus链接按钮样式 */
:deep(.el-button--primary.is-link) {
  --el-button-text-color: #4F46E5 !important; /* 紫色 */
  --el-button-hover-text-color: #4338CA !important; /* 深紫色 */
  --el-button-active-text-color: #3730A3 !important; /* 更深的紫色 */
  --el-button-bg-color: transparent !important;
  --el-button-border-color: transparent !important;
  --el-button-hover-bg-color: transparent !important;
  --el-button-hover-border-color: transparent !important;
  --el-button-active-bg-color: transparent !important;
  --el-button-active-border-color: transparent !important;
}

/* 覆盖所有 a 标签颜色为紫色 */
:deep(a),
:deep(a:visited),
:deep(a:active) {
  color: #4F46E5 !important; /* 紫色 */
  text-decoration: none !important;
}

:deep(a:hover) {
  color: #4338CA !important; /* 深紫色 */
  text-decoration: underline !important;
}

/* 覆盖基于Tailwind的链接文本样式 */
:deep(.text-blue-600),
:deep(.text-blue-500) {
  color: #4F46E5 !important; /* 紫色 */
}

:deep(.hover\:text-blue-700:hover),
:deep(.hover\:text-blue-600:hover) {
  color: #4338CA !important; /* 深紫色 */
}

/* 确保表格中的操作链接不会显示为蓝色 */
:deep(.el-table .cell a) {
  color: #4F46E5 !important; /* 紫色 */
}

:deep(.el-table .cell a:hover) {
  color: #4338CA !important; /* 深紫色 */
}

/* 覆盖分页组件可能的蓝色为紫色 */
:deep(.el-pagination .el-pager li.is-active) {
  --el-color-primary: #4F46E5 !important; /* 紫色 */
  color: #FFFFFF !important;
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
}

:deep(.el-pagination .el-pager li:hover:not(.is-active)) {
  color: #4F46E5 !important; /* 紫色 */
}

:deep(.el-pagination .btn-prev:hover),
:deep(.el-pagination .btn-next:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

:deep(.el-pagination .el-pagination__jump .el-pagination__editor) {
  border-color: #4F46E5 !important; /* 紫色 */
}

/* 修改表格选中行高亮颜色 */
:deep(.el-table tr.current-row) {
  background-color: rgba(79, 70, 229, 0.1) !important; /* 浅紫色 */
}

/* 修改所有element-plus的颜色初始变量 */
:deep(:root) {
  --el-color-primary: #4F46E5 !important; /* 紫色 */
  --el-color-primary-light-3: #6366F1 !important;
  --el-color-primary-light-5: #818CF8 !important;
  --el-color-primary-light-7: #C7D2FE !important;
  --el-color-primary-light-9: #EEF2FF !important;
  --el-color-primary-dark-2: #4338CA !important; /* 深紫色 */
}

/* 文件分析状态提示背景修改 */
.uploading, .processing {
  background-color: rgba(79, 70, 229, 0.1) !important;
}

.uploading .material-icons-outlined, 
.processing .material-icons-outlined, 
.text-indigo-600 {
  color: #4F46E5 !important;
}

.bg-indigo-50 {
  background-color: rgba(79, 70, 229, 0.1) !important;
}

/* 确保所有蓝色相关类名都使用紫色 */
.text-indigo-700,
.text-blue-700 {
  color: #4338CA !important;
}

/* 确保所有蓝色背景都使用紫色 */
.bg-blue-50,
.bg-indigo-50 {
  background-color: rgba(79, 70, 229, 0.1) !important;
}

.bg-primary {
  background-color: #4F46E5 !important;
}

.text-primary {
  color: #4F46E5 !important;
}

/* 进度条颜色 */
:deep(.el-progress-bar__inner) {
  background-color: #4F46E5 !important;
}

/* 删除确认对话框样式修改 - 移除阴影 */
:deep(.el-dialog) {
  box-shadow: none !important;
  border: 1px solid #e4e7ed;
}

:deep(.el-dialog__header),
:deep(.el-dialog__footer) {
  box-shadow: none !important;
  border-top: none;
  background-color: #fff;
}

:deep(.el-dialog__body) {
  background-color: #fff;
}

/* 增强分页组件的可见度 */
.pagination-custom {
  @apply mt-4;
}

:deep(.pagination-custom .el-pagination__total) {
  @apply text-gray-600;
}

:deep(.pagination-custom .el-pager li) {
  @apply border border-gray-200 min-w-[32px] h-8 leading-8 mx-1 transition-all duration-300 ease-in-out;
}

:deep(.pagination-custom .el-pager li.is-active) {
  @apply bg-primary text-white border-primary transform scale-110;
}

:deep(.pagination-custom .el-pager li:hover:not(.is-active)) {
  @apply bg-gray-50 border-gray-300 transform scale-105;
}

:deep(.pagination-custom .btn-prev),
:deep(.pagination-custom .btn-next) {
  @apply border border-gray-200 min-w-[32px] h-8 leading-8 mx-1 transition-all duration-300 ease-in-out;
}

:deep(.pagination-custom .btn-prev:hover),
:deep(.pagination-custom .btn-next:hover) {
  @apply bg-gray-50 border-gray-300 transform scale-105;
}

:deep(.pagination-custom .el-pagination button:disabled) {
  @apply bg-gray-100 cursor-not-allowed transform scale-100;
}

/* 添加禁用状态样式 */
:deep(.pagination-custom .el-pager li.is-disabled) {
  @apply opacity-50 cursor-not-allowed transform scale-100;
}

/* 确保激活页码的动画更平滑 */
:deep(.pagination-custom .el-pager) {
  @apply flex items-center;
}

/* 优化数字按钮的外观 */
:deep(.pagination-custom .el-pager li.number) {
  @apply font-medium;
}

/* 增强表格行的可点击性 */
.client-table {
  @apply cursor-pointer;
}

:deep(.client-table .el-table__row) {
  transition: background-color 0.2s ease;
}

:deep(.client-table .el-table__row:hover) {
  background-color: #f5f7fa !important;
}

:deep(.el-button.el-button--danger.is-link) {
  --el-button-text-color: #f56c6c;
}

:deep(.el-dropdown-menu__item) {
  @apply text-gray-600;
  color: #606266 !important; /* 强制覆盖Element默认颜色 */
}

:deep(.el-dropdown-menu__item i) {
  @apply text-gray-400;
  color: #909399 !important; /* 强制覆盖Element默认颜色 */
}

/* 为特定菜单项添加样式 */
:deep(.view-item),
:deep(.view-item i) {
  color: #606266 !important; /* 灰色文本 */
}

:deep(.archive-item),
:deep(.archive-item i) {
  color: #606266 !important; /* 灰色文本 */
}

:deep(.delete-item),
:deep(.delete-item i) {
  color: #606266 !important; /* 灰色文本 */
}

:deep(.delete-item:hover),
:deep(.delete-item:hover i) {
  color: #f56c6c !important; /* 红色 */
}

/* 覆盖默认的蓝色高亮 */
:deep(.el-dropdown-menu__item:focus),
:deep(.el-dropdown-menu__item:not(.is-disabled):focus) {
  background-color: transparent !important;
  color: #606266 !important;
}

/* 设置鼠标悬停状态为紫色 */
:deep(.el-dropdown-menu__item:hover),
:deep(.el-dropdown-menu__item:not(.is-disabled):hover) {
  @apply text-primary bg-primary/5;
  color: #4F46E5 !important; /* 紫色 */
  background-color: rgba(79, 70, 229, 0.05) !important;
}

/* 设置危险按钮的样式 */
:deep(.el-dropdown-menu__item.danger:hover) {
  @apply text-danger bg-danger/5;
  color: #f56c6c !important; /* 红色 */
  background-color: rgba(245, 108, 108, 0.05) !important;
}

/* 修改分隔线样式 */
:deep(.el-dropdown-menu__item--divided) {
  margin: 0 !important;
  padding: 0 !important;
  height: 1px !important;
  background-color: #f0f0f0 !important;
  border-top: none !important;
}

/* 去掉分隔线前的空白间距 */
:deep(.el-dropdown-menu__item--divided::before) {
  height: 0 !important;
  margin: 0 !important;
  display: none !important;
}

:deep(.el-button.el-button--default) {
  --el-button-bg-color: transparent;
  --el-button-border-color: transparent;
  --el-button-hover-bg-color: transparent;
  --el-button-hover-border-color: transparent;
  --el-button-active-bg-color: transparent;
  --el-button-active-border-color: transparent;
  --el-button-hover-text-color: #4F46E5 !important; /* 紫色 */
}

/* 确保所有按钮悬浮时的文字颜色都是紫色 */
:deep(.el-button:hover) {
  --el-button-text-color: #4F46E5 !important; /* 紫色 */
}

/* 特殊处理取消按钮和其他默认按钮 */
:deep(.el-button:not(.el-button--primary):not(.el-button--danger):hover) {
  color: #4F46E5 !important; /* 紫色 */
  border-color: #4F46E5 !important; /* 紫色边框 */
}

/* 标签页按钮动画样式 */
.status-tab-btn {
  position: relative;
  overflow: hidden;
}

.status-tab-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.status-tab-btn:active::before {
  left: 100%;
}

/* 轻柔的脉冲动画 */
@keyframes pulse-gentle {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.02);
  }
}

.animate-pulse-gentle {
  animation: pulse-gentle 2s infinite;
}

/* 弹跳动画 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.animate-bounce {
  animation: bounce 0.6s ease-out;
}

/* 按钮状态切换时的闪烁效果 */
@keyframes flash {
  0% {
    background-color: currentColor;
    opacity: 1;
  }
  50% {
    background-color: rgba(79, 70, 229, 0.8);
    opacity: 0.8;
  }
  100% {
    background-color: currentColor;
    opacity: 1;
  }
}

.status-tab-btn.animate-flash {
  animation: flash 0.3s ease-in-out;
}

/* 全局覆盖所有图标的悬浮颜色为紫色 */
:deep(svg:hover),
:deep(.material-icons-outlined:hover),
:deep(.material-icons:hover),
:deep(.el-icon:hover),
:deep(i:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖Element Plus内部图标的悬浮颜色 */
:deep(.el-input__prefix .el-input__icon:hover),
:deep(.el-input__suffix .el-input__icon:hover),
:deep(.el-select__caret:hover),
:deep(.el-pagination__right-wrapper .el-icon:hover),
:deep(.el-pagination__left-wrapper .el-icon:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖搜索框图标的悬浮颜色 */
:deep(.el-input .el-input__prefix .material-icons-outlined:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖下拉菜单操作按钮图标的悬浮颜色 */
:deep(.el-dropdown-link:hover .material-icons-outlined),
:deep(.icon-btn:hover .material-icons-outlined) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖对话框中的图标悬浮颜色 */
:deep(.el-dialog .material-icons-outlined:hover),
:deep(.el-dialog svg:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖关闭按钮SVG图标颜色 */
:deep(.el-dialog__headerbtn:hover),
:deep(.el-dialog__headerbtn:hover svg),
:deep(.el-dialog__close:hover),
:deep(.el-dialog__close:hover svg) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖Element Plus上传组件图标 */
:deep(.el-upload .material-icons-outlined:hover),
:deep(.el-upload-dragger:hover .material-icons-outlined) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖表格操作图标悬浮颜色 */
:deep(.el-table .el-icon:hover),
:deep(.el-table .material-icons-outlined:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖分页组件图标 */
:deep(.el-pagination .el-icon:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 确保按钮内的图标悬浮时也是紫色 */
:deep(.el-button:hover .el-icon),
:deep(.el-button:hover .material-icons-outlined),
:deep(.el-button:hover svg) {
  color: inherit !important; /* 继承按钮文字颜色 */
}

/* 特殊处理搜索输入框前缀图标 */
:deep(.el-input__prefix-inner:hover),
:deep(.el-input__prefix-inner:hover .material-icons-outlined) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 输入框焦点状态紫色覆盖 */
:deep(.el-input.is-focus .el-input__wrapper) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

:deep(.el-input:focus-within .el-input__wrapper) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

:deep(.el-textarea.is-focus .el-textarea__inner) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

:deep(.el-select.is-focus .el-input__wrapper) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

:deep(.el-date-editor.is-focus .el-input__wrapper) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

/* 覆盖Element Plus默认的focus颜色 */
:deep(.el-input__wrapper:focus),
:deep(.el-input__wrapper:focus-within),
:deep(.el-textarea__inner:focus) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

/* 下拉框焦点状态紫色覆盖 */
:deep(.el-select__wrapper.is-focused) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

:deep(.el-select__wrapper:focus) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

:deep(.el-select__wrapper:focus-within) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

:deep(.el-select:focus-within .el-select__wrapper) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

/* 下拉框激活状态 */
:deep(.el-select.is-focus .el-select__wrapper) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

/* 智能建档状态图标样式 - 确保旋转图标清晰可见 */
.processing-dialog .material-icons-outlined.animate-spin {
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 确保状态图标容器的背景色正确应用 */
.processing-dialog .bg-primary {
  background-color: #4F46E5 !important;
}

/* 为旋转动画添加更平滑的效果 */
@keyframes smooth-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: smooth-spin 1s linear infinite;
}
</style>