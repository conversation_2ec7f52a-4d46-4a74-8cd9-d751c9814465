<template>
  <div class="client-profile-page max-w-7xl mx-auto">
    <!-- 面包屑导航 -->
    <div class="mb-4">
      <Breadcrumb />
    </div>

    <!-- 顶部导航 -->
    <div class="flex items-center mb-5">
      <button class="text-gray-500 hover:text-gray-900 mr-3" @click="handleBackToList">
        <span class="material-icons-outlined text-lg">arrow_back</span>
      </button>
      <h2 class="text-xl font-medium text-gray-800">{{ isNewClient ? '新建客户' : clientData.name || '客户详情' }}</h2>
    </div>

    <!-- 标签导航 -->
    <div class="flex items-center mb-6 border-b border-gray-200">
      <div
        v-for="tab in tabs"
        :key="tab.key"
        class="px-4 py-2.5 cursor-pointer relative text-sm"
        :class="[
          activeTab === tab.key
            ? 'text-primary border-b-2 border-primary font-medium'
            : 'text-gray-600 hover:text-gray-900'
        ]"
        @click="handleTabClick(tab.key)"
      >
        <span class="material-icons-outlined mr-1 text-sm align-middle">{{ tab.icon }}</span>
        {{ tab.label }}
      </div>
    </div>

    <!-- 主要内容区 -->
    <div class="flex gap-5 pb-8">
      <!-- 左侧信息栏 -->
      <div class="w-72 flex-shrink-0 space-y-5">
        <!-- 个人信息卡片 -->
        <div class="pro-card">
          <div class="pro-card-header">
            <div class="pro-card-title">
              <span class="material-icons-outlined icon">person</span>
              个人信息
            </div>
            <div class="flex gap-2">
              <el-button type="primary" class="edit-btn icon-btn" link @click="handleEditPersonalInfo">
                <span class="material-icons-outlined text-sm">edit</span>
              </el-button>
            </div>
          </div>
          <div class="pro-card-body">
            <div class="flex flex-col items-center mb-4">
              <div class="w-16 h-16 rounded-full bg-primary bg-opacity-10 flex items-center justify-center text-xl text-primary mb-3">
                {{ clientData.name?.charAt(0) || '?' }}
              </div>
              <div class="text-center">
                <div class="text-base font-medium text-gray-800">{{ clientData.name }}</div>
              </div>
            </div>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-500">姓名:</span>
                <span class="text-gray-800">{{ clientData.name || '未提供' }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">性别:</span>
                <span class="text-gray-800">{{ clientData.gender === 'female' ? '女' : clientData.gender === 'male' ? '男' : '未提供' }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 联系方式卡片 -->
        <div class="pro-card">
          <div class="pro-card-header">
            <div class="pro-card-title">
              <span class="material-icons-outlined icon">contact_phone</span>
              联系方式
            </div>
            <el-button type="primary" class="edit-btn icon-btn" link @click="handleEditContact">
              <span class="material-icons-outlined text-sm">edit</span>
            </el-button>
          </div>
          <div class="pro-card-body">
            <div class="space-y-3 text-sm">
              <div class="flex items-center">
                <span class="material-icons-outlined text-gray-400 mr-2 text-sm">phone</span>
                <div>
                  <div class="text-xs text-gray-500">电话</div>
                  <div class="text-gray-800">{{ clientData.phone || '未提供' }}</div>
                </div>
              </div>
              <div class="flex items-center">
                <span class="material-icons-outlined text-gray-400 mr-2 text-sm">email</span>
                <div>
                  <div class="text-xs text-gray-500">邮箱</div>
                  <div class="text-gray-800">{{ clientData.email || '未提供' }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 地址卡片 -->
        <div class="pro-card">
          <div class="pro-card-header">
            <div class="pro-card-title">
              <span class="material-icons-outlined icon">location_on</span>
              地址
            </div>
            <el-button type="primary" class="edit-btn icon-btn" link @click="handleEditAddress">
              <span class="material-icons-outlined text-sm">edit</span>
            </el-button>
          </div>
          <div class="pro-card-body">
            <div class="space-y-3 text-sm">
              <div class="flex items-start">
                <span class="material-icons-outlined text-gray-400 mr-2 text-sm mt-1">home</span>
                <div>
                  <div class="text-xs text-gray-500">当前地址</div>
                  <div class="text-gray-800">{{ clientData.address || '未提供地址' }}</div>
                  <div class="text-xs text-gray-400 mt-1">{{ clientData.location || 'Shanghai, Shanghai, China' }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 语言成绩卡片 -->
        <div class="pro-card">
          <div class="pro-card-header">
            <div class="pro-card-title">
              <span class="material-icons-outlined icon">translate</span>
              语言成绩
            </div>
            <el-button type="primary" size="small" class="add-btn flex items-center" @click="handleEditLanguageScore">
              <span class="material-icons-outlined text-xs mr-1">add</span>
              添加
            </el-button>
          </div>
          <div class="pro-card-body">
            <div v-if="clientData.languageScores && clientData.languageScores.length > 0">
              <div v-for="(score, index) in clientData.languageScores" :key="index" class="mb-3 last:mb-0 pb-3 last:pb-0 border-b last:border-0 border-gray-100">
                <div class="flex justify-between items-center">
                  <div>
                    <div class="font-medium text-sm text-gray-800">{{ languageTypeMap[score.type] || score.type }}</div>
                    <div class="text-sm text-gray-700 mt-1">得分: {{ score.score }}</div>
                    <div class="text-sm text-gray-700">考试日期: {{ formatDate(score.date) }}</div>
                    <div v-if="score.validity" class="text-sm text-gray-700">有效期至: {{ formatDate(score.validity) }}</div>
                  </div>
                  <div class="flex space-x-1 items-center self-center">
                    <el-button type="primary" class="icon-btn" link @click="handleEditLanguageScore(index)">
                      <span class="material-icons-outlined text-xs">edit</span>
                    </el-button>
                    <el-button type="danger" class="icon-btn" link @click="handleDeleteLanguageScore(index)">
                      <span class="material-icons-outlined text-xs">delete</span>
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="text-center text-gray-500 text-xs py-2">
              暂无语言成绩，点击右上角添加
            </div>
          </div>
        </div>

        <!-- 功能管理卡片 -->
        <div v-if="activeTab === 'backgrounds'" class="pro-card">
          <div class="pro-card-header">
            <div class="pro-card-title">
              <span class="material-icons-outlined icon">settings</span>
              功能管理
            </div>
          </div>
          <div class="pro-card-body">
            <div class="space-y-3">
              <el-button type="primary" size="small" class="w-full add-btn flex items-center justify-center" @click="handleManageModules">
                <span class="material-icons-outlined text-xs mr-1">settings</span>
                管理模块
              </el-button>
            </div>
          </div>
        </div>


      </div>

      <!-- 右侧内容区 -->
      <div class="flex-grow min-h-[calc(100vh-15rem)]">
        <transition name="tab-fade" mode="out-in">
          <div v-if="activeTab === 'backgrounds'" class="space-y-5" key="backgrounds">

            <!-- 动态渲染模块 -->
            <div class="space-y-5">
              <template v-for="module in visibleBackgroundModules" :key="module.id">
                <!-- 教育经历 -->
                <div v-if="module.type === 'education'" class="pro-card">
                  <div class="pro-card-header">
                    <div class="pro-card-title">
                      <span class="material-icons-outlined icon">{{ module.icon }}</span>
                      {{ module.title }}
                    </div>
                    <el-button type="primary" size="small" class="add-btn flex items-center" @click="handleAddEducation">
                      <span class="material-icons-outlined text-xs mr-1">add</span>
                      添加
                    </el-button>
                  </div>
                  <div class="divide-y divide-gray-100">
                    <div v-for="(edu, index) in clientData.education" :key="index" class="p-4">
                      <div class="flex justify-between">
                        <div>
                          <div class="font-medium text-sm text-gray-900">{{ edu.school }}</div>
                          <div class="text-sm text-gray-700 mt-1">{{ edu.major }} | {{ degreeMap[edu.degree] || edu.degree }}</div>
                          <div class="text-sm text-gray-600">{{ formatDate(edu.start_date) }} - {{ formatDate(edu.end_date) }}</div>
                          <div class="text-sm text-gray-600">GPA: {{ edu.gpa }}</div>
                        </div>
                        <div class="flex space-x-1 items-center self-center">
                          <el-button type="primary" class="edit-btn icon-btn" link @click="handleEditEducation(index)">
                            <span class="material-icons-outlined text-xs">edit</span>
                          </el-button>
                          <el-button type="danger" class="delete-btn icon-btn" link @click="handleDeleteEducation(index)">
                            <span class="material-icons-outlined text-xs">delete</span>
                          </el-button>
                        </div>
                      </div>
                    </div>
                    <div v-if="clientData.education.length === 0" class="p-4 text-center text-gray-500 text-sm">
                      暂无教育经历，点击右上角添加
                    </div>
                  </div>
                </div>

                <!-- 学术经历 -->
                <div v-if="module.type === 'academic'" class="pro-card">
                  <div class="pro-card-header">
                    <div class="pro-card-title">
                      <span class="material-icons-outlined icon">{{ module.icon }}</span>
                      {{ module.title }}
                    </div>
                    <el-button type="primary" size="small" class="add-btn flex items-center" @click="handleAddAcademic">
                      <span class="material-icons-outlined text-xs mr-1">add</span>
                      添加
                    </el-button>
                  </div>
                  <div class="divide-y divide-gray-100">
                    <div v-for="(item, index) in clientData.academic" :key="index" class="p-4">
                      <div class="flex justify-between">
                        <div>
                          <div class="font-medium text-sm text-gray-900">{{ item.title }}</div>
                          <div class="text-sm text-gray-700 mt-1">学术类型: {{ item.type }}</div>
                          <div class="text-sm text-gray-600">{{ item.date }}</div>
                          <div class="text-sm text-gray-600 whitespace-pre-line">{{ item.description }}</div>
                        </div>
                        <div class="flex space-x-1 items-center self-center">
                          <el-button type="primary" class="icon-btn" link @click="handleEditAcademic(index)">
                            <span class="material-icons-outlined text-xs">edit</span>
                          </el-button>
                          <el-button type="danger" class="icon-btn" link @click="handleDeleteAcademic(index)">
                            <span class="material-icons-outlined text-xs">delete</span>
                          </el-button>
                        </div>
                      </div>
                    </div>
                    <div v-if="clientData.academic.length === 0" class="p-4 text-center text-gray-500 text-sm">
                      暂无学术经历，点击右上角添加
                    </div>
                  </div>
                </div>

                <!-- 工作经历 -->
                <div v-if="module.type === 'work'" class="pro-card">
                  <div class="pro-card-header">
                    <div class="pro-card-title">
                      <span class="material-icons-outlined icon">{{ module.icon }}</span>
                      {{ module.title }}
                    </div>
                    <el-button type="primary" size="small" class="add-btn flex items-center" @click="handleAddWork">
                      <span class="material-icons-outlined text-xs mr-1">add</span>
                      添加
                    </el-button>
                  </div>
                  <div class="divide-y divide-gray-100">
                    <div v-for="(work, index) in clientData.work" :key="index" class="p-4">
                      <div class="flex justify-between">
                        <div>
                          <div class="font-medium text-sm text-gray-900">{{ work.company }}</div>
                          <div class="text-sm text-gray-700 mt-1">{{ work.position }}</div>
                          <div class="text-sm text-gray-600">{{ formatDate(work.start_date) }} - {{ formatDate(work.end_date) }}</div>
                          <div class="text-sm text-gray-600 whitespace-pre-line">{{ work.description }}</div>
                        </div>
                        <div class="flex space-x-1 items-center self-center">
                          <el-button type="primary" class="icon-btn" link @click="handleEditWork(index)">
                            <span class="material-icons-outlined text-xs">edit</span>
                          </el-button>
                          <el-button type="danger" class="icon-btn" link @click="handleDeleteWork(index)">
                            <span class="material-icons-outlined text-xs">delete</span>
                          </el-button>
                        </div>
                      </div>
                    </div>
                    <div v-if="clientData.work.length === 0" class="p-4 text-center text-gray-500 text-sm">
                      暂无工作经历，点击右上角添加
                    </div>
                  </div>
                </div>

                <!-- 课外活动 -->
                <div v-if="module.type === 'activity'" class="pro-card">
                  <div class="pro-card-header">
                    <div class="pro-card-title">
                      <span class="material-icons-outlined icon">{{ module.icon }}</span>
                      {{ module.title }}
                    </div>
                    <el-button type="primary" size="small" class="add-btn flex items-center" @click="handleAddActivity">
                      <span class="material-icons-outlined text-xs mr-1">add</span>
                      添加
                    </el-button>
                  </div>
                  <div class="divide-y divide-gray-100">
                    <div v-for="(activity, index) in clientData.activities" :key="index" class="p-4">
                      <div class="flex justify-between">
                        <div>
                          <div class="font-medium text-sm text-gray-900">{{ activity.name }}</div>
                          <div class="text-sm text-gray-700 mt-1">{{ activity.role }}</div>
                          <div class="text-sm text-gray-600">{{ formatDate(activity.start_date) }} - {{ formatDate(activity.end_date) }}</div>
                          <div class="text-sm text-gray-600 whitespace-pre-line">{{ activity.description }}</div>
                        </div>
                        <div class="flex space-x-1 items-center self-center">
                          <el-button type="primary" class="icon-btn" link @click="handleEditActivity(index)">
                            <span class="material-icons-outlined text-xs">edit</span>
                          </el-button>
                          <el-button type="danger" class="icon-btn" link @click="handleDeleteActivity(index)">
                            <span class="material-icons-outlined text-xs">delete</span>
                          </el-button>
                        </div>
                      </div>
                    </div>
                    <div v-if="clientData.activities.length === 0" class="p-4 text-center text-gray-500 text-sm">
                      暂无课外活动，点击右上角添加
                    </div>
                  </div>
                </div>

                <!-- 奖项荣誉 -->
                <div v-if="module.type === 'award'" class="pro-card">
                  <div class="pro-card-header">
                    <div class="pro-card-title">
                      <span class="material-icons-outlined icon">{{ module.icon }}</span>
                      {{ module.title }}
                    </div>
                    <el-button type="primary" size="small" class="add-btn flex items-center" @click="handleAddAward">
                      <span class="material-icons-outlined text-xs mr-1">add</span>
                      添加
                    </el-button>
                  </div>
                  <div class="divide-y divide-gray-100">
                    <div v-for="(award, index) in clientData.awards" :key="index" class="p-4">
                      <div class="flex justify-between">
                        <div>
                          <div class="font-medium text-sm text-gray-900">{{ award.name }}</div>
                          <div class="text-sm text-gray-700 mt-1">{{ awardLevelMap[award.level] || award.level }} | {{ award.date }}</div>
                          <div class="text-sm text-gray-600 whitespace-pre-line">{{ award.description }}</div>
                        </div>
                        <div class="flex space-x-1 items-center self-center">
                          <el-button type="primary" class="icon-btn" link @click="handleEditAward(index)">
                            <span class="material-icons-outlined text-xs">edit</span>
                          </el-button>
                          <el-button type="danger" class="icon-btn" link @click="handleDeleteAward(index)">
                            <span class="material-icons-outlined text-xs">delete</span>
                          </el-button>
                        </div>
                      </div>
                    </div>
                    <div v-if="clientData.awards.length === 0" class="p-4 text-center text-gray-500 text-sm">
                      暂无奖项荣誉，点击右上角添加
                    </div>
                  </div>
                </div>

                <!-- 相关技能 -->
                <div v-if="module.type === 'skill'" class="pro-card">
                  <div class="pro-card-header">
                    <div class="pro-card-title">
                      <span class="material-icons-outlined icon">{{ module.icon }}</span>
                      {{ module.title }}
                    </div>
                    <el-button type="primary" size="small" class="add-btn flex items-center" @click="handleAddSkill">
                      <span class="material-icons-outlined text-xs mr-1">add</span>
                      添加
                    </el-button>
                  </div>
                  <div class="divide-y divide-gray-100">
                    <div v-for="(skill, index) in clientData.skills" :key="index" class="p-4">
                      <div class="flex justify-between">
                        <div>
                          <div class="font-medium text-sm text-gray-900">{{ skill.type }}:</div>
                          <div class="text-sm text-gray-700 mt-1 whitespace-pre-line">{{ skill.description }}</div>
                        </div>
                        <div class="flex space-x-1 items-center self-center">
                          <el-button type="primary" class="icon-btn" link @click="handleEditSkill(index)">
                            <span class="material-icons-outlined text-xs">edit</span>
                          </el-button>
                          <el-button type="danger" class="icon-btn" link @click="handleDeleteSkill(index)">
                            <span class="material-icons-outlined text-xs">delete</span>
                          </el-button>
                        </div>
                      </div>
                    </div>
                    <div v-if="clientData.skills.length === 0" class="p-4 text-center text-gray-500 text-sm">
                      暂无相关技能，点击右上角添加
                    </div>
                  </div>
                </div>

                <!-- 自定义模块 -->
                <div v-if="module.type === 'custom'" class="pro-card">
                  <div class="pro-card-header">
                    <div class="pro-card-title">
                      <span class="material-icons-outlined icon">{{ module.icon }}</span>
                      {{ module.title }}
                    </div>
                    <el-button type="primary" size="small" class="add-btn flex items-center" @click="() => handleEditCustomModule(module.id)">
                      <span class="material-icons-outlined text-xs mr-1">add</span>
                      添加
                    </el-button>
                  </div>
                  <div class="divide-y divide-gray-100">
                    <div v-if="clientData.customModules[module.id]?.content" class="p-4">
                      <div class="flex justify-between">
                        <div class="w-full">
                          <div class="text-gray-800 text-sm whitespace-pre-line">
                            {{ clientData.customModules[module.id]?.content }}
                          </div>
                        </div>
                        <div class="flex space-x-1 flex-shrink-0 ml-4 items-center self-center">
                          <el-button type="primary" class="edit-btn icon-btn" link @click="() => handleEditCustomModule(module.id)">
                            <span class="material-icons-outlined text-xs">edit</span>
                          </el-button>
                          <el-button type="danger" class="delete-btn icon-btn" link @click="() => handleDeleteCustomModuleContent(module.id)">
                            <span class="material-icons-outlined text-xs">delete</span>
                          </el-button>
                        </div>
                      </div>
                    </div>
                    <div v-else class="p-4 text-center text-gray-500 text-sm">
                      暂无内容，点击右上角添加
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>

          <div v-else-if="activeTab === 'identifications'" class="space-y-5" key="identifications">

            <!-- 身份证信息 -->
            <div class="space-y-5">
              <!-- 身份证信息 -->
              <div class="pro-card">
                <div class="pro-card-header">
                  <div class="pro-card-title">
                    <span class="material-icons-outlined icon">badge</span>
                    身份证信息
                  </div>
                  <el-button type="primary" class="edit-btn icon-btn" link @click="handleEditIdCard">
                    <span class="material-icons-outlined text-sm">edit</span>
                  </el-button>
                </div>
                <div class="pro-card-body">
                  <div class="space-y-3 text-sm">
                    <div class="flex items-center justify-between">
                      <div class="text-sm text-gray-700">身份证号</div>
                      <div class="text-sm text-gray-900">{{ clientData.idCard || '-' }}</div>
                    </div>
                    <div class="flex items-center justify-between">
                      <div class="text-sm text-gray-700">签发机关</div>
                      <div class="text-sm text-gray-900">{{ clientData.idCardIssuer || '-' }}</div>
                    </div>
                    <div class="flex items-center justify-between">
                      <div class="text-sm text-gray-700">有效期限</div>
                      <div class="text-sm text-gray-900">{{ formatDate(clientData.idCardValidity) || '-' }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 护照信息 -->
              <div class="pro-card">
                <div class="pro-card-header">
                  <div class="pro-card-title">
                    <span class="material-icons-outlined icon">flight</span>
                    护照信息
                  </div>
                  <el-button type="primary" class="edit-btn icon-btn" link @click="handleEditPassport">
                    <span class="material-icons-outlined text-sm">edit</span>
                  </el-button>
                </div>
                <div class="pro-card-body">
                  <div class="space-y-3 text-sm">
                    <div class="flex items-center justify-between">
                      <div class="text-sm text-gray-700">护照号码</div>
                      <div class="text-sm text-gray-900">{{ clientData.passport || '-' }}</div>
                    </div>
                    <div class="flex items-center justify-between">
                      <div class="text-sm text-gray-700">签发地点</div>
                      <div class="text-sm text-gray-900">{{ clientData.passportIssuePlace || '-' }}</div>
                    </div>
                    <div class="flex items-center justify-between">
                      <div class="text-sm text-gray-700">签发日期</div>
                      <div class="text-sm text-gray-900">{{ formatDate(clientData.passportIssueDate) || '-' }}</div>
                    </div>
                    <div class="flex items-center justify-between">
                      <div class="text-sm text-gray-700">有效期至</div>
                      <div class="text-sm text-gray-900">{{ formatDate(clientData.passportExpiry) || '-' }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-else-if="activeTab === 'materials'" class="space-y-5" key="materials">
            <!-- 申请材料组件 -->
            <ApplicationMaterials 
              :client-data="clientData" 
              :is-new-client="isNewClient"
            />
          </div>



          <div v-else-if="activeTab === 'schools'" class="space-y-5" key="schools">
            <!-- 定校书组件 -->
            <SchoolList 
              :client-data="clientData" 
              :is-new-client="isNewClient"
              :school-programs="schoolPrograms"
              :add-school-program-dialog-visible="addSchoolProgramDialogVisible"
              @update:add-school-program-dialog-visible="addSchoolProgramDialogVisible = $event"
              @add-school-program="handleAddSchoolProgram"
              @select-program="handleSelectProgram"
              @remove-school-program="handleRemoveSchoolProgram"
              @view-program-details="handleViewProgramDetails"
              @download-school-book="handleDownloadSchoolBook"
            />
          </div>

          <div v-else-if="activeTab === 'documents'" class="space-y-5" key="documents">
            <!-- 个人简历部分 -->
            <div class="pro-card">
              <div class="pro-card-header">
                <div class="pro-card-title">
                  <span class="material-icons-outlined icon">assignment</span>
                  简历 CV
                </div>
              </div>
              <div class="pro-card-body">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <!-- CV文书卡片 -->
                  <div
                    v-for="(cvDoc, index) in cvDocuments"
                    :key="cvDoc.id"
                    class="document-item-card bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg p-4 cursor-pointer transition-all duration-200 h-36 flex flex-col"
                    @click="handleNavigateToCV(cvDoc)"
                  >
                    <div class="flex-1 flex flex-col">
                      <!-- 顶部标题和版本 -->
                      <div class="flex items-start justify-between mb-3">
                        <div class="flex-1">
                          <!-- 如果有target_major，显示学校专业信息；否则显示默认标题 -->
                          <div v-if="cvDoc.target_major" class="flex items-start space-x-3 mb-2">
                            <!-- 学校logo -->
                            <div class="flex-shrink-0">
                              <img
                                :src="getCVSchoolLogo(cvDoc)"
                                :alt="getCVSchoolName(cvDoc)"
                                class="w-8 h-8 rounded-lg object-contain bg-white shadow-sm border border-gray-200"
                                @error="handleLogoError"
                              />
                            </div>
                            <!-- 学校和专业信息 -->
                            <div class="flex-1 min-w-0">
                              <h3 class="font-medium text-gray-900 text-sm mb-1 truncate">{{ getCVSchoolName(cvDoc) }}</h3>
                              <p class="text-xs text-gray-600 mb-1 line-clamp-1">{{ getCVProgramName(cvDoc) }}</p>
                            </div>
                          </div>
                          <!-- 没有target_major时的默认显示 -->
                          <div v-else class="mb-2">
                            <h3 class="font-medium text-gray-900 text-base mb-1">学术简历</h3>
                          </div>
                          <!-- 版本信息标签 -->
                          <div class="flex items-center gap-2">
                            <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                              版本 {{ cvDoc.version || (index + 1) }}
                            </span>
                          </div>
                        </div>
                        <!-- 状态指示器 -->
                        <div class="flex-shrink-0">
                          <span class="inline-flex items-center w-2 h-2 bg-green-400 rounded-full" title="最新版本"></span>
                        </div>
                      </div>

                      <!-- 底部信息 -->
                      <div class="mt-auto">
                        <div class="flex items-center justify-between text-xs">
                          <div class="flex items-center text-gray-600">
                            <span class="material-icons-outlined text-xs mr-1">person</span>
                            <span class="truncate max-w-20">{{ cvDoc.created_by || currentUser?.nickname || currentUser?.username || currentUser?.name || 'celia' }}</span>
                          </div>
                          <div class="flex items-center text-gray-500">
                            <span class="material-icons-outlined text-xs mr-1">schedule</span>
                            <span>{{ formatDate(cvDoc.updated_at || cvDoc.created_at) }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 空状态：没有CV时显示 -->
                  <div
                    v-if="cvDocuments.length === 0"
                    class="document-item-card bg-gray-50 border border-gray-200 rounded-lg p-4 flex items-center justify-center text-center cursor-pointer hover:bg-gray-100 transition-all duration-200 h-36"
                    @click="handleNavigateToCV()"
                  >
                    <div>
                      <span class="material-icons-outlined text-gray-400 text-2xl mb-2 block">add_circle_outline</span>
                      <p class="text-sm text-gray-500">暂无CV</p>
                      <p class="text-xs text-gray-400">点击创建第一份简历</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- RL部分 -->
            <div class="pro-card">
              <div class="pro-card-header">
                <div class="pro-card-title">
                  <span class="material-icons-outlined icon">recommend</span>
                  推荐信 RL
                </div>
              </div>
                             <div class="pro-card-body">
                 <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                      <!-- RL文书卡片 -->
                   <div 
                     v-for="(rlDoc, index) in rlDocuments" 
                     :key="rlDoc.id"
                     class="document-item-card bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg p-4 cursor-pointer transition-all duration-200 h-36 flex flex-col" 
                     @click="handleNavigateToRL(rlDoc)"
                   >
                     <div class="flex-1 flex flex-col">
                       <!-- 顶部标题和版本 -->
                       <div class="flex items-start justify-between mb-3">
                         <div class="flex-1">
                           <h3 class="font-medium text-gray-900 text-base mb-1">{{ rlDoc.recommender_name || '孙笑川' }}</h3>
                           <!-- 版本信息标签 -->
                           <div class="flex items-center gap-2">
                             <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                               版本 {{ rlDoc.version || (index + 1) }}
                             </span>
                             <span class="text-xs text-gray-500">版本 {{ formatDateTime(rlDoc.updated_at || rlDoc.created_at) }}</span>
                           </div>
                         </div>
                         <!-- 状态指示器 -->
                         <div class="flex-shrink-0">
                           <span class="inline-flex items-center w-2 h-2 bg-green-400 rounded-full" title="最新版本"></span>
                         </div>
                       </div>
                       
                       <!-- 底部信息 -->
                       <div class="mt-auto">
                         <div class="flex items-center justify-between text-xs">
                           <div class="flex items-center text-gray-600">
                             <span class="material-icons-outlined text-xs mr-1">person</span>
                             <span class="truncate max-w-20">{{ rlDoc.created_by || currentUser?.nickname || currentUser?.username || currentUser?.name || 'celia' }}</span>
                           </div>
                           <div class="flex items-center text-gray-500">
                             <span class="material-icons-outlined text-xs mr-1">schedule</span>
                             <span>{{ formatDate(rlDoc.updated_at || rlDoc.created_at) }}</span>
                           </div>
                         </div>
                       </div>
                     </div>
                   </div>

                   <!-- 空状态：没有RL时显示 -->
                   <div
                     v-if="rlDocuments.length === 0"
                     class="document-item-card bg-gray-50 border border-gray-200 rounded-lg p-4 flex items-center justify-center text-center cursor-pointer hover:bg-gray-100 transition-all duration-200 h-36"
                     @click="handleNavigateToRL()"
                   >
                     <div>
                       <span class="material-icons-outlined text-gray-400 text-2xl mb-2 block">add_circle_outline</span>
                       <p class="text-sm text-gray-500">暂无推荐信</p>
                       <p class="text-xs text-gray-400">点击创建第一份推荐信</p>
                     </div>
                   </div>
                 </div>
              </div>
            </div>

            <!-- PS部分 -->
            <div class="pro-card">
              <div class="pro-card-header">
                <div class="pro-card-title">
                  <span class="material-icons-outlined icon">article</span>
                  个人陈述 PS
                </div>
              </div>
              <div class="pro-card-body">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <!-- PS卡片 - 基于定校书数据 -->
                  <div 
                    v-for="(program, index) in psPrograms" 
                    :key="program.id"
                    class="document-item-card bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg p-4 cursor-pointer transition-all duration-200 h-36 flex flex-col" 
                    @click="handleNavigateToPS"
                  >
                    <div class="flex-1 flex flex-col">
                      <!-- 顶部标题和版本 -->
                      <div class="flex items-start justify-between mb-3">
                        <div class="flex-1">
                          <div class="flex items-start space-x-3 mb-2">
                            <!-- 学校logo -->
                            <div class="flex-shrink-0">
                              <img 
                                :src="program.logo" 
                                :alt="program.school_name_cn"
                                class="w-8 h-8 rounded-lg object-contain bg-white shadow-sm border border-gray-200"
                                @error="handleLogoError"
                              />
                            </div>
                            <!-- 学校和专业信息 -->
                            <div class="flex-1 min-w-0">
                              <h3 class="font-medium text-gray-900 text-sm mb-1 truncate">{{ program.school_name_cn }}</h3>
                              <p class="text-xs text-gray-600 mb-1 line-clamp-1">{{ program.program_name_cn }}</p>
                            </div>
                          </div>
                          <!-- 版本信息标签 -->
                          <div class="flex items-center gap-2">
                            <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-purple-100 text-purple-800 border border-purple-200">
                              版本 {{ program.version || (index + 1) }}
                            </span>
                          </div>
                        </div>
                        <!-- 状态指示器 -->
                        <div class="flex-shrink-0">
                          <span class="inline-flex items-center w-2 h-2 bg-green-400 rounded-full" title="最新版本"></span>
                        </div>
                      </div>
                      
                      <!-- 底部信息 -->
                      <div class="mt-auto">
                        <div class="flex items-center justify-between text-xs">
                          <div class="flex items-center text-gray-600">
                            <span class="material-icons-outlined text-xs mr-1">person</span>
                            <span class="truncate max-w-20">{{ currentUser?.nickname || currentUser?.username || currentUser?.name || 'celia' }}</span>
                          </div>
                          <div class="flex items-center text-gray-500">
                            <span class="material-icons-outlined text-xs mr-1">schedule</span>
                            <span>{{ formatDate(program.created_at) }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 空状态：没有定校书时显示 -->
                  <div v-if="psPrograms.length === 0" class="document-item-card bg-gray-50 border border-gray-200 rounded-lg p-4 flex items-center justify-center text-center col-span-full h-36">
                    <div>
                      <span class="material-icons-outlined text-gray-400 text-2xl mb-2 block">school</span>
                      <p class="text-sm text-gray-500">暂无定校书专业</p>
                      <p class="text-xs text-gray-400">请先在"定校书"页面添加专业</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </transition>
      </div>
    </div>

    <!-- 编辑弹窗 -->
    <!-- 模块管理弹窗 -->
    <el-dialog
      v-model="moduleManageDialogVisible"
      title="管理模块"
      width="650px"
      custom-class="custom-dialog"
    >
      <div class="p-2">
        <div class="flex justify-between items-center mb-4">
          <p class="text-xs text-gray-500">您可以通过拖拽调整模块顺序，点击开关控制模块显示或隐藏</p>
          <el-button type="primary" size="small" @click="handleAddNewModule" class="flex items-center">
            <span class="material-icons-outlined text-xs mr-1">add</span>
            添加新模块
          </el-button>
        </div>

        <div ref="moduleSortableContainer" class="w-full mb-4 border border-gray-200 rounded-lg">
          <div v-for="module in backgroundModules" :key="module.id" :data-id="module.id"
               class="flex items-center justify-between p-2 border-b last:border-0 border-gray-200 bg-white hover:bg-gray-50 cursor-move">
            <div class="flex items-center">
              <span class="material-icons-outlined text-gray-400 mr-3 text-base">drag_indicator</span>
              <span class="material-icons-outlined text-gray-500 mr-2 text-sm">{{ module.icon }}</span>
              <div class="flex-grow">
                <el-input
                  v-model="module.title"
                  class="max-w-40"
                  size="small"
                  @change="() => handleModuleTitleChange(module.id, module.title)"
                />
              </div>
            </div>
            <div class="flex items-center space-x-4">
              <div class="flex items-center">
                <span class="text-xs text-gray-500 mr-2">显示</span>
                <el-switch
                  v-model="module.visible"
                  @update:model-value="() => handleToggleModuleVisibility(module.id)"
                />
              </div>
              <el-button
                type="danger"
                link
                class="icon-btn"
                :disabled="module.isDefault"
                @click="handleDeleteModule(module.id)"
              >
                <span class="material-icons-outlined text-xs">delete</span>
              </el-button>
            </div>
          </div>
        </div>

        <div v-if="isAddingNewModule" class="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <h4 class="text-sm font-medium mb-3">添加新模块</h4>
          <el-form :model="newModuleForm" label-width="80px" class="text-sm">
            <el-form-item label="模块名称" required>
              <el-input v-model="newModuleForm.title" placeholder="请输入模块名称" />
            </el-form-item>
            <el-form-item label="图标" required>
              <div class="grid grid-cols-8 gap-2">
                <div
                  v-for="icon in availableIcons"
                  :key="icon"
                  @click="newModuleForm.icon = icon"
                  class="w-8 h-8 flex items-center justify-center rounded-md cursor-pointer hover:bg-gray-200"
                  :class="[newModuleForm.icon === icon ? 'bg-primary bg-opacity-10 text-primary' : 'bg-white text-gray-500']"
                >
                  <span class="material-icons-outlined text-base">{{ icon }}</span>
                </div>
              </div>
            </el-form-item>
            <el-form-item>
              <div class="flex space-x-2">
                <el-button size="small" @click="isAddingNewModule = false">取消</el-button>
                <el-button type="primary" size="small" @click="handleConfirmAddModule">确认添加</el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end">
          <el-button class="cancel-btn" @click="moduleManageDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 语言成绩编辑弹窗 -->
    <el-dialog
      v-model="languageScoreDialogVisible"
      :title="editingIndex === -1 ? '添加语言成绩' : '编辑语言成绩'"
      width="500px"
      custom-class="custom-dialog"
    >
      <el-form :model="editingForm" label-width="100px" class="text-sm">
        <el-form-item label="考试类型" required>
          <el-select v-model="editingForm.type" class="w-full">
            <el-option label="托福 (TOEFL)" value="toefl" />
            <el-option label="雅思 (IELTS)" value="ielts" />
            <el-option label="GRE" value="gre" />
            <el-option label="GMAT" value="gmat" />
            <el-option label="SAT" value="sat" />
            <el-option label="ACT" value="act" />
            <el-option label="多邻国 (Duolingo)" value="duolingo" />
            <el-option label="日语能力考试 (JLPT)" value="jlpt" />
            <el-option label="汉语水平考试 (HSK)" value="hsk" />
            <el-option label="西班牙语水平考试 (DELE)" value="dele" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="成绩/分数" required>
          <el-input v-model="editingForm.score" />
        </el-form-item>
        <el-form-item label="考试日期">
          <el-date-picker
            v-model="editingForm.date"
            type="date"
            class="w-full"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            :default-time="new Date(2000, 0, 1, 8)"
          />
        </el-form-item>
        <el-form-item label="有效期至">
          <el-date-picker
            v-model="editingForm.validity"
            type="date"
            class="w-full"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            :default-time="new Date(2000, 0, 1, 8)"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end">
          <el-button class="cancel-btn" @click="languageScoreDialogVisible = false">取消</el-button>
          <el-button type="primary" class="confirm-btn" @click="handleSaveLanguageScore">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 学术经历编辑弹窗 -->
    <el-dialog
      v-model="academicDialogVisible"
      :title="editingIndex === -1 ? '添加学术经历' : '编辑学术经历'"
      width="500px"
      custom-class="custom-dialog"
    >
      <el-form :model="editingForm" label-width="100px" class="text-sm">
        <el-form-item label="项目主题" required>
          <el-input v-model="editingForm.title" />
        </el-form-item>
        <el-form-item label="学术类型" required>
          <el-select
            v-model="editingForm.type"
            class="w-full"
            filterable
            allow-create
            default-first-option
            placeholder="请选择或输入学术类型"
          >
            <el-option label="毕业设计" value="毕业设计" />
            <el-option label="论文" value="论文" />
            <el-option label="科研项目" value="科研项目" />
            <el-option label="学科课程项目" value="学科课程项目" />
            <el-option label="大学生创业项目" value="大学生创业项目" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="研究日期">
          <el-date-picker v-model="editingForm.date" type="date" class="w-full" value-format="YYYY-MM-DD" format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="详细描述">
          <el-input v-model="editingForm.description" type="textarea" :rows="6" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end">
          <el-button class="cancel-btn" @click="academicDialogVisible = false">取消</el-button>
          <el-button type="primary" class="confirm-btn" @click="handleSaveAcademic">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 课外活动编辑弹窗 -->
    <el-dialog
      v-model="activityDialogVisible"
      :title="editingIndex === -1 ? '添加课外活动' : '编辑课外活动'"
      width="500px"
      custom-class="custom-dialog"
    >
      <el-form :model="editingForm" label-width="100px" class="text-sm">
        <el-form-item label="活动名称" required>
          <el-input v-model="editingForm.name" />
        </el-form-item>
        <el-form-item label="担任角色" required>
          <el-input v-model="editingForm.role" />
        </el-form-item>
        <el-form-item label="开始时间" required>
          <el-date-picker v-model="editingForm.start_date" type="date" class="w-full" value-format="YYYY-MM-DD" format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="结束时间" required>
          <el-date-picker v-model="editingForm.end_date" type="date" class="w-full" value-format="YYYY-MM-DD" format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="活动描述">
          <el-input v-model="editingForm.description" type="textarea" :rows="6" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end">
          <el-button class="cancel-btn" @click="activityDialogVisible = false">取消</el-button>
          <el-button type="primary" class="confirm-btn" @click="handleSaveActivity">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 技能编辑弹窗 -->
    <el-dialog
      v-model="skillDialogVisible"
      :title="editingIndex === -1 ? '添加技能' : '编辑技能'"
      width="500px"
      custom-class="custom-dialog"
    >
      <el-form :model="editingForm" label-width="100px" class="text-sm">
        <el-form-item label="技能类型" required>
          <el-select
            v-model="editingForm.type"
            class="w-full"
            filterable
            allow-create
            default-first-option
            placeholder="请选择或输入技能类型"
          >
            <el-option label="专业技能" value="专业技能" />
            <el-option label="综合技能" value="综合技能" />
            <!-- 允许用户创建其他类型 -->
          </el-select>
        </el-form-item>
        <el-form-item label="技能描述" required>
          <el-input
            v-model="editingForm.description"
            type="textarea"
            :rows="6"
            placeholder="请输入具体技能"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end">
          <el-button class="cancel-btn" @click="skillDialogVisible = false">取消</el-button>
          <el-button type="primary" class="confirm-btn" @click="handleSaveSkill">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 个人信息编辑弹窗 -->
    <el-dialog
      v-model="personalInfoDialogVisible"
      title="编辑个人信息"
      width="480px"
      custom-class="custom-dialog"
    >
      <div class="px-2">
        <el-form :model="editingForm" label-width="80px" class="text-sm">
          <el-form-item label="姓名" required>
            <el-input v-model="editingForm.name" />
          </el-form-item>
          <el-form-item label="性别" required>
            <el-select v-model="editingForm.gender" class="w-full">
              <el-option label="男" value="male" />
              <el-option label="女" value="female" />
              <el-option label="未知" value="unknown" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="flex justify-end">
          <el-button class="cancel-btn" @click="personalInfoDialogVisible = false">取消</el-button>
          <el-button type="primary" class="confirm-btn" @click="handleSavePersonalInfo">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 教育经历编辑弹窗 -->
    <el-dialog
      v-model="educationDialogVisible"
      :title="editingIndex === -1 ? '添加教育经历' : '编辑教育经历'"
      width="500px"
      custom-class="custom-dialog"
    >
      <el-form :model="editingForm" label-width="100px" class="text-sm">
        <el-form-item label="学校名称" required>
          <el-input v-model="editingForm.school" />
        </el-form-item>
        <el-form-item label="专业" required>
          <el-input v-model="editingForm.major" />
        </el-form-item>
        <el-form-item label="学历" required>
          <el-select v-model="editingForm.degree" class="w-full">
            <el-option label="高中" value="high_school" />
            <el-option label="本科" value="bachelor" />
            <el-option label="硕士" value="master" />
            <el-option label="博士" value="phd" />
            <el-option label="暑校" value="summer_school" />
            <el-option label="第二学位" value="second_degree" />
            <el-option label="交换" value="exchange" />
          </el-select>
        </el-form-item>
        <el-form-item label="GPA">
          <el-input v-model="editingForm.gpa" type="number" />
        </el-form-item>
        <el-form-item label="开始时间" required>
          <el-date-picker v-model="editingForm.start_date" type="date" class="w-full" value-format="YYYY-MM-DD" format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="结束时间" required>
          <el-date-picker v-model="editingForm.end_date" type="date" class="w-full" value-format="YYYY-MM-DD" format="YYYY-MM-DD" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end">
          <el-button class="cancel-btn" @click="educationDialogVisible = false">取消</el-button>
          <el-button type="primary" class="confirm-btn" @click="handleSaveEducation">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 奖项荣誉编辑弹窗 -->
    <el-dialog
      v-model="awardDialogVisible"
      :title="editingIndex === -1 ? '添加奖项荣誉' : '编辑奖项荣誉'"
      width="500px"
      custom-class="custom-dialog"
    >
      <el-form :model="editingForm" label-width="100px" class="text-sm">
        <el-form-item label="奖项名称" required>
          <el-input v-model="editingForm.name" />
        </el-form-item>
        <el-form-item label="获奖级别" required>
          <el-select v-model="editingForm.level" class="w-full">
            <el-option label="校级" value="school" />
            <el-option label="市级" value="city" />
            <el-option label="省级" value="province" />
            <el-option label="国家级" value="national" />
            <el-option label="世界级" value="world" />
          </el-select>
        </el-form-item>
        <el-form-item label="获奖时间" required>
          <el-date-picker v-model="editingForm.date" type="date" class="w-full" value-format="YYYY-MM-DD" format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="奖项描述">
          <el-input v-model="editingForm.description" type="textarea" :rows="6" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end">
          <el-button class="cancel-btn" @click="awardDialogVisible = false">取消</el-button>
          <el-button type="primary" class="confirm-btn" @click="handleSaveAward">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 工作经历编辑弹窗 -->
    <el-dialog
      v-model="workDialogVisible"
      :title="editingIndex === -1 ? '添加工作经历' : '编辑工作经历'"
      width="500px"
      custom-class="custom-dialog"
    >
      <el-form :model="editingForm" label-width="100px" class="text-sm">
        <el-form-item label="公司名称" required>
          <el-input v-model="editingForm.company" />
        </el-form-item>
        <el-form-item label="职位" required>
          <el-input v-model="editingForm.position" />
        </el-form-item>
        <el-form-item label="开始时间" required>
          <el-date-picker v-model="editingForm.start_date" type="date" class="w-full" value-format="YYYY-MM-DD" format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="结束时间" required>
          <el-date-picker v-model="editingForm.end_date" type="date" class="w-full" value-format="YYYY-MM-DD" format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="工作描述">
          <el-input v-model="editingForm.description" type="textarea" :rows="6" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end">
          <el-button class="cancel-btn" @click="workDialogVisible = false">取消</el-button>
          <el-button type="primary" class="confirm-btn" @click="handleSaveWork">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 自定义模块编辑弹窗 -->
    <el-dialog
      v-model="customModuleDialogVisible"
      title="编辑自定义模块"
      width="500px"
      custom-class="custom-dialog"
    >
      <div class="px-2">
        <el-form label-width="80px" class="text-sm">
          <el-form-item label="内容" required>
            <el-input v-model="customModuleContent" type="textarea" :rows="6" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="flex justify-end">
          <el-button class="cancel-btn" @click="customModuleDialogVisible = false">取消</el-button>
          <el-button type="primary" class="confirm-btn" @click="handleSaveCustomModuleContent">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 联系方式编辑弹窗 -->
    <el-dialog
      v-model="contactDialogVisible"
      title="编辑联系方式"
      width="480px"
      custom-class="custom-dialog"
          >
        <div class="px-2">
          <el-form :model="editingForm" label-width="80px" class="text-sm">
            <el-form-item label="电话">
              <el-input v-model="editingForm.phone" placeholder="请输入联系电话" />
            </el-form-item>
            <el-form-item label="邮箱">
              <el-input v-model="editingForm.email" placeholder="请输入邮箱地址" />
            </el-form-item>
          </el-form>
        </div>
      <template #footer>
        <div class="flex justify-end">
          <el-button class="cancel-btn" @click="contactDialogVisible = false">取消</el-button>
          <el-button type="primary" class="confirm-btn" @click="handleSaveContact">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 地址编辑弹窗 -->
    <el-dialog
      v-model="addressDialogVisible"
      title="编辑地址"
      width="480px"
      custom-class="custom-dialog"
    >
      <div class="px-2">
        <el-form :model="editingForm" label-width="80px" class="text-sm">
          <el-form-item label="地址" required>
            <el-input v-model="editingForm.address" placeholder="请输入详细地址" />
          </el-form-item>
          <el-form-item label="所在地" required>
            <el-input v-model="editingForm.location" placeholder="例如：Shanghai, Shanghai, China" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="flex justify-end">
          <el-button class="cancel-btn" @click="addressDialogVisible = false">取消</el-button>
          <el-button type="primary" class="confirm-btn" @click="handleSaveAddress">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 身份证信息编辑弹窗 -->
    <el-dialog
      v-model="idCardDialogVisible"
      title="编辑身份证信息"
      width="480px"
      custom-class="custom-dialog"
    >
      <div class="px-2">
        <el-form :model="editingForm" label-width="80px" class="text-sm">
          <el-form-item label="身份证号" required>
            <el-input v-model="editingForm.idCard" placeholder="请输入身份证号" />
          </el-form-item>
          <el-form-item label="签发机关">
            <el-input v-model="editingForm.idCardIssuer" placeholder="请输入签发机关" />
          </el-form-item>
          <el-form-item label="有效期限">
            <el-date-picker
              v-model="editingForm.idCardValidity"
              type="date"
              placeholder="请选择有效期限"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :default-time="new Date(2000, 0, 1, 8)"
              class="w-full"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="flex justify-end">
          <el-button class="cancel-btn" @click="idCardDialogVisible = false">取消</el-button>
          <el-button type="primary" class="confirm-btn" @click="handleSaveIdCard">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 护照信息编辑弹窗 -->
    <el-dialog
      v-model="passportDialogVisible"
      title="编辑护照信息"
      width="480px"
      custom-class="custom-dialog"
    >
      <div class="px-2">
        <el-form :model="editingForm" label-width="80px" class="text-sm">
          <el-form-item label="护照号码" required>
            <el-input v-model="editingForm.passport" placeholder="请输入护照号码" />
          </el-form-item>
          <el-form-item label="签发地点">
            <el-input v-model="editingForm.passportIssuePlace" placeholder="请输入签发地点" />
          </el-form-item>
          <el-form-item label="签发日期">
            <el-date-picker
              v-model="editingForm.passportIssueDate"
              type="date"
              placeholder="请选择签发日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :default-time="new Date(2000, 0, 1, 8)"
              class="w-full"
            />
          </el-form-item>
          <el-form-item label="有效期至">
            <el-date-picker
              v-model="editingForm.passportExpiry"
              type="date"
              placeholder="请选择有效期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :default-time="new Date(2000, 0, 1, 8)"
              class="w-full"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="flex justify-end">
          <el-button class="cancel-btn" @click="passportDialogVisible = false">取消</el-button>
          <el-button type="primary" class="confirm-btn" @click="handleSavePassport">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加方式选择弹窗 -->
    <el-dialog
      v-model="addModeDialogVisible"
      :title="addModeTitle"
      width="500px"
      custom-class="custom-dialog"
    >
      <div class="px-2">
        <p class="text-sm text-gray-600 mb-6">请选择添加方式：</p>
        
        <div class="space-y-4">
          <!-- 手动添加选项 -->
          <div 
            class="border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-primary hover:bg-blue-50 transition-all duration-200"
            @click="handleSelectAddMode('manual')"
          >
            <div class="flex items-center">
              <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                <span class="material-icons-outlined text-blue-600">edit</span>
              </div>
              <div>
                <h3 class="text-base font-medium text-gray-900 mb-1">手动添加</h3>
                <p class="text-sm text-gray-500">
                  {{ addModeType === 'academic' ? '手动填写学术经历的详细信息' : '手动填写工作经历的详细信息' }}
                </p>
              </div>
            </div>
          </div>

          <!-- 智能添加选项 -->
          <!-- 暂时注释掉智能添加功能
          <div 
            class="border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-primary hover:bg-blue-50 transition-all duration-200"
            @click="handleSelectAddMode('smart')"
          >
            <div class="flex items-center">
              <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                <span class="material-icons-outlined text-green-600">upload_file</span>
              </div>
              <div>
                <h3 class="text-base font-medium text-gray-900 mb-1">智能添加</h3>
                <p class="text-sm text-gray-500">
                  {{ addModeType === 'academic' ? '上传学术文件，AI自动提取并生成经历内容' : '上传工作文件，AI自动提取并生成经历内容' }}
                </p>
              </div>
            </div>
          </div>
          -->
        </div>
      </div>
      
      <template #footer>
        <div class="flex justify-end">
          <el-button class="cancel-btn" @click="addModeDialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 智能上传弹窗 -->
    <!-- 暂时注释掉智能上传功能
    <el-dialog
      v-model="smartUploadDialogVisible"
      :title="smartUploadTitle"
      width="600px"
      custom-class="custom-dialog"
    >
      <div class="px-2">
        <div class="mb-4">
          <p class="text-sm text-gray-600 mb-4">
            {{ smartUploadType === 'academic' ? '上传学术文件（如论文、项目报告等），系统将自动提取内容生成学术经历' : '上传工作相关文件（如简历、工作证明等），系统将自动提取内容生成工作经历' }}
          </p>
          
          <el-upload
            ref="uploadRef"
            v-model:file-list="uploadFileList"
            :auto-upload="false"
            :limit="1"
            :accept="'.pdf,.doc,.docx,.txt'"
            :on-exceed="handleExceed"
            :on-change="handleFileChange"
            drag
            class="w-full"
          >
            <div class="flex flex-col items-center justify-center py-8">
              <span class="material-icons-outlined text-4xl text-gray-400 mb-4">upload_file</span>
              <div class="text-center">
                <p class="text-sm text-gray-600 mb-2">点击或拖拽文件到此区域上传</p>
                <p class="text-xs text-gray-400">支持 PDF、Word、TXT 格式文件，最大 10MB</p>
              </div>
            </div>
          </el-upload>
        </div>

        <div v-if="extractedContent" class="mt-4">
          <h4 class="text-sm font-medium mb-2">提取的内容：</h4>
          <div class="bg-gray-50 rounded-lg p-4 max-h-60 overflow-y-auto">
            <p class="text-sm text-gray-700 whitespace-pre-line">{{ extractedContent }}</p>
          </div>
        </div>

        <div v-if="isExtracting" class="mt-4 flex items-center justify-center py-4">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mr-3"></div>
          <span class="text-sm text-gray-600">正在智能提取内容...</span>
        </div>
      </div>
      
      <template #footer>
        <div class="flex justify-end space-x-2">
          <el-button class="cancel-btn" @click="handleCancelSmartUpload">取消</el-button>
          <el-button 
            type="primary" 
            :disabled="!uploadFileList.length || isExtracting"
            @click="handleConfirmSmartUpload"
            class="confirm-btn"
          >
            {{ isExtracting ? '提取中...' : '开始提取' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
    -->





  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import Sortable from 'sortablejs'

// 导入组件
import SchoolList from './SchoolList.vue'
import ApplicationMaterials from './ApplicationMaterials.vue'

// 导入stores
import { useAuthStore } from '@/stores/auth'

// 导入工具函数
import { getSchoolLogo as getSchoolLogoFallback } from '@/utils/schoolLogos'
import { useSchoolLogosStore } from '@/stores/schoolLogos'

// 导入API函数
import {
  getClientById,
  updateClient,
  addEducation,
  updateEducation,
  deleteEducation,
  addAcademic,
  updateAcademic,
  deleteAcademic,
  addWork,
  updateWork,
  deleteWork,
  addActivity,
  updateActivity,
  deleteActivity,
  addAward,
  updateAward,
  deleteAward,
  addSkill,
  updateSkill,
  deleteSkill,
  addLanguageScore,
  updateLanguageScore,
  deleteLanguageScore,
  addBackgroundModule,
  updateBackgroundModule,
  deleteBackgroundModule,
  // 添加定校书相关API
  getClientPrograms,
  addClientProgram,
  removeClientProgram,
  // 添加文书相关API
  getClientDocuments,
  // augmentExperienceFromFile // 智能增强 - 暂时注释掉
} from '../../api/client.js'

// 类型定义
interface Education {
  id?: number
  school: string
  major: string
  degree: string
  gpa: string
  start_date: string
  end_date: string
  description?: string
}

interface Award {
  id?: number
  name: string
  level: string
  date: string
  description: string
}

interface Work {
  id?: number
  company: string
  position: string
  start_date: string
  end_date: string
  description: string
}

interface Academic {
  id?: number
  title: string
  type: string // 修改：原institution字段
  date: string
  description: string
}

interface Activity {
  id?: number
  name: string
  role: string
  start_date: string
  end_date: string
  description: string
}

interface Skill {
  id?: number
  name?: string // 添加name字段，兼容旧代码
  type: string // 用于存储技能类型，如"专业技能"
  level?: string // 添加level字段，兼容旧代码
  description: string // 用于存储具体的技能列表，如 "Access Database, MATLAB, SPSS"
}

interface LanguageScore {
  id?: number
  type: string
  score: string
  date: string
  validity: string
}

interface SchoolProgram {
  id: string
  school_name_cn: string
  school_name_en: string
  program_name_cn: string
  program_name_en: string
  school_region: string
  school_qs_rank?: number
  program_website?: string
  degree?: string
  program_category?: string
  gpa_requirements?: string
  language_requirements?: string
  application_requirements?: string
  program_tuition?: string
  enrollment_time?: string
  program_duration?: string
  created_at?: string
  client_program_id?: number  // 添加这个字段用于删除操作
}



interface NewModuleForm {
  title: string
  icon: string
}

interface CustomModuleContent {
  id?: number
  content: string
  module_id?: string
  title?: string
  icon?: string
  visible?: boolean
  order?: number
}

interface BackgroundModule {
  id: string
  type: 'education' | 'academic' | 'work' | 'activity' | 'award' | 'skill' | 'custom' | 'internal'
  title: string
  icon: string
  visible: boolean
  order: number
  isDefault: boolean  // 标识是否为默认模块
}

interface ClientData {
  name: string
  location: string
  gender: string
  phone: string
  email: string
  address: string
  // 身份证信息
  idCard: string
  idCardIssuer: string
  idCardValidity: string
  // 护照信息
  passport: string
  passportIssuePlace: string
  passportIssueDate: string
  passportExpiry: string
  // 时间信息
  created_at?: string
  updated_at?: string
  // 其他信息
  education: Education[]
  awards: Award[]
  work: Work[]
  academic: Academic[]
  activities: Activity[]
  skills: Skill[]
  languageScores: LanguageScore[]
  customModules: Record<string, CustomModuleContent>
}

// 路由实例
const route = useRoute()
const router = useRouter()

// Auth store
const authStore = useAuthStore()
const currentUser = computed(() => authStore.user)

// 组件引用
// const schoolListRef = ref<InstanceType<typeof SchoolList> | null>(null)

// 计算属性
const isNewClient = computed(() => route.path === '/clients/new')

// 状态定义
const personalInfoDialogVisible = ref(false)
const educationDialogVisible = ref(false)
const awardDialogVisible = ref(false)
const workDialogVisible = ref(false)
const academicDialogVisible = ref(false)
const activityDialogVisible = ref(false)
const skillDialogVisible = ref(false)
const languageScoreDialogVisible = ref(false)
const moduleManageDialogVisible = ref(false)
const isAddingNewModule = ref(false)
const editingIndex = ref(-1)
const editingForm = ref<Partial<ClientData & Education & Award & Work & Academic & Activity & Skill & LanguageScore>>({})
const newModuleForm = ref<NewModuleForm>({ title: '', icon: 'school' })
const moduleSortableContainer = ref<HTMLElement | null>(null)
let moduleSortableInstance: Sortable | null = null

// 添加方式选择相关状态
const addModeDialogVisible = ref(false)
const addModeType = ref<'academic' | 'work'>('academic')

// 智能上传相关状态 - 暂时注释掉
// const smartUploadDialogVisible = ref(false)
// const smartUploadType = ref<'academic' | 'work'>('academic')
// const uploadFileList = ref([])
// const uploadRef = ref()
// const extractedContent = ref('')
// const isExtracting = ref(false)

// 定校书相关状态
const addSchoolProgramDialogVisible = ref(false)
const schoolPrograms = ref<SchoolProgram[]>([])
const schoolBookPdfUrl = ref<string>('')

// 文书相关状态
const clientDocuments = ref([])
const documentsLoading = ref(false)
const documentFilter = ref('all')

// 使用全局logo缓存store
const logoStore = useSchoolLogosStore()




// 可用的图标
const availableIcons = [
  'school', 'science', 'work', 'sports_esports', 'emoji_events', 'psychology',
  'interests', 'volunteer_activism', 'public', 'diversity_3', 'groups',
  'shield', 'engineering', 'computer', 'language', 'biotech', 'theater_comedy',
  'music_note', 'palette', 'brush', 'sports_basketball', 'sports_soccer',
  'military_tech', 'hiking', 'star'
]

// 客户数据
const clientData = ref<ClientData & { customModules: Record<string, CustomModuleContent> }>({
  name: '',
  location: '',
  gender: '',
  phone: '',
  email: '',
  address: '',
  idCard: '',
  idCardIssuer: '',
  idCardValidity: '',
  passport: '',
  passportIssuePlace: '',
  passportIssueDate: '',
  passportExpiry: '',
  education: [],
  awards: [],
  work: [],
  academic: [],
  activities: [],
  skills: [],
  languageScores: [],
  customModules: {}
})

// 背景经历模块配置
const backgroundModules = ref<BackgroundModule[]>([
  { id: 'education', type: 'education', title: '教育经历', icon: 'school', visible: true, order: 0, isDefault: true },
  { id: 'academic', type: 'academic', title: '学术经历', icon: 'science', visible: true, order: 1, isDefault: true },
  { id: 'work', type: 'work', title: '工作经历', icon: 'work', visible: true, order: 2, isDefault: true },
  { id: 'activity', type: 'activity', title: '课外活动', icon: 'sports_esports', visible: true, order: 3, isDefault: true },
  { id: 'award', type: 'award', title: '奖项荣誉', icon: 'emoji_events', visible: true, order: 4, isDefault: true },
  { id: 'skill', type: 'skill', title: '相关技能', icon: 'psychology', visible: true, order: 5, isDefault: true }
])













// 整合后的onMounted生命周期函数
onMounted(async () => {
  // 确保至少有一个默认模块可见
  const hasVisibleDefaultModule = backgroundModules.value.some(m => m.isDefault && m.visible)
  if (!hasVisibleDefaultModule) {
    // 如果没有可见的默认模块，设置第一个默认模块为可见
    const firstDefaultModule = backgroundModules.value.find(m => m.isDefault)
    if (firstDefaultModule) {
      firstDefaultModule.visible = true
    }
  }

  nextTick(() => {
    initModuleSortable()
  })

  // 如果不是新建客户，获取客户详情
  if (!isNewClient.value) {
    // 使用fetchClientDetail获取客户详情，该函数会负责加载背景和想法模块
    await fetchClientDetail()
    
    // 从服务器获取客户的定校书数据
    try {
      const clientId = route.params.id as string
      const schoolProgramsResponse = await getClientPrograms(clientId)
      const schoolProgramsData = schoolProgramsResponse.data || schoolProgramsResponse
      
      // 转换定校书数据格式，使其符合前端组件的期望格式
      schoolPrograms.value = schoolProgramsData.map((cp: any) => ({
        id: cp.program_details?.id || cp.program_id,
        school_name_cn: cp.program_details?.school_name_cn || '',
        school_name_en: cp.program_details?.school_name_en || '',
        program_name_cn: cp.program_details?.program_name_cn || '',
        program_name_en: cp.program_details?.program_name_en || '',
        school_region: cp.program_details?.school_region || '',
        school_qs_rank: cp.program_details?.school_qs_rank || '',
        program_website: cp.program_details?.program_website || '',
        degree: cp.program_details?.degree || '',
        program_category: cp.program_details?.program_category || '',
        gpa_requirements: cp.program_details?.gpa_requirements || '',
        language_requirements: cp.program_details?.language_requirements || '',
        application_requirements: cp.program_details?.application_requirements || '',
        program_tuition: cp.program_details?.program_tuition || '',
        enrollment_time: cp.program_details?.enrollment_time || '',
        program_duration: cp.program_details?.program_duration || '',
        created_at: cp.created_at,
        client_program_id: cp.id  // 保存关联记录的ID，用于删除操作
      }))
      
      console.log('成功加载客户定校书数据:', schoolPrograms.value.length, '个专业')
    } catch (error) {
      console.error('获取客户定校书数据失败:', error)
      schoolPrograms.value = []
      ElMessage.warning('获取定校书数据失败')
    }

    // 加载客户文书数据
    await fetchClientDocuments()
  }
})

// 获取客户详情
const fetchClientDetail = async () => {
  if (isNewClient.value) return

  try {
    const clientId = route.params.id as string

    // 显示加载状态
    const loading = ElMessage({
      type: 'info',
      message: '加载客户详情...',
      duration: 1000
    })

    // 调用API获取客户详情
    const response = await getClientById(clientId)
    
    console.log('API响应:', response) // 调试日志

    // 更新客户基本信息
    const responseData = response.data || response;
    clientData.value = {
      name: responseData.name || '',
      location: responseData.location || '',
      gender: responseData.gender || 'other',
      phone: responseData.phone || '',
      email: responseData.email || '',
      address: responseData.address || '',
      // 身份证信息
      idCard: responseData.id_card || '',
      idCardIssuer: responseData.id_card_issuer || '',
      idCardValidity: responseData.id_card_validity || '',
      // 护照信息
      passport: responseData.passport || '',
      passportIssuePlace: responseData.passport_issue_place || '',
      passportIssueDate: responseData.passport_issue_date || '',
      passportExpiry: responseData.passport_expiry || '',
      // 时间信息
      created_at: responseData.created_at || '',
      updated_at: responseData.updated_at || '',
      // 其他信息
      education: responseData.education || [],
      awards: responseData.awards || [],
      work: responseData.work || [],
      academic: responseData.academic || [],
      activities: responseData.activities || [],
      skills: responseData.skills || [],
      languageScores: responseData.language_scores || [],
      customModules: {}
    }

    // 处理定校书数据
    if (responseData.client_programs && Array.isArray(responseData.client_programs)) {
      schoolPrograms.value = responseData.client_programs.map((cp: any) => ({
        id: cp.program_details?.id || cp.program_id,
        school_name_cn: cp.program_details?.school_name_cn || '',
        school_name_en: cp.program_details?.school_name_en || '',
        program_name_cn: cp.program_details?.program_name_cn || '',
        program_name_en: cp.program_details?.program_name_en || '',
        school_region: cp.program_details?.school_region || '',
        school_qs_rank: cp.program_details?.school_qs_rank || '',
        program_website: cp.program_details?.program_website || '',
        degree: cp.program_details?.degree || '',
        program_category: cp.program_details?.program_category || '',
        gpa_requirements: cp.program_details?.gpa_requirements || '',
        language_requirements: cp.program_details?.language_requirements || '',
        application_requirements: cp.program_details?.application_requirements || '',
        program_tuition: cp.program_details?.program_tuition || '',
        enrollment_time: cp.program_details?.enrollment_time || '',
        program_duration: cp.program_details?.program_duration || '',
        created_at: cp.created_at,
        client_program_id: cp.id  // 保存关联记录的ID，用于删除操作
      }))
      console.log('从客户详情中加载定校书数据:', schoolPrograms.value.length, '个专业')
    } else {
      schoolPrograms.value = []
    }

    // 清空现有模块
    // 保留默认模块，移除自定义模块
    backgroundModules.value = backgroundModules.value.filter(m => m.isDefault);

    // 处理背景自定义模块
    if (responseData.background_modules && Array.isArray(responseData.background_modules)) {
      responseData.background_modules.forEach((module: { id: number, title: string, content: string, icon: string, visible: boolean, order: number }) => {
        // 生成前端使用的模块ID
        const frontendId = `custom_${Date.now()}_${module.id}`

        // 保存模块内容
        clientData.value.customModules[frontendId] = {
          id: module.id,
          content: module.content || '',
          title: module.title,
          icon: module.icon || 'description',
          order: module.order,
          visible: module.visible !== undefined ? module.visible : true
        }

        // 保存ID映射
        moduleIdMap.value[frontendId] = module.id

        // 添加到backgroundModules
        backgroundModules.value.push({
          id: frontendId,
          type: 'custom',
          title: module.title,
          icon: module.icon || 'description',
          visible: module.visible !== undefined ? module.visible : true,
          order: module.order || backgroundModules.value.length,
          isDefault: false
        })
      })
    }

    // 根据order排序模块
    backgroundModules.value.sort((a, b) => (a.order || 0) - (b.order || 0))

    // 关闭加载提示
    loading.close()
    // ElMessage.success('客户详情加载成功')
  } catch (error) {
    console.error('API调用失败:', error)
    ElMessage.error('获取客户详情失败')
    console.error(error)
  }
}

// 监听模块管理弹窗的打开状态
watch(moduleManageDialogVisible, (newVal) => {
  if (newVal) {
    nextTick(() => {
      initModuleSortable()
    })
  } else {
    // 关闭弹窗时，销毁sortable实例
    if (moduleSortableInstance) {
      moduleSortableInstance.destroy()
      moduleSortableInstance = null
    }
  }
})

// 监听模块可见性的变化
watch(
  () => backgroundModules.value.map(m => m.visible),
  () => {
    // 确保至少有一个默认模块可见
    const visibleDefaultModules = backgroundModules.value.filter(m => m.isDefault && m.visible)
    if (visibleDefaultModules.length === 0) {
      // 如果没有可见的默认模块，将第一个默认模块设为可见
      const firstDefaultModule = backgroundModules.value.find(m => m.isDefault)
      if (firstDefaultModule) {
        firstDefaultModule.visible = true
        ElMessage.warning('至少需要保持一个默认模块可见')
      }
    }
  },
  { deep: true }
)

// 初始化拖拽排序实例
const initModuleSortable = () => {
  if (!moduleSortableContainer.value) return

  moduleSortableInstance = Sortable.create(moduleSortableContainer.value, {
    animation: 150,
    handle: '.cursor-move',
    ghostClass: 'bg-gray-100',
    onEnd: () => {
      // 获取重新排序后的ID顺序
      const ids = Array.from(moduleSortableContainer.value?.children || [])
        .map(el => el.getAttribute('data-id'))
        .filter(Boolean) as string[]

      // 更新模块顺序
      ids.forEach((id, index) => {
        const module = backgroundModules.value.find(m => m.id === id)
        if (module) {
          module.order = index
        }
      })

      // 按新顺序排序
      backgroundModules.value.sort((a, b) => a.order - b.order)
    }
  })
}

// 可见的排序过的背景模块
const visibleBackgroundModules = computed(() => {
  return [...backgroundModules.value]
    .filter(module => module.visible)
    .sort((a, b) => a.order - b.order)
})

// 定校书相关计算属性
const schoolProgramsByRegion = computed(() => {
  const grouped: Record<string, SchoolProgram[]> = {}
  schoolPrograms.value.forEach(program => {
    const region = program.school_region || '其他'
    if (!grouped[region]) {
      grouped[region] = []
    }
    grouped[region].push(program)
  })
  return grouped
})

const totalProgramsCount = computed(() => {
  return schoolPrograms.value.length
})

// 文书相关计算属性
const cvDocuments = computed(() => {
  return clientDocuments.value.filter(doc => doc.type === 'CV')
})

const rlDocuments = computed(() => {
  return clientDocuments.value.filter(doc => doc.type === 'RL')
})

const filteredDocuments = computed(() => {
  if (documentFilter.value === 'all') {
    return clientDocuments.value
  }
  return clientDocuments.value.filter(doc => doc.type === documentFilter.value)
})

// PS相关的学校项目数据
const psPrograms = computed(() => {
  return schoolPrograms.value.map(program => ({
    ...program,
    logo: program.program_details?.school_logo_url || logoStore.getSchoolLogo(program.school_name_cn),
    created_at: program.created_at || new Date().toISOString()
  }))
})

// 添加方式选择弹窗标题
const addModeTitle = computed(() => {
  return addModeType.value === 'academic' ? '添加学术经历' : '添加工作经历'
})

// 智能上传弹窗标题 - 暂时注释掉
// const smartUploadTitle = computed(() => {
//   return smartUploadType.value === 'academic' ? '智能添加学术经历' : '智能添加工作经历'
// })



// 日期格式化
const formatDate = (date: string) => {
  if (!date) return ''
  return dayjs(date).tz('Asia/Shanghai').format('YYYY-MM-DD')
}

// 详细时间格式化（包含时分秒）
const formatDateTime = (date: string) => {
  if (!date) return ''
  return dayjs(date).tz('Asia/Shanghai').format('YYYY/MM/DD HH:mm:ss')
}

// 新增：对经历进行排序（最新的在前）
const sortExperiences = (experiences: any[]) => {
  if (!Array.isArray(experiences) || experiences.length === 0) {
    return;
  }

  // 确定用于排序的日期字段名 ('date' for academic, 'start_date' for work)
  const dateKey = experiences[0].hasOwnProperty('start_date') ? 'start_date' : 'date';

  experiences.sort((a, b) => {
    // 将日期字符串转换为Date对象进行比较
    const dateA = a[dateKey] ? new Date(a[dateKey]) : null;
    const dateB = b[dateKey] ? new Date(b[dateKey]) : null;

    // 将没有日期的项排在最后
    if (!dateA && !dateB) return 0;
    if (!dateA) return 1; // a 在后
    if (!dateB) return -1; // b 在后

    // 按降序排序 (日期越大越新，排在越前)
    return dateB.getTime() - dateA.getTime();
  });
};

const languageTypeMap: { [key: string]: string } = {
  toefl: 'TOEFL',
  ielts: 'IELTS',
  gre: 'GRE',
  gmat: 'GMAT',
  sat: 'SAT',
  act: 'ACT',
  duolingo: 'Duolingo',
  jlpt: 'JLPT',
  hsk: 'HSK',
  dele: 'DELE',
  other: '其他'
}

// 标签页配置
const tabs = [
  { key: 'backgrounds', label: '背景经历', icon: 'history_edu' },
  { key: 'identifications', label: '身份证件', icon: 'badge' },
  { key: 'materials', label: '申请材料', icon: 'folder_shared' },
  { key: 'schools', label: '定校书', icon: 'school' },
  { key: 'documents', label: '客户文书', icon: 'description' }
]

const activeTab = ref('backgrounds')

// 路由处理
const handleTabClick = (tabKey: string) => {
  activeTab.value = tabKey
  const query = { ...route.query, tab: tabKey }
  router.push({ query })
}

// 监听路由变化
watch(
  () => route.query.tab,
  (newTab) => {
    if (newTab && typeof newTab === 'string') {
      activeTab.value = newTab
    }
  },
  { immediate: true }
)

// 模块管理方法
const handleManageModules = () => {
  moduleManageDialogVisible.value = true
}

const handleToggleModuleVisibility = (moduleId: string) => {
  const module = backgroundModules.value.find(m => m.id === moduleId)
  if (!module) return

  // 状态在开关更改时已经改变，所以这里要判断的是新状态
  const newVisibleState = module.visible

  // 如果尝试隐藏默认模块，检查是否至少有一个其他默认模块可见
  if (module.isDefault && !newVisibleState) {
    const visibleDefaultModules = backgroundModules.value.filter(
      m => m.isDefault && m.visible && m.id !== moduleId
    )
    if (visibleDefaultModules.length === 0) {
      ElMessage.warning('至少需要保持一个默认模块可见')
      // 恢复可见状态
      module.visible = true
      return
    }
  }
}



// 模块管理弹窗
const handleAddNewModule = () => {
  isAddingNewModule.value = true
  newModuleForm.value = {
    title: '',
    icon: 'school'
  }
}

const handleConfirmAddModule = async () => {
  if (!newModuleForm.value.title.trim()) {
    ElMessage.warning('请输入模块名称')
    return
  }
  const newId = `custom_${Date.now()}`
  const newModule: BackgroundModule = {
    id: newId,
    type: 'custom',
    title: newModuleForm.value.title,
    icon: newModuleForm.value.icon,
    visible: true,
    order: backgroundModules.value.length,
    isDefault: false // 新增模块设置为非默认
  }

  // 先添加到本地
  backgroundModules.value.push(newModule)
  clientData.value.customModules[newId] = { content: '' }

  // 如果非新建客户，则创建自定义模块到服务器
  if (!isNewClient.value) {
    try {
      const clientId = route.params.id as string
      const customModuleData = {
        module_id: newId,
        title: newModule.title,
        type: 'custom',
        content: '',
        icon: newModule.icon,
        visible: true,
        order: newModule.order
      }

      // 使用变量存储loading消息，便于后续关闭
      let loadingInstance = ElMessage({
        type: 'info',
        message: '正在保存模块...',
        duration: 0  // 设置为0表示不自动关闭
      })

      try {
        // 使用新的背景模块API
        const response = await addBackgroundModule(clientId, customModuleData)

        // 先关闭loading消息
        loadingInstance.close()

        // 更新本地数据，保存服务器返回的ID
        if (response && response.id) {
          clientData.value.customModules[newId].id = response.id
          // 保存ID映射
          moduleIdMap.value[newId] = response.id
        }

        // 显示成功消息
        // ElMessage.success('添加模块成功')
      } catch (err) {
        // 先关闭loading消息
        loadingInstance.close()

        console.error('保存背景自定义模块失败:', err)
        ElMessage.warning({
          message: '模块已添加但未能同步到服务器',
          duration: 3000
        })
      }
    } catch (error) {
      console.error('创建背景模块过程中发生错误:', error)
      ElMessage.error({
        message: '添加模块失败',
        duration: 3000
      })
    }
  } else {
    // ElMessage.success('添加模块成功')
  }

  isAddingNewModule.value = false
}

const handleModuleTitleChange = (moduleId: string, newTitle: string) => {
  const moduleIndex = backgroundModules.value.findIndex(m => m.id === moduleId)
  if (moduleIndex !== -1 && newTitle.trim()) {
    backgroundModules.value[moduleIndex].title = newTitle
  }
}

const handleDeleteModule = async (moduleId: string) => {
  const module = backgroundModules.value.find(m => m.id === moduleId)
  if (!module) return

  if (module.isDefault) {
    ElMessage.warning('默认模块不能删除')
    return
  }

  const moduleIndex = backgroundModules.value.findIndex(m => m.id === moduleId)
  if (moduleIndex !== -1) {
    // 如果不是新建客户，且是自定义模块，则从服务器中删除
    if (!isNewClient.value && !module.isDefault) {
      try {
        const clientId = route.params.id as string
        // 使用变量存储loading消息，便于后续关闭
        let loadingInstance = ElMessage({
          type: 'info',
          message: '正在删除模块...',
          duration: 0  // 设置为0表示不自动关闭
        })

        try {
          // 使用服务器返回的真实ID而不是前端生成的ID
          const serverId = clientData.value.customModules[moduleId]?.id || moduleIdMap.value[moduleId]
          if (serverId) {
            // 使用背景模块API删除
            await deleteBackgroundModule(clientId, serverId)

            // 从本地删除自定义模块内容
            if (clientData.value.customModules[moduleId]) {
              delete clientData.value.customModules[moduleId]
            }

            // 从本地删除模块配置
            backgroundModules.value.splice(moduleIndex, 1)

            // 重新调整其他模块的顺序
            backgroundModules.value.forEach((module, index) => {
              module.order = index
            })

            loadingInstance.close()
            // ElMessage.success('删除模块成功')
          } else {
            throw new Error('找不到模块的服务器ID')
          }
        } catch (err) {
          loadingInstance.close()
          // 处理删除失败
          ElMessage.error('删除失败')
        }
      } catch (error) {
        // 处理删除过程中的错误
        ElMessage.error('删除失败，请重试')
      }
    } else {
      // 新建客户或默认模块，只从本地删除
      backgroundModules.value.splice(moduleIndex, 1)
      backgroundModules.value.forEach((module, index) => {
        module.order = index
      })
      // ElMessage.success('删除模块成功')
    }
  }
}



// 个人信息编辑
const handleEditPersonalInfo = () => {
  editingForm.value = {
    name: clientData.value.name,
    location: clientData.value.location,
    gender: clientData.value.gender
  }
  personalInfoDialogVisible.value = true
}

const handleSavePersonalInfo = async () => {
  // 表单验证
  if (!editingForm.value.name?.trim()) {
    ElMessage.warning('请输入客户姓名')
    return
  }

  // 更新本地数据
  if (editingForm.value.name) clientData.value.name = editingForm.value.name
  if (editingForm.value.location) clientData.value.location = editingForm.value.location
  if (editingForm.value.gender) clientData.value.gender = editingForm.value.gender

  // 如果是新客户，不需要调用API
  if (isNewClient.value) {
    personalInfoDialogVisible.value = false
    // ElMessage.success('保存成功')
    return
  }

  // 如果是已有客户，调用API更新
  const clientId = route.params.id as string

  try {
    // 显示加载状态
    const loading = ElMessage({
      type: 'info',
      message: '正在保存...',
      duration: 5000 // 修改为5000ms
    })

    // 准备要更新的数据
    const updateData = {
      name: clientData.value.name,
      location: clientData.value.location,
      gender: clientData.value.gender
    }

    // 调用API更新客户信息
    await updateClient(clientId, updateData)

    // 关闭加载提示
    loading.close()
    personalInfoDialogVisible.value = false
    // ElMessage.success('保存成功')
  } catch (error) {
    // 处理更新失败

    ElMessage.error('保存失败')
  }
}

// 联系方式编辑
const handleEditContact = () => {
  editingForm.value = {
    phone: clientData.value.phone,
    email: clientData.value.email
  }
  contactDialogVisible.value = true
}

const contactDialogVisible = ref(false)

const handleSaveContact = async () => {
  // 更新本地数据
  if (editingForm.value.phone) clientData.value.phone = editingForm.value.phone
  if (editingForm.value.email) clientData.value.email = editingForm.value.email

  // 如果是新客户，不需要调用API
  if (isNewClient.value) {
    contactDialogVisible.value = false
    // ElMessage.success('保存成功')
    return
  }

  // 如果是已有客户，调用API更新
  const clientId = route.params.id as string

  try {
    // 显示加载状态
    const loading = ElMessage({
      type: 'info',
      message: '正在保存...',
      duration: 5000 // 修改为5000ms
    })

    // 准备要更新的数据
    const updateData = {
      phone: clientData.value.phone,
      email: clientData.value.email
    }

    // 调用API更新客户信息
    await updateClient(clientId, updateData)

    // 关闭加载提示
    loading.close()
    contactDialogVisible.value = false
    // ElMessage.success('保存成功')
  } catch (error) {
    // 处理更新失败
    ElMessage.error('保存失败')
  }
}

const addressDialogVisible = ref(false)

const handleSaveAddress = async () => {
  // 更新本地数据
  if (editingForm.value.address !== undefined) clientData.value.address = editingForm.value.address
  if (editingForm.value.location !== undefined) clientData.value.location = editingForm.value.location

  // 如果是新客户，不需要调用API
  if (isNewClient.value) {
    addressDialogVisible.value = false
    // ElMessage.success('保存成功')
    return
  }

  // 如果是已有客户，调用API更新
  const clientId = route.params.id as string

  try {
    // 显示加载状态
    const loading = ElMessage({
      type: 'info',
      message: '正在保存...',
      duration: 5000 // 修改为5000ms
    })

    // 准备要更新的数据
    const updateData = {
      address: clientData.value.address,
      location: clientData.value.location
    }

    // 调用API更新客户信息
    await updateClient(clientId, updateData)

    // 关闭加载提示
    loading.close()
    addressDialogVisible.value = false
    // ElMessage.success('保存成功')
  } catch (error) {
    // 处理更新失败

    ElMessage.error('保存失败')
  }
}

// 语言成绩编辑
// 此函数已被handleEditLanguageScore替代，保留注释以便理解

const handleEditLanguageScore = (index = -1) => {
  editingIndex.value = index
  if (index === -1) {
    // 新增语言成绩
    editingForm.value = {
      type: '',
      score: '',
      date: '',
      validity: ''
    }
  } else {
    // 编辑现有语言成绩
    const score = clientData.value.languageScores[index]
    editingForm.value = { ...score }
  }
  languageScoreDialogVisible.value = true
}

const handleDeleteLanguageScore = async (index: number) => {
  if (isNewClient.value) {
    // 如果是新客户，直接从本地数据中删除
    clientData.value.languageScores.splice(index, 1)
    // ElMessage.success('删除成功')
    return
  }

  const clientId = route.params.id as string
  const languageScore = clientData.value.languageScores[index]

  // 如果没有ID，说明是本地添加的，还没有保存到后端
  if (!languageScore.id) {
    clientData.value.languageScores.splice(index, 1)
    // ElMessage.success('删除成功')
    return
  }

  try {
    // 显示加载状态
    const loading = ElMessage({
      type: 'info',
      message: '正在删除...',
      duration: 1000
    })

    // 调用API删除语言成绩
    await deleteLanguageScore(clientId, languageScore.id)

    // 从本地数据中删除
    clientData.value.languageScores.splice(index, 1)

    // 关闭加载提示
    loading.close()
    // ElMessage.success('删除成功')
  } catch (error) {
    // 处理删除失败

    ElMessage.error('删除失败')
  }
}

const handleSaveLanguageScore = async () => {
  // 表单验证
  if (!editingForm.value.type?.trim()) {
    ElMessage.warning('请选择语言考试类型')
    return
  }

  if (!editingForm.value.score?.trim()) {
    ElMessage.warning('请输入考试分数')
    return
  }

  const languageScore = editingForm.value as LanguageScore

  if (isNewClient.value) {
    // 如果是新客户，直接添加到本地数据中
    if (editingIndex.value === -1) {
      clientData.value.languageScores.push({...languageScore})
    } else {
      clientData.value.languageScores[editingIndex.value] = {...languageScore}
    }
    languageScoreDialogVisible.value = false
    // ElMessage.success('保存成功')
    return
  }

  const clientId = route.params.id as string

  try {
    // 显示加载状态
    const loading = ElMessage({
      type: 'info',
      message: '正在保存...',
      duration: 5000 // 修改为5000ms
    })

    let response: LanguageScore
    if (editingIndex.value === -1) {
      // 添加新语言成绩
      response = await addLanguageScore(clientId, languageScore)
      // 确保clientData.value.languageScores是一个数组
      if (!Array.isArray(clientData.value.languageScores)) {
        clientData.value.languageScores = []
      }
      clientData.value.languageScores = [...clientData.value.languageScores, response]
    } else {
      // 更新语言成绩
      const currentLanguageScore = clientData.value.languageScores[editingIndex.value]
      // 检查是否存在currentLanguageScore以及是否有id
      if (!currentLanguageScore || !currentLanguageScore.id) {
        // 如果没有id，说明是新添加的
        response = await addLanguageScore(clientId, languageScore)
        clientData.value.languageScores = [
          ...clientData.value.languageScores.slice(0, editingIndex.value),
          response,
          ...clientData.value.languageScores.slice(editingIndex.value + 1)
        ]
      } else {
        // 如果有id，则更新
        response = await updateLanguageScore(clientId, currentLanguageScore.id, languageScore)
        clientData.value.languageScores = [
          ...clientData.value.languageScores.slice(0, editingIndex.value),
          response,
          ...clientData.value.languageScores.slice(editingIndex.value + 1)
        ]
      }
    }

    // 关闭加载提示
    loading.close()
    languageScoreDialogVisible.value = false
    // ElMessage.success('保存成功')
  } catch (error) {
    // 处理保存失败
    ElMessage.error('保存失败')
  }
}

// 身份证件编辑
const handleEditIdCard = () => {
  editingForm.value = {
    idCard: clientData.value.idCard,
    idCardIssuer: clientData.value.idCardIssuer,
    idCardValidity: clientData.value.idCardValidity
  }
  idCardDialogVisible.value = true
}

// 保存身份证件
const handleSaveIdCard = async () => {
  // 更新本地数据
  if (editingForm.value.idCard !== undefined) clientData.value.idCard = editingForm.value.idCard
  if (editingForm.value.idCardIssuer !== undefined) clientData.value.idCardIssuer = editingForm.value.idCardIssuer
  if (editingForm.value.idCardValidity !== undefined) clientData.value.idCardValidity = editingForm.value.idCardValidity

  // 如果是新客户，不需要调用API
  if (isNewClient.value) {
    idCardDialogVisible.value = false
    // ElMessage.success('保存成功')
    return
  }

  const clientId = route.params.id as string

  try {
    // 显示加载状态
    const loading = ElMessage({
      type: 'info',
      message: '正在保存...',
      duration: 5000 // 修改为5000ms
    })

    // 准备要更新的数据
    const updateData = {
      id_card: clientData.value.idCard,
      id_card_issuer: clientData.value.idCardIssuer,
      id_card_validity: clientData.value.idCardValidity
    }

    // 调用API更新客户信息
    await updateClient(clientId, updateData)

    // 关闭加载提示
    loading.close()
    idCardDialogVisible.value = false
    // ElMessage.success('保存成功')
  } catch (error) {
    // 处理更新失败
    ElMessage.error('保存失败')
  }
}

// 教育经历编辑
const handleAddEducation = () => {
  editingIndex.value = -1
  editingForm.value = {
    school: '',
    major: '',
    degree: '',
    gpa: '',
    start_date: '',
    end_date: '',
    description: ''
  }
  educationDialogVisible.value = true
}

const handleEditEducation = (index: number) => {
  editingIndex.value = index
  const education = clientData.value.education[index]
  editingForm.value = { ...education }
  educationDialogVisible.value = true
}

const handleDeleteEducation = async (index: number) => {
  if (isNewClient.value) {
    // 如果是新客户，直接从本地数据中删除
    clientData.value.education.splice(index, 1)
    // ElMessage.success('删除成功')
    return
  }

  const clientId = route.params.id as string
  const education = clientData.value.education[index]

  // 如果没有ID，说明是本地添加的，还没有保存到后端
  if (!education.id) {
    clientData.value.education.splice(index, 1)
    // ElMessage.success('删除成功')
    return
  }

  try {
    // 显示加载状态
    ElMessage({
      type: 'info',
      message: '正在删除...',
      duration: 1000
    })

    // 调用API删除教育经历
    await deleteEducation(clientId, education.id)

    // 从本地数据中删除
    clientData.value.education.splice(index, 1)
    // ElMessage.success('删除成功')
  } catch (error) {
    // 处理删除失败

    ElMessage.error('删除失败')
  }
}

const handleSaveEducation = async () => {
  // 表单验证
  if (!editingForm.value.school?.trim()) {
    ElMessage.warning('请输入学校名称')
    return
  }

  if (!editingForm.value.major?.trim()) {
    ElMessage.warning('请输入专业名称')
    return
  }

  if (!editingForm.value.degree?.trim()) {
    ElMessage.warning('请选择学位类型')
    return
  }

  const education = editingForm.value as Education

  if (isNewClient.value) {
    // 如果是新客户，直接添加到本地数据中
    if (editingIndex.value === -1) {
      clientData.value.education.push(education)
    } else {
      clientData.value.education[editingIndex.value] = education
    }
    educationDialogVisible.value = false
    // ElMessage.success('保存成功')
    return
  }

  const clientId = route.params.id as string

  try {
    // 显示加载状态
    const loading = ElMessage({
      type: 'info',
      message: '正在保存...',
      duration: 5000 // 修改为5000ms
    })

    if (editingIndex.value === -1) {
      // 添加新教育经历
      const response = await addEducation(clientId, education)

      // 使用返回的数据更新本地数据
      clientData.value.education.push(response)
    } else {
      // 更新教育经历
      const currentEducation = clientData.value.education[editingIndex.value]

      // 如果没有ID，说明是本地添加的，还没有保存到后端
      if (!currentEducation.id) {
        const response = await addEducation(clientId, education)
        clientData.value.education[editingIndex.value] = response
      } else {
        const response = await updateEducation(clientId, currentEducation.id, education)
        clientData.value.education[editingIndex.value] = response
      }
    }

    // 关闭加载提示
    loading.close()
    educationDialogVisible.value = false
    // ElMessage.success('保存成功')
  } catch (error) {
    // 处理保存失败

    ElMessage.error('保存失败')
  }
}

// 学术经历添加
const handleAddAcademic = () => {
  // 直接进入手动添加模式
  handleAddAcademicManual()
}

// 直接打开学术经历编辑弹窗（手动添加）
const handleAddAcademicManual = () => {
  editingIndex.value = -1
  editingForm.value = {
    title: '',
    type: '', // 修改：原institution字段
    date: '',
    description: ''
  }
  academicDialogVisible.value = true
}

const handleEditAcademic = (index: number) => {
  editingIndex.value = index
  const academic = clientData.value.academic[index]
  editingForm.value = { ...academic } // Academic 接口已更新，这里会自动包含 type
  academicDialogVisible.value = true
}

const handleDeleteAcademic = async (index: number) => {
  if (isNewClient.value) {
    // 如果是新客户，直接从本地数据中删除
    clientData.value.academic.splice(index, 1)
    // ElMessage.success('删除成功')
    return
  }

  const clientId = route.params.id as string
  const academic = clientData.value.academic[index]

  // 如果没有ID，说明是本地添加的，还没有保存到后端
  if (!academic.id) {
    clientData.value.academic.splice(index, 1)
    // ElMessage.success('删除成功')
    return
  }

  try {
    // 显示加载状态
    const loading = ElMessage({
      type: 'info',
      message: '正在删除...',
      duration: 1000
    })

    // 调用API删除学术经历
    await deleteAcademic(clientId, academic.id)

    // 从本地数据中删除
    clientData.value.academic.splice(index, 1)

    // 关闭加载提示
    loading.close()
    // ElMessage.success('删除成功')
  } catch (error) {
    // 处理删除失败

    ElMessage.error('删除失败')
  }
}

const handleSaveAcademic = async () => {
  // 表单验证
  if (!editingForm.value.title?.trim()) {
    ElMessage.warning('请输入学术经历标题')
    return
  }

  if (!editingForm.value.type?.trim()) { // 修改：验证 type
    ElMessage.warning('请选择或输入学术类型') // 修改：提示信息
    return
  }

  const academic = editingForm.value as Academic // Academic 接口已更新

  if (isNewClient.value) {
    // 如果是新客户，直接添加到本地数据中
    if (editingIndex.value === -1) {
      clientData.value.academic.push(academic)
    } else {
      clientData.value.academic[editingIndex.value] = academic
    }
    academicDialogVisible.value = false
    // ElMessage.success('保存成功')
    return
  }

  const clientId = route.params.id as string

  try {
    // 显示加载状态
    const loading = ElMessage({
      type: 'info',
      message: '正在保存...',
      duration: 5000 // 修改为5000ms
    })

    if (editingIndex.value === -1) {
      // 添加新学术经历
      const response = await addAcademic(clientId, academic)

      // 使用返回的数据更新本地数据
      clientData.value.academic.push(response)
    } else {
      // 更新学术经历
      const currentAcademic = clientData.value.academic[editingIndex.value]

      // 如果没有ID，说明是本地添加的，还没有保存到后端
      if (!currentAcademic.id) {
        const response = await addAcademic(clientId, academic)
        clientData.value.academic[editingIndex.value] = response
      } else {
        const response = await updateAcademic(clientId, currentAcademic.id, academic)
        clientData.value.academic[editingIndex.value] = response
      }
    }

    // 关闭加载提示
    loading.close()
    academicDialogVisible.value = false
    // ElMessage.success('保存成功')
  } catch (error) {
    // 处理保存失败

    ElMessage.error('保存失败')
  }
}

// 课外活动编辑
const handleAddActivity = () => {
  editingIndex.value = -1
  editingForm.value = {
    name: '',
    role: '',
    start_date: '',
    end_date: '',
    description: ''
  }
  activityDialogVisible.value = true
}

const handleEditActivity = (index: number) => {
  editingIndex.value = index
  const activity = clientData.value.activities[index]
  editingForm.value = { ...activity }
  activityDialogVisible.value = true
}

const handleDeleteActivity = async (index: number) => {
  if (isNewClient.value) {
    // 如果是新客户，直接从本地数据中删除
    clientData.value.activities.splice(index, 1)
    // ElMessage.success('删除成功')
    return
  }

  const clientId = route.params.id as string
  const activity = clientData.value.activities[index]

  // 如果没有ID，说明是本地添加的，还没有保存到后端
  if (!activity.id) {
    clientData.value.activities.splice(index, 1)
    // ElMessage.success('删除成功')
    return
  }

  try {
    // 显示加载状态
    const loading = ElMessage({
      type: 'info',
      message: '正在删除...',
      duration: 1000
    })

    // 调用API删除活动经历
    await deleteActivity(clientId, activity.id)

    // 从本地数据中删除
    clientData.value.activities.splice(index, 1)

    // 关闭加载提示
    loading.close()
    // ElMessage.success('删除成功')
  } catch (error) {
    // 处理删除失败

    ElMessage.error('删除失败')
  }
}

const handleSaveActivity = async () => {
  // 表单验证
  if (!editingForm.value.name?.trim()) {
    ElMessage.warning('请输入活动名称')
    return
  }

  if (!editingForm.value.role?.trim()) {
    ElMessage.warning('请输入担任角色')
    return
  }

  const activity = editingForm.value as Activity

  if (isNewClient.value) {
    // 如果是新客户，直接添加到本地数据中
    if (editingIndex.value === -1) {
      clientData.value.activities.push(activity)
    } else {
      clientData.value.activities[editingIndex.value] = activity
    }
    activityDialogVisible.value = false
    // ElMessage.success('保存成功')
    return
  }

  const clientId = route.params.id as string

  try {
    // 显示加载状态
    const loading = ElMessage({
      type: 'info',
      message: '正在保存...',
      duration: 5000 // 修改为5000ms
    })

    if (editingIndex.value === -1) {
      // 添加新活动经历
      const response = await addActivity(clientId, activity)

      // 使用返回的数据更新本地数据
      clientData.value.activities.push(response)
    } else {
      // 更新活动经历
      const currentActivity = clientData.value.activities[editingIndex.value]

      // 如果没有ID，说明是本地添加的，还没有保存到后端
      if (!currentActivity.id) {
        const response = await addActivity(clientId, activity)
        clientData.value.activities[editingIndex.value] = response
      } else {
        const response = await updateActivity(clientId, currentActivity.id, activity)
        clientData.value.activities[editingIndex.value] = response
      }
    }

    // 关闭加载提示
    loading.close()
    activityDialogVisible.value = false
    // ElMessage.success('保存成功')
  } catch (error) {
    // 处理保存失败

    ElMessage.error('保存失败')
  }
}

// 奖项荣誉编辑
const handleAddAward = () => {
  editingIndex.value = -1
  editingForm.value = {
    name: '',
    level: '',
    date: '',
    description: ''
  }
  awardDialogVisible.value = true
}

const handleEditAward = (index: number) => {
  editingIndex.value = index
  const award = clientData.value.awards[index]
  editingForm.value = { ...award }
  awardDialogVisible.value = true
}

const handleDeleteAward = async (index: number) => {
  if (isNewClient.value) {
    // 如果是新客户，直接从本地数据中删除
    clientData.value.awards.splice(index, 1)
    // ElMessage.success('删除成功')
    return
  }

  const clientId = route.params.id as string
  const award = clientData.value.awards[index]

  // 如果没有ID，说明是本地添加的，还没有保存到后端
  if (!award.id) {
    clientData.value.awards.splice(index, 1)
    // ElMessage.success('删除成功')
    return
  }

  try {
    // 显示加载状态
    const loading = ElMessage({
      type: 'info',
      message: '正在删除...',
      duration: 1000
    })

    // 调用API删除奖项
    await deleteAward(clientId, award.id)

    // 从本地数据中删除
    clientData.value.awards.splice(index, 1)

    // 关闭加载提示
    loading.close()
    // ElMessage.success('删除成功')
  } catch (error) {
    console.error('删除奖项失败:', error)

    ElMessage.error('删除失败')
  }
}

const handleSaveAward = async () => {
  // 表单验证
  if (!editingForm.value.name?.trim()) {
    ElMessage.warning('请输入奖项名称')
    return
  }

  if (!editingForm.value.level?.trim()) {
    ElMessage.warning('请输入奖项级别')
    return
  }

  const award = editingForm.value as Award

  if (isNewClient.value) {
    // 如果是新客户，直接添加到本地数据中
    if (editingIndex.value === -1) {
      clientData.value.awards.push(award)
    } else {
      clientData.value.awards[editingIndex.value] = award
    }
    awardDialogVisible.value = false
    // ElMessage.success('保存成功')
    return
  }

  const clientId = route.params.id as string

  try {
    // 显示加载状态
    const loading = ElMessage({
      type: 'info',
      message: '正在保存...',
      duration: 5000 // 修改为5000ms
    })

    if (editingIndex.value === -1) {
      // 添加新奖项
      const response = await addAward(clientId, award)

      // 使用返回的数据更新本地数据
      clientData.value.awards.push(response)
    } else {
      // 更新奖项
      const currentAward = clientData.value.awards[editingIndex.value]

      // 如果没有ID，说明是本地添加的，还没有保存到后端
      if (!currentAward.id) {
        const response = await addAward(clientId, award)
        clientData.value.awards[editingIndex.value] = response
      } else {
        const response = await updateAward(clientId, currentAward.id, award)
        clientData.value.awards[editingIndex.value] = response
      }
    }

    // 关闭加载提示
    loading.close()
    awardDialogVisible.value = false
    // ElMessage.success('保存成功')
  } catch (error) {
    console.error('保存奖项失败:', error)

    ElMessage.error('保存失败')
  }
}

// 相关技能编辑
const handleAddSkill = () => {
  editingIndex.value = -1
  editingForm.value = {
    type: '', // 修改：初始化type
    description: '' // 修改：初始化description
  }
  skillDialogVisible.value = true
}

const handleEditSkill = (index: number) => {
  editingIndex.value = index
  const skill = clientData.value.skills[index]
  editingForm.value = { ...skill } // Skill结构已更新，这里依然适用
  skillDialogVisible.value = true
}

const handleDeleteSkill = async (index: number) => {
  if (isNewClient.value) {
    // 如果是新客户，直接从本地数据中删除
    clientData.value.skills.splice(index, 1)
    // ElMessage.success('删除成功')
    return
  }

  const clientId = route.params.id as string
  const skill = clientData.value.skills[index]

  // 如果没有ID，说明是本地添加的，还没有保存到后端
  if (!skill.id) {
    clientData.value.skills.splice(index, 1)
    // ElMessage.success('删除成功')
    return
  }

  try {
    // 显示加载状态
    const loading = ElMessage({
      type: 'info',
      message: '正在删除...',
      duration: 1000
    })

    // 调用API删除技能
    await deleteSkill(clientId, skill.id)

    // 从本地数据中删除
    clientData.value.skills.splice(index, 1)

    // 关闭加载提示
    loading.close()
    // ElMessage.success('删除成功')
  } catch (error) {
    console.error('删除技能失败:', error)

    ElMessage.error('删除失败')
  }
}

const handleSaveSkill = async () => {
  // 表单验证
  if (!editingForm.value.type?.trim()) { // 修改：验证type
    ElMessage.warning('请选择或输入技能类型')
    return
  }

  if (!editingForm.value.description?.trim()) { // 修改：验证description
    ElMessage.warning('请输入技能描述')
    return
  }

  const skill = editingForm.value as Skill // Skill结构已更新

  if (isNewClient.value) {
    // 如果是新客户，直接添加到本地数据中
    if (editingIndex.value === -1) {
      clientData.value.skills.push(skill)
    } else {
      clientData.value.skills[editingIndex.value] = skill
    }
    skillDialogVisible.value = false
    // ElMessage.success('保存成功')
    return
  }

  const clientId = route.params.id as string

  try {
    // 显示加载状态
    const loading = ElMessage({
      type: 'info',
      message: '正在保存...',
      duration: 5000 // 修改为5000ms
    })

    if (editingIndex.value === -1) {
      // 添加新技能
      const response = await addSkill(clientId, skill) // API将接收新的skill结构
      clientData.value.skills.push(response)
    } else {
      // 更新技能
      const currentSkill = clientData.value.skills[editingIndex.value]
      if (!currentSkill.id) {
        const response = await addSkill(clientId, skill)
        clientData.value.skills[editingIndex.value] = response
      } else {
        const response = await updateSkill(clientId, currentSkill.id, skill) // API将接收新的skill结构
        clientData.value.skills[editingIndex.value] = response
      }
    }

    loading.close()
    skillDialogVisible.value = false
    // ElMessage.success('保存成功')
  } catch (error) {
    console.error('保存技能失败:', error)
    ElMessage.error('保存失败')
  }
}

// 工作经历添加
const handleAddWork = () => {
  // 直接进入手动添加模式
  handleAddWorkManual()
}

// 直接打开工作经历编辑弹窗（手动添加）
const handleAddWorkManual = () => {
  editingIndex.value = -1
  editingForm.value = {
    company: '',
    position: '',
    start_date: '',
    end_date: '',
    description: ''
  }
  workDialogVisible.value = true
}

const handleEditWork = (index: number) => {
  editingIndex.value = index
  const work = clientData.value.work[index]
  editingForm.value = { ...work }
  workDialogVisible.value = true
}

const handleDeleteWork = async (index: number) => {
  if (isNewClient.value) {
    // 如果是新客户，直接从本地数据中删除
    clientData.value.work.splice(index, 1)
    // ElMessage.success('删除成功')
    return
  }

  const clientId = route.params.id as string
  const work = clientData.value.work[index]

  // 如果没有ID，说明是本地添加的，还没有保存到后端
  if (!work.id) {
    clientData.value.work.splice(index, 1)
    // ElMessage.success('删除成功')
    return
  }

  try {
    // 显示加载状态
    const loading = ElMessage({
      type: 'info',
      message: '正在删除...',
      duration: 1000
    })

    // 调用API删除工作经历
    await deleteWork(clientId, work.id)

    // 从本地数据中删除
    clientData.value.work.splice(index, 1)

    // 关闭加载提示
    loading.close()
    // ElMessage.success('删除成功')
  } catch (error) {
    console.error('删除工作经历失败:', error)

    ElMessage.error('删除失败')
  }
}

const handleSaveWork = async () => {
  // 表单验证
  if (!editingForm.value.company?.trim()) {
    ElMessage.warning('请输入公司名称')
    return
  }

  if (!editingForm.value.position?.trim()) {
    ElMessage.warning('请输入职位名称')
    return
  }

  const work = editingForm.value as Work

  if (isNewClient.value) {
    // 如果是新客户，直接添加到本地数据中
    if (editingIndex.value === -1) {
      clientData.value.work.push(work)
    } else {
      clientData.value.work[editingIndex.value] = work
    }
    workDialogVisible.value = false
    // ElMessage.success('保存成功')
    return
  }

  const clientId = route.params.id as string

  try {
    // 显示加载状态
    const loading = ElMessage({
      type: 'info',
      message: '正在保存...',
      duration: 5000 // 修改为5000ms
    })

    if (editingIndex.value === -1) {
      // 添加新工作经历
      const response = await addWork(clientId, work)

      // 使用返回的数据更新本地数据
      clientData.value.work.push(response)
    } else {
      // 更新工作经历
      const currentWork = clientData.value.work[editingIndex.value]

      // 如果没有ID，说明是本地添加的，还没有保存到后端
      if (!currentWork.id) {
        const response = await addWork(clientId, work)
        clientData.value.work[editingIndex.value] = response
      } else {
        const response = await updateWork(clientId, currentWork.id, work)
        clientData.value.work[editingIndex.value] = response
      }
    }

    // 关闭加载提示
    loading.close()
    workDialogVisible.value = false
    // ElMessage.success('保存成功')
  } catch (error) {
    console.error('保存工作经历失败:', error)

    ElMessage.error('保存失败')
  }
}

const handleEditPassport = () => {
  editingForm.value = {
    passport: clientData.value.passport,
    passportIssuePlace: clientData.value.passportIssuePlace,
    passportIssueDate: clientData.value.passportIssueDate,
    passportExpiry: clientData.value.passportExpiry
  }
  passportDialogVisible.value = true
}

// 保存护照信息
const handleSavePassport = async () => {
  // 更新本地数据
  if (editingForm.value.passport !== undefined) clientData.value.passport = editingForm.value.passport
  if (editingForm.value.passportIssuePlace !== undefined) clientData.value.passportIssuePlace = editingForm.value.passportIssuePlace
  if (editingForm.value.passportIssueDate !== undefined) clientData.value.passportIssueDate = editingForm.value.passportIssueDate
  if (editingForm.value.passportExpiry !== undefined) clientData.value.passportExpiry = editingForm.value.passportExpiry

  // 如果是新客户，不需要调用API
  if (isNewClient.value) {
    passportDialogVisible.value = false
    // ElMessage.success('保存成功')
    return
  }

  const clientId = route.params.id as string

  try {
    // 显示加载状态
    const loading = ElMessage({
      type: 'info',
      message: '正在保存...',
      duration: 5000 // 修改为5000ms
    })

    // 准备要更新的数据
    const updateData = {
      passport: clientData.value.passport,
      passport_issue_place: clientData.value.passportIssuePlace,
      passport_issue_date: clientData.value.passportIssueDate,
      passport_expiry: clientData.value.passportExpiry
    }

    // 调用API更新客户信息
    await updateClient(clientId, updateData)

    // 关闭加载提示
    loading.close()
    passportDialogVisible.value = false
    // ElMessage.success('保存成功')
  } catch (error) {
    console.error('更新护照信息失败:', error)
    ElMessage.error('保存失败')
  }
}

const handleEditAddress = () => {
  editingForm.value = {
    address: clientData.value.address,
    location: clientData.value.location
  }
  addressDialogVisible.value = true
}

// 自定义模块编辑
const customModuleDialogVisible = ref(false)
const editingCustomModuleId = ref('')
const customModuleContent = ref('')

const handleEditCustomModule = (id: string) => {
  editingCustomModuleId.value = id
  customModuleContent.value = clientData.value.customModules[id]?.content || ''
  customModuleDialogVisible.value = true
}

const handleSaveCustomModuleContent = async () => {
  if (!editingCustomModuleId.value) return

  if (isNewClient.value) {
    // 如果是新客户，只更新本地数据
    clientData.value.customModules[editingCustomModuleId.value] = {
      ...clientData.value.customModules[editingCustomModuleId.value] || {},
      content: customModuleContent.value
    }
    customModuleDialogVisible.value = false
    // ElMessage.success('保存成功')
    return
  }

  // 已有客户，调用API更新
  const clientId = route.params.id as string

  try {
    const loading = ElMessage({
      type: 'info',
      message: '正在保存...',
      duration: 1000
    })

    // 准备模块数据
    const moduleId = editingCustomModuleId.value
    const module = backgroundModules.value.find(m => m.id === moduleId)

    if (!module) {
      loading.close()
      ElMessage.error('模块配置不存在')
      return
    }

    const customModuleData = {
      module_id: moduleId,
      title: module.title,
      type: 'custom',
      content: customModuleContent.value,
      icon: module.icon,
      visible: module.visible,
      order: module.order
    }

    // 检查模块是否已存在于服务器
    const existingModule = clientData.value.customModules[moduleId]

    if (existingModule && existingModule.id) {
      // 使用服务器返回的真实ID而不是前端生成的ID
      const serverId = existingModule.id

      // 使用新的背景模块API更新
      await updateBackgroundModule(clientId, serverId, customModuleData)
    } else {
      // 使用新的背景模块API创建
      const response = await addBackgroundModule(clientId, customModuleData)
      // 保存返回的ID
      if (!clientData.value.customModules[moduleId]) {
        clientData.value.customModules[moduleId] = { content: '' }
      }
      clientData.value.customModules[moduleId].id = response.id
      // 保存ID映射
      moduleIdMap.value[moduleId] = response.id
    }

    // 更新本地数据
    clientData.value.customModules[moduleId] = {
      ...clientData.value.customModules[moduleId] || {},
      content: customModuleContent.value
    }

    loading.close()
    customModuleDialogVisible.value = false
    // ElMessage.success('保存成功')
  } catch (error) {
    // 处理保存失败
    ElMessage.error('保存失败')
  }
}

const handleDeleteCustomModuleContent = async (id: string) => {
  if (isNewClient.value) {
    // 新客户只清空本地数据
    if (clientData.value.customModules[id]) {
      clientData.value.customModules[id].content = ''
    }
    // ElMessage.success('删除成功')
    return
  }

  const clientId = route.params.id as string

  try {
    const loading = ElMessage({
      type: 'info',
      message: '正在删除内容...',
      duration: 1000
    })

    // 使用updateBackgroundModule清空内容而不是删除模块
    const module = backgroundModules.value.find(m => m.id === id)

    if (module && clientData.value.customModules[id]) {
      const emptyData = {
        module_id: id,
        title: module.title,
        type: 'custom',
        content: '', // 清空内容
        icon: module.icon,
        visible: module.visible,
        order: module.order
      }

      // 使用服务器返回的真实ID而不是前端生成的ID
      const serverId = clientData.value.customModules[id].id || moduleIdMap.value[id]
      if (serverId) {
        await updateBackgroundModule(clientId, serverId, emptyData)
      } else {
        throw new Error('找不到模块的服务器ID')
      }

      // 更新本地数据
      if (!clientData.value.customModules[id]) {
        clientData.value.customModules[id] = { content: '' }
      } else {
        clientData.value.customModules[id].content = ''
      }
    }

    loading.close()
    // ElMessage.success('删除成功')
  } catch (error) {
    // 处理清空内容失败
    ElMessage.error('操作失败')
  }
}

// 添加映射对象
const degreeMap = {
  high_school: '高中',
  bachelor: '本科',
  master: '硕士',
  phd: '博士',
  summer_school: '暑校',
  second_degree: '第二学位',
  exchange: '交换'
}

const awardLevelMap = {
  school: '校级',
  city: '市级',
  province: '省级',
  national: '国家级',
  world: '世界级'
}



// 添加状态变量
const idCardDialogVisible = ref(false)
const passportDialogVisible = ref(false)

// 添加弹窗状态变量 - 这些在前面已经定义了，这里添加缺失的几个变量

// 配置 dayjs 插件
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.tz.setDefault('Asia/Shanghai')

// 添加返回客户列表的方法
const handleBackToList = () => {
  router.push('/clients')
}

// 文书管理相关方法
const fetchClientDocuments = async () => {
  if (isNewClient.value) return
  
  documentsLoading.value = true
  try {
    const clientId = route.params.id as string
    const response = await getClientDocuments(clientId)
    clientDocuments.value = response.data || response || []
    console.log('获取文书列表成功:', clientDocuments.value.length, '份')
  } catch (error) {
    console.error('获取文书列表失败:', error)
    clientDocuments.value = []
    ElMessage.error('获取文书列表失败')
  } finally {
    documentsLoading.value = false
  }
}

const handleRefreshDocuments = async () => {
  await fetchClientDocuments()
  ElMessage.success('文书列表已刷新')
}

const handleCreateNewDocument = () => {
  // 跳转到文书创建页面的选择界面
  ElMessage.info('即将跳转到文书创建页面')
  // TODO: 实现跳转逻辑，可能需要一个选择界面让用户选择创建CV还是RL
}

// 新增导航方法
const handleNavigateToCV = (cvDoc?: any) => {
  const clientId = route.params.id as string
  if (cvDoc && cvDoc.id) {
    // 编辑现有CV文书
    router.push(`/writing/cv?client=${clientId}&edit=${cvDoc.id}`)
  } else {
    // 创建新CV文书
    router.push(`/writing/cv?client=${clientId}`)
  }
}

const handleNavigateToRL = (rlDoc?: any) => {
  const clientId = route.params.id as string
  if (rlDoc && rlDoc.id) {
    // 编辑现有RL文书
    router.push(`/writing/rl?client=${clientId}&edit=${rlDoc.id}`)
  } else {
    // 创建新RL文书
    router.push(`/writing/rl?client=${clientId}`)
  }
}

const handleNavigateToPS = () => {
  const clientId = route.params.id as string
  router.push(`/writing/ps?client=${clientId}`)
}

// 处理学校logo加载错误
const handleLogoError = (event: Event) => {
  const target = event.target as HTMLImageElement
  target.src = logoStore.getSchoolLogoFallback('')
}

// CV文书相关的辅助函数
const getCVSchoolLogo = (cvDoc: any) => {
  if (!cvDoc.target_major) return logoStore.getDefaultLogo()

  const schoolName = getCVSchoolName(cvDoc)
  return logoStore.getSchoolLogo(schoolName)
}

const getCVSchoolName = (cvDoc: any) => {
  if (!cvDoc.target_major) return '学校未知'

  // target_major格式：'学校 - 学位 - 专业'
  const parts = cvDoc.target_major.split(' - ')
  return parts[0] || '学校未知'
}

const getCVProgramName = (cvDoc: any) => {
  if (!cvDoc.target_major) return '专业未知'

  // target_major格式：'学校 - 学位 - 专业'
  const parts = cvDoc.target_major.split(' - ')
  if (parts.length >= 3) {
    return parts[2] // 只返回专业名称，与PS模块保持一致
  } else if (parts.length === 2) {
    return parts[1] // 只有专业
  }
  return '专业未知'
}

const handleViewDocument = (document) => {
  console.log('查看文书:', document)
  ElMessage.info(`查看${document.type}: ${document.title}`)
  // TODO: 实现文书查看功能
}

const handleEditDocument = (document) => {
  console.log('编辑文书:', document)
  if (document.type === 'CV') {
    // 跳转到CV编辑页面
    router.push(`/writing/cv?client=${route.params.id}&edit=${document.id}`)
  } else if (document.type === 'RL') {
    // 跳转到RL编辑页面
    router.push(`/writing/rl?client=${route.params.id}&edit=${document.id}`)
  }
}

const handleDownloadDocument = (document) => {
  console.log('下载文书:', document)
  ElMessage.info(`下载${document.type}: ${document.title}`)
  // TODO: 实现文书下载功能
}

const handleDeleteDocument = async (document) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这份${document.type}吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    ElMessage.info('删除功能开发中...')
    // TODO: 实现文书删除API
    
  } catch (error) {
    // 用户取消删除
    console.log('用户取消删除')
  }
}

// 添加模块ID映射对象
const moduleIdMap = ref({});

// 定校书相关方法
const handleAddSchoolProgram = () => {
  addSchoolProgramDialogVisible.value = true
  // TODO: 当后端API准备好时，调用API获取最新的专业数据
  // await fetchAllPrograms()
}



const handleSelectProgram = async (program: SchoolProgram) => {
  // 检查是否重复添加
  if (isAlreadySelected(program.id)) {
    ElMessage.warning('该专业已在定校书中')
    return
  }

  // 添加专业到定校书
  schoolPrograms.value.push({ ...program })
  
  // 如果不是新建客户，调用API保存到服务器
  if (!isNewClient.value) {
    try {
      const clientId = route.params.id as string
      const programData = {
        program_id: parseInt(program.id)
      }
      const response = await addClientProgram(clientId, programData)
      console.log('成功添加专业到定校书:', response)
      // ElMessage.success('已添加到定校书') // 已注释掉成功提示
    } catch (error) {
      console.error('保存到定校书失败:', error)
      // 如果保存失败，从本地列表中移除
      const index = schoolPrograms.value.findIndex(p => p.id === program.id)
      if (index !== -1) {
        schoolPrograms.value.splice(index, 1)
      }
      ElMessage.error('保存到定校书失败')
      return
    }
  }

  // 自动重新生成定校书PDF
  handleRegenerateSchoolBook()
}

const handleRemoveSchoolProgram = async (programId: string) => {
  console.log('开始删除定校书项目:', { programId })

  // 直接通过programId在全局列表中查找要删除的项目
  const globalIndex = schoolPrograms.value.findIndex(p => p.id === programId)
  console.log('全局列表中的索引:', globalIndex)

  if (globalIndex !== -1) {
    const programToRemove = schoolPrograms.value[globalIndex]
    console.log('准备删除的项目:', programToRemove)

    // 如果不是新建客户，调用API从服务器删除
    if (!isNewClient.value) {
      try {
        const clientId = route.params.id as string
        // 使用正确的program_id - 这应该是ai_selection_programs表中的ID
        const programIdInt = parseInt(programId)
        console.log('删除定校书项目:', {
          clientId,
          programId: programIdInt,
          programName: programToRemove.program_name_cn
        })

        await removeClientProgram(clientId, programIdInt)
        console.log('成功从定校书中删除专业:', programToRemove.program_name_cn)
      } catch (error) {
        console.error('从定校书删除专业失败:', error)
        ElMessage.error('删除失败，请稍后重试')
        return
      }
    }

    // 从本地移除
    schoolPrograms.value.splice(globalIndex, 1)
    console.log('已从本地移除，剩余项目数量:', schoolPrograms.value.length)

    // ElMessage.success('已移除专业') // 已注释掉成功提示

    // 自动重新生成定校书PDF
    handleRegenerateSchoolBook()
  } else {
    console.error('未在全局列表中找到要删除的项目:', { programId })
    ElMessage.error('删除失败：未找到目标项目')
  }
}

const isAlreadySelected = (programId: string) => {
  return schoolPrograms.value.some(p => p.id === programId)
}

const handleViewProgramDetails = (program: any) => {
  // 查看专业详情功能
  console.log('查看专业详情:', program)
  // TODO: 实现专业详情查看逻辑，可以打开弹窗或跳转页面
  ElMessage.info('专业详情功能开发中')
}

const handleRegenerateSchoolBook = async () => {
  if (schoolPrograms.value.length === 0) {
    schoolBookPdfUrl.value = ''
    return
  }

  try {
    // TODO: 后端API开发完成后，调用API生成PDF
    // const clientId = route.params.id as string
    // const response = await generateSchoolBookPdf(clientId, schoolPrograms.value)
    // schoolBookPdfUrl.value = response.pdf_url

    // TODO: 调用后端API生成真实PDF
    await new Promise(resolve => setTimeout(resolve, 1500))
    // schoolBookPdfUrl.value = response.pdf_url // 使用真实的PDF URL
    schoolBookPdfUrl.value = '' // 暂时置空，等待后端API完成
  } catch (error) {
    console.error('生成定校书失败:', error)
  }
}

const handleDownloadSchoolBook = async () => {
  if (!schoolBookPdfUrl.value) {
    return
  }

  try {
    // TODO: 后端API开发完成后，调用API下载PDF
    // const clientId = route.params.id as string
    // const response = await downloadSchoolBookPdf(clientId)
    // 创建下载链接

    // 模拟下载
    const link = document.createElement('a')
    link.href = schoolBookPdfUrl.value
    link.download = `${clientData.value.name || '客户'}-定校书.pdf`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (error) {
    console.error('下载失败:', error)
  }
}

// 选择添加方式
const handleSelectAddMode = (mode: 'manual' | 'smart') => {
  addModeDialogVisible.value = false
  
  if (mode === 'manual') {
    // 手动添加
    if (addModeType.value === 'academic') {
      handleAddAcademicManual()
    } else {
      handleAddWorkManual()
    }
  } else {
    // 智能添加 - 暂时注释掉
    // handleSmartUpload(addModeType.value)
    ElMessage.info('智能添加功能暂时不可用')
  }
}

// 智能上传相关方法 - 暂时注释掉
/*
const handleSmartUpload = (type: 'academic' | 'work') => {
  smartUploadType.value = type
  smartUploadDialogVisible.value = true
  uploadFileList.value = []
  extractedContent.value = ''
  isExtracting.value = false
}

const handleFileChange = (file: any) => {
  console.log('文件选择:', file)
  // 限制文件大小为10MB
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过10MB')
    uploadFileList.value = []
    return
  }
}

const handleExceed = () => {
  ElMessage.warning('只能上传一个文件')
}

const handleCancelSmartUpload = () => {
  smartUploadDialogVisible.value = false
  uploadFileList.value = []
  extractedContent.value = ''
  isExtracting.value = false
}
*/

/*
const handleConfirmSmartUpload = async () => {
  if (!uploadFileList.value.length) {
    ElMessage.warning('请先选择文件')
    return
  }
  
  isExtracting.value = true;
  const file = uploadFileList.value[0].raw;

  try {
    const response = await augmentExperienceFromFile(route.params.id as string, smartUploadType.value, file);
    console.log('智能分析API响应:', response); // 调试日志
    
    // 检查响应格式和状态
    if (response && (response.status === 'success' || response.experience_type)) {
      const experienceType = response.experience_type || smartUploadType.value;
      const newExperience = response.data;
      
      console.log('准备添加的经历数据:', { experienceType, newExperience }); // 调试日志
      
      // 确保新经历数据格式正确
      if (newExperience && typeof newExperience === 'object') {
        // 创建格式化的新经历对象
        const formattedExperience = {
          ...newExperience,
          // 确保日期字段格式正确
          created_at: newExperience.created_at || new Date().toISOString(),
          updated_at: newExperience.updated_at || new Date().toISOString()
        };
        
        if (experienceType === 'academic') {
          // 添加到学术经历数组的开头（最新的在前面）
          clientData.value.academic.unshift(formattedExperience);
          sortExperiences(clientData.value.academic);
          console.log('已添加学术经历，当前数量:', clientData.value.academic.length);
        } else if (experienceType === 'work') {
          // 添加到工作经历数组的开头（最新的在前面）
          clientData.value.work.unshift(formattedExperience);
          sortExperiences(clientData.value.work);
          console.log('已添加工作经历，当前数量:', clientData.value.work.length);
        }
        
        // 强制触发Vue的响应式更新
        clientData.value = { ...clientData.value };
        
        ElMessage.success('智能添加成功！新经历已添加到列表中');
        smartUploadDialogVisible.value = false;
        
        // 重置上传状态
        uploadFileList.value = [];
        extractedContent.value = '';
        
        // 重新获取客户详情以确保数据同步（保险措施）
        try {
          await fetchClientDetail();
          console.log('客户详情已重新同步');
        } catch (syncError) {
          console.warn('同步客户详情失败，但新数据已在界面显示:', syncError);
        }
        
        // 如果当前不在对应的标签页，可以提示用户切换
        if (experienceType === 'academic' && activeTab.value !== 'backgrounds') {
          setTimeout(() => {
            ElMessage.info('新的学术经历已添加，请切换到"背景信息"标签页查看');
          }, 1000);
        } else if (experienceType === 'work' && activeTab.value !== 'backgrounds') {
          setTimeout(() => {
            ElMessage.info('新的工作经历已添加，请切换到"背景信息"标签页查看');
          }, 1000);
        }
      } else {
        console.error('返回的经历数据格式不正确:', newExperience);
        ElMessage.error('添加失败：返回的数据格式不正确');
      }
    } else {
      console.warn('意外的响应格式:', response);
      ElMessage.error(response.message || response.detail || '智能添加失败，响应格式异常。');
    }
  } catch (error) {
    console.error("智能添加失败:", error);
    
    // 更详细的错误处理
    let errorMsg = '智能添加失败，请稍后重试。';
    
    if (error.response) {
      const { status, data } = error.response;
      switch (status) {
        case 400:
          errorMsg = data.detail || '请求参数错误，请检查上传的文件格式';
          break;
        case 404:
          errorMsg = '客户不存在或服务不可用';
          break;
        case 422:
          errorMsg = data.detail || '文件内容无法解析，请检查文件内容是否完整';
          break;
        case 500:
          errorMsg = 'AI处理服务暂时不可用，请稍后重试';
          break;
        case 504:
          errorMsg = 'AI处理超时，请稍后重试或联系管理员';
          break;
        default:
          errorMsg = data.detail || `服务错误 (${status})`;
      }
    } else if (error.request) {
      errorMsg = '网络连接失败，请检查网络状态';
    }
    
    ElMessage.error(errorMsg);
  } finally {
    isExtracting.value = false;
  }
}
*/
</script>

<style lang="postcss" scoped>
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 20px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: 'liga';
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.client-profile-page {
  color: #334155;
  --header-height: 3.5rem; /* 统一表头高度 */
}

/* 图标按钮样式 */
:deep(.icon-btn) {
  padding: 6px !important;
  min-width: unset !important;
  width: 32px !important;
  height: 32px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.icon-btn.is-link) {
  padding: 2px !important;
  min-width: unset !important;
  width: 24px !important;
  height: 24px !important;
}

:deep(.icon-btn .material-icons-outlined) {
  margin: 0 !important;
}

/* 卡片内的操作按钮容器样式 */
.pro-card .flex.justify-between .flex.space-x-1 {
  display: flex;
  align-items: center;
  align-self: center;
}

.custom-dialog :deep(.el-dialog__header) {
  padding: 16px 24px;
  margin: 0;
  border-bottom: 1px solid #f1f5f9;
}

.custom-dialog :deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 500;
}

.custom-dialog :deep(.el-dialog__body) {
  padding: 24px;
}

.custom-dialog :deep(.el-dialog__footer) {
  padding: 16px 24px;
  border-top: 1px solid #f1f5f9;
}

/* 调整页面结构 */
.backgrounds-header {
  @apply flex justify-between items-center mb-4;
}

.backgrounds-content {
  @apply space-y-5;
}

/* 自定义按钮样式 */
:deep(.el-button--primary) {
  --el-button-bg-color: #4F46E5;
  --el-button-border-color: #4F46E5;
  --el-button-hover-bg-color: #4338CA;
  --el-button-hover-border-color: #4338CA;
  --el-button-active-bg-color: #3730A3;
  --el-button-active-border-color: #3730A3;
}

:deep(.el-button--primary.is-link) {
  --el-button-text-color: #4F46E5;
}

:deep(.el-button--primary.is-link:hover) {
  --el-button-hover-text-color: #4338CA;
}

/* 添加按钮的统一样式 */
.add-btn {
  @apply text-white font-medium;
  background-color: #4F46E5;
  border-color: #4F46E5;
}

/* 学校卡片样式 */
.school-card {
  @apply transition-all duration-300;
}

.school-card:hover {
  @apply transform -translate-y-0.5 shadow-lg;
}

/* 排名徽章样式 */
.ranking-badge {
  @apply flex items-center justify-center gap-2 px-3 py-2 rounded-xl border shadow-sm transition-all duration-300 min-w-20 h-9;
  border: 1px solid rgba(251, 191, 36, 0.4);
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  backdrop-filter: blur(8px);
}

.ranking-badge:hover {
  @apply transform -translate-y-0.5 scale-105;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #fde68a 0%, #fcd34d 100%);
  border-color: rgba(251, 191, 36, 0.6);
}

.ranking-label {
  @apply text-xs font-semibold text-slate-600 uppercase tracking-wide;
  font-size: 0.6rem;
  line-height: 1;
}

.ranking-number {
  @apply text-sm font-bold text-slate-700;
  line-height: 1;
  font-feature-settings: 'tnum';
}

.add-btn:hover {
  background-color: #4338CA;
  border-color: #4338CA;
}

/* 编辑按钮的统一样式 */
.edit-btn {
  @apply text-indigo-600;
}

.edit-btn:hover {
  @apply text-indigo-700 bg-indigo-50;
}

/* 删除按钮的统一样式 */
.delete-btn {
  @apply text-red-600;
}

.delete-btn:hover {
  @apply text-red-700 bg-red-50;
}

/* 取消按钮样式 */
.cancel-btn {
  @apply text-gray-700 border-gray-300;
}

.cancel-btn:hover {
  @apply bg-gray-50 text-gray-900;
}

/* 确认按钮样式 */
.confirm-btn {
  @apply text-white font-medium;
  background-color: #4F46E5;
  border-color: #4F46E5;
}

.confirm-btn:hover {
  background-color: #4338CA;
  border-color: #4338CA;
}



/* 添加标签页切换动画 */
.tab-fade-enter-active,
.tab-fade-leave-active {
  transition: all 0.2s ease;
}

.tab-fade-enter-from {
  opacity: 0;
  transform: translateY(0px); /* 改为负值，使其从上方进入 */
}

.tab-fade-leave-to {
  opacity: 0;
  transform: translateY(0px); /* 改为正值，使其向下方离开 */
}

/* 确保内容区域有相对定位，以便子元素可以使用绝对定位 */
.tab-content-wrapper {
  position: relative;
  min-height: 400px;
}

/* 确保过渡中的内容使用绝对定位，避免布局跳动 */
.tab-content {
  position: absolute;
  width: 100%;
  transition: all 0.2s ease;
}

.pro-card-title {
  @apply text-gray-800 font-medium flex items-center;
  font-weight: 600; /* 增加标题字体粗细，使其更加明显 */
}

.pro-card-title .icon {
  @apply mr-2 text-primary text-lg;
}

/* 添加内联定义的pro-card样式，确保它们在组件内部能正确显示 */
.pro-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-100;
  margin-bottom: 1rem;
}

.pro-card-header {
  @apply border-b border-gray-100 flex items-center justify-between;
  height: 3.5rem;
  padding: 0 0.875rem;
}

.pro-card-body {
  @apply p-4;
}

.pro-card-footer {
  @apply p-3.5 border-t border-gray-100 flex justify-end;
}

/* 对话框样式修改 - 移除阴影 */
:deep(.el-dialog) {
  box-shadow: none !important;
  border: 1px solid #e4e7ed;
}

:deep(.el-dialog__header),
:deep(.el-dialog__footer) {
  box-shadow: none !important;
  border-top: none;
  background-color: #fff;
}

:deep(.el-dialog__body) {
  background-color: #fff;
}

.section-card {
  @apply bg-white rounded-md mb-6 overflow-hidden;
}

/* 默认按钮悬浮文字颜色覆盖 */
:deep(.el-button.el-button--default) {
  --el-button-bg-color: transparent;
  --el-button-border-color: transparent;
  --el-button-hover-bg-color: transparent;
  --el-button-hover-border-color: transparent;
  --el-button-active-bg-color: transparent;
  --el-button-active-border-color: transparent;
  --el-button-hover-text-color: #4F46E5 !important; /* 紫色 */
}

/* 确保所有按钮悬浮时的文字颜色都是紫色 */
:deep(.el-button:hover) {
  --el-button-text-color: #4F46E5 !important; /* 紫色 */
}

/* 特殊处理取消按钮和其他默认按钮 */
:deep(.el-button:not(.el-button--primary):not(.el-button--danger):hover) {
  color: #4F46E5 !important; /* 紫色 */
  border-color: #4F46E5 !important; /* 紫色边框 */
}

/* 全局覆盖所有图标的悬浮颜色为紫色 */
:deep(svg:hover),
:deep(.material-icons-outlined:hover),
:deep(.material-icons:hover),
:deep(.el-icon:hover),
:deep(i:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖Element Plus内部图标的悬浮颜色 */
:deep(.el-input__prefix .el-input__icon:hover),
:deep(.el-input__suffix .el-input__icon:hover),
:deep(.el-select__caret:hover),
:deep(.el-pagination__right-wrapper .el-icon:hover),
:deep(.el-pagination__left-wrapper .el-icon:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖搜索框图标的悬浮颜色 */
:deep(.el-input .el-input__prefix .material-icons-outlined:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖下拉菜单操作按钮图标的悬浮颜色 */
:deep(.el-dropdown-link:hover .material-icons-outlined),
:deep(.icon-btn:hover .material-icons-outlined) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖对话框中的图标悬浮颜色 */
:deep(.el-dialog .material-icons-outlined:hover),
:deep(.el-dialog svg:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖关闭按钮SVG图标颜色 */
:deep(.el-dialog__headerbtn:hover),
:deep(.el-dialog__headerbtn:hover svg),
:deep(.el-dialog__close:hover),
:deep(.el-dialog__close:hover svg) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖Element Plus上传组件图标 */
:deep(.el-upload .material-icons-outlined:hover),
:deep(.el-upload-dragger:hover .material-icons-outlined) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖表格操作图标悬浮颜色 */
:deep(.el-table .el-icon:hover),
:deep(.el-table .material-icons-outlined:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖分页组件图标 */
:deep(.el-pagination .el-icon:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 确保按钮内的图标悬浮时也是紫色 */
:deep(.el-button:hover .el-icon),
:deep(.el-button:hover .material-icons-outlined),
:deep(.el-button:hover svg) {
  color: inherit !important; /* 继承按钮文字颜色 */
}

/* 特殊处理搜索输入框前缀图标 */
:deep(.el-input__prefix-inner:hover),
:deep(.el-input__prefix-inner:hover .material-icons-outlined) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖所有 a 标签颜色为紫色 */
:deep(a),
:deep(a:visited),
:deep(a:active) {
  color: #4F46E5 !important; /* 紫色 */
  text-decoration: none !important;
}

:deep(a:hover) {
  color: #4338CA !important; /* 深紫色 */
  text-decoration: underline !important;
}

/* 覆盖基于Tailwind的链接文本样式 */
:deep(.text-blue-600),
:deep(.text-blue-500) {
  color: #4F46E5 !important; /* 紫色 */
}

:deep(.hover\:text-blue-700:hover),
:deep(.hover\:text-blue-600:hover) {
  color: #4338CA !important; /* 深紫色 */
}

/* 修改所有element-plus的颜色初始变量 */
:deep(:root) {
  --el-color-primary: #4F46E5 !important; /* 紫色 */
  --el-color-primary-light-3: #6366F1 !important;
  --el-color-primary-light-5: #818CF8 !important;
  --el-color-primary-light-7: #C7D2FE !important;
  --el-color-primary-light-9: #EEF2FF !important;
  --el-color-primary-dark-2: #4338CA !important; /* 深紫色 */
}

/* 覆盖分页组件可能的蓝色为紫色 */
:deep(.el-pagination .el-pager li.is-active) {
  --el-color-primary: #4F46E5 !important; /* 紫色 */
  color: #FFFFFF !important;
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
}

:deep(.el-pagination .el-pager li:hover:not(.is-active)) {
  color: #4F46E5 !important; /* 紫色 */
}

:deep(.el-pagination .btn-prev:hover),
:deep(.el-pagination .btn-next:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 进度条颜色 */
:deep(.el-progress-bar__inner) {
  background-color: #4F46E5 !important;
}

/* 覆盖下拉菜单项的悬浮颜色 */
:deep(.el-dropdown-menu__item:hover),
:deep(.el-dropdown-menu__item:not(.is-disabled):hover) {
  color: #4F46E5 !important; /* 紫色 */
  background-color: rgba(79, 70, 229, 0.05) !important;
}

/* 覆盖表单元素的主色调 */
:deep(.el-input:hover .el-input__wrapper),
:deep(.el-input.is-focus .el-input__wrapper) {
  border-color: #4F46E5 !important;
}

:deep(.el-select:hover .el-input__wrapper),
:deep(.el-select.is-focus .el-input__wrapper) {
  border-color: #4F46E5 !important;
}

:deep(.el-date-editor:hover .el-input__wrapper),
:deep(.el-date-editor.is-focus .el-input__wrapper) {
  border-color: #4F46E5 !important;
}

/* 覆盖开关组件的激活色 */
:deep(.el-switch.is-checked .el-switch__core) {
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
}

/* 覆盖选择器下拉选项的悬浮色 */
:deep(.el-select-dropdown__item:hover) {
  background-color: rgba(79, 70, 229, 0.1) !important;
  color: #4F46E5 !important;
}

:deep(.el-select-dropdown__item.selected) {
  color: #4F46E5 !important;
}

/* 覆盖日期选择器的悬浮色 */
:deep(.el-date-table td:hover),
:deep(.el-date-table td.current:not(.disabled)) {
  color: #4F46E5 !important;
}

:deep(.el-date-table td.today) {
  color: #4F46E5 !important;
}

/* 覆盖标签页可能的蓝色 */
:deep(.el-tabs__active-bar) {
  background-color: #4F46E5 !important;
}

:deep(.el-tabs__item.is-active) {
  color: #4F46E5 !important;
}

:deep(.el-tabs__item:hover) {
  color: #4F46E5 !important;
}

/* 输入框焦点状态紫色覆盖 */
:deep(.el-input.is-focus .el-input__wrapper) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.8) !important; /* 紫色阴影 */
}

:deep(.el-input:focus-within .el-input__wrapper) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.8) !important; /* 紫色阴影 */
}

:deep(.el-textarea.is-focus .el-textarea__inner) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.8) !important; /* 紫色阴影 */
}

:deep(.el-select.is-focus .el-input__wrapper) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.8) !important; /* 紫色阴影 */
}

:deep(.el-date-editor.is-focus .el-input__wrapper) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.8) !important; /* 紫色阴影 */
}

/* 覆盖Element Plus默认的focus颜色 */
:deep(.el-input__wrapper:focus),
:deep(.el-input__wrapper:focus-within),
:deep(.el-textarea__inner:focus) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.8) !important; /* 紫色阴影 */
}

/* 下拉框焦点状态紫色覆盖 */
:deep(.el-select__wrapper.is-focused) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.8) !important; /* 紫色阴影 */
}

:deep(.el-select__wrapper:focus) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.8) !important; /* 紫色阴影 */
}

:deep(.el-select__wrapper:focus-within) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.8) !important; /* 紫色阴影 */
}

:deep(.el-select:focus-within .el-select__wrapper) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.8) !important; /* 紫色阴影 */
}

/* 下拉框激活状态 */
:deep(.el-select.is-focus .el-select__wrapper) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.8) !important; /* 紫色阴影 */
}

/* 专业卡片样式 */
.program-card {
  transition: all 0.3s ease;
}

.program-card:hover {
  transform: translateY(-4px);
}

.program-card .bg-white {
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.program-card:hover .bg-white {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.program-card .w-2.h-2.bg-blue-500 {
  box-shadow: 0 0 8px rgba(79, 70, 229, 0.4);
}

/* 地区标题样式 */
.region-title {
  position: relative;
}

.region-title::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: -8px;
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, #4F46E5, #818CF8);
  border-radius: 1px;
}

/* 统计信息卡片 */
.stats-card {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* 操作按钮样式优化 */
:deep(.el-button.w-full) {
  height: 40px;
  font-weight: 500;
  transition: all 0.2s ease;
}

:deep(.el-button.w-full:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.25);
}

/* 提示信息样式 */
.info-tip {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

/* 专业卡片内的排名标签 */
.program-card .bg-gray-100 {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border: 1px solid #d1d5db;
}

/* 语言要求和截止日期的图标颜色 */
.program-card .text-blue-500 {
  color: #3b82f6 !important;
}

.program-card .text-orange-500 {
  color: #f59e0b !important;
}

/* 官网链接样式 */
.program-card .border-t.border-gray-100 {
  border-color: #e5e7eb;
}

.program-card .text-blue-600 {
  color: #4F46E5 !important;
  transition: all 0.2s ease;
}

.program-card .text-blue-600:hover {
  color: #4338CA !important;
  text-decoration: underline;
}

/* 地区标签样式 */
.bg-blue-100.text-blue-800 {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  font-weight: 600;
  border: 1px solid #93c5fd;
}

/* 空状态样式优化 */
.text-center.py-16 {
  background: linear-gradient(135deg, #fafafa 0%, #f3f4f6 100%);
  border-radius: 12px;
  border: 2px dashed #d1d5db;
}

/* 右侧统计卡片样式 */
.bg-gray-50.rounded-lg.p-4 {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  border: 1px solid #e5e7eb;
}

.text-2xl.font-bold.text-blue-600 {
  color: #4F46E5 !important;
  text-shadow: 0 1px 2px rgba(79, 70, 229, 0.1);
}

/* 淡紫色禁用按钮样式 */
:deep(.disabled-light-purple.el-button--primary.is-disabled) {
  background-color: #C7D2FE !important;
  border-color: #C7D2FE !important;
  color: #6366F1 !important;
}

:deep(.disabled-light-purple.el-button--primary.is-disabled:hover) {
  background-color: #C7D2FE !important;
  border-color: #C7D2FE !important;
  color: #6366F1 !important;
}

/* 文书卡片样式 */
.document-card {
  transition: all 0.3s ease;
}

.document-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

/* 内容预览的截断显示 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 文书类型标签样式 */
.document-card .bg-blue-100 {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 1px solid #bfdbfe;
}

.document-card .bg-green-100 {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border: 1px solid #bbf7d0;
}

.document-card .text-blue-800 {
  color: #1e40af !important;
}

.document-card .text-green-800 {
  color: #166534 !important;
}

/* 统计卡片样式 */
.document-card .bg-blue-50 {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 1px solid #bfdbfe;
}

.document-card .bg-green-50 {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border: 1px solid #bbf7d0;
}

.document-card .text-blue-600 {
  color: #2563eb !important;
}

.document-card .text-green-600 {
  color: #16a34a !important;
}

/* 文书筛选按钮样式 */
:deep(.el-radio-group .el-radio-button__inner) {
  border-color: #d1d5db;
  color: #6b7280;
}

:deep(.el-radio-group .el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  color: #ffffff !important;
}

:deep(.el-radio-group .el-radio-button:hover .el-radio-button__inner) {
  color: #4F46E5 !important;
}

/* 文书统计数据强调样式 */
.documents-stats .text-lg.font-bold {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 新的文书项目卡片样式 */
.document-item-card {
  transition: all 0.3s ease;
  border-radius: 12px;
  border-width: 2px;
  min-height: 144px; /* 9rem = 144px */
  position: relative;
  overflow: hidden;
}

.document-item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: #3b82f6;
}

/* 版本标签样式优化 */
.document-item-card .bg-blue-100 {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.15);
}

.document-item-card .bg-green-100 {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  box-shadow: 0 1px 3px rgba(34, 197, 94, 0.15);
}

.document-item-card .bg-purple-100 {
  background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
  box-shadow: 0 1px 3px rgba(147, 51, 234, 0.15);
}

/* 状态指示器动画 */
.document-item-card .bg-green-400 {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 文本截断样式 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .document-item-card {
    min-height: 120px;
  }
  
  .document-item-card h3 {
    font-size: 0.875rem;
  }
}

/* 蓝色主题（个人简历） */
.document-item-card.bg-blue-50 {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.document-item-card.bg-blue-50:hover {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
}

/* 绿色主题（推荐信） */
.document-item-card.bg-green-50 {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}

.document-item-card.bg-green-50:hover {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
}

/* 紫色主题（PS） */
.document-item-card.bg-purple-50 {
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
}

.document-item-card.bg-purple-50:hover {
  background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
}

.document-item-card.border-purple-200 {
  border-color: #c4b5fd;
}

.document-item-card.border-purple-200:hover {
  border-color: #a78bfa;
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
  max-height: 2.8em;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 学校logo样式 */
.document-item-card img {
  transition: all 0.2s ease;
}

.document-item-card:hover img {
  transform: scale(1.05);
}
</style>