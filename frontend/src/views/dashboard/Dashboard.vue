<template>
  <div class="bg-gray-50 max-w-7xl mx-auto py-6">
    <!-- 欢迎信息卡片 -->
    <div class="bg-gradient-to-r from-primary to-primary-light rounded-xl shadow-lg p-6 mb-6 text-white relative overflow-hidden">
      <!-- 装饰性背景图案 -->
      <div class="absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -translate-y-8 translate-x-8"></div>
      <div class="absolute bottom-0 left-0 w-24 h-24 bg-white bg-opacity-5 rounded-full translate-y-6 -translate-x-6"></div>
      
      <div class="relative z-10">
        <h1 class="text-3xl font-bold text-white mb-2">总览</h1>
        <p class="text-white text-opacity-90 text-sm sm:text-base leading-relaxed">欢迎回到TunshuEdu，请尽情享受您的心灵创造。我们将会保护您的隐私。</p>
      </div>
    </div>

    <!-- 统计数据卡片 -->
    <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- 累计客户数量 -->
        <div class="text-center">
          <h3 class="text-sm font-medium text-gray-500 mb-2">累计客户数量</h3>
          <div class="text-4xl font-bold text-gray-900 mb-2">{{ totalClients }}</div>
          <div class="text-sm text-gray-400">Total clients</div>
        </div>

        <!-- 当前客户数量 -->
        <div class="text-center">
          <h3 class="text-sm font-medium text-gray-500 mb-2">当前客户数量</h3>
          <div class="text-4xl font-bold text-primary mb-2">{{ currentClients }}</div>
          <div class="text-sm text-gray-400">Current clients</div>
        </div>

        <!-- 紧急客户数量 -->
        <div class="text-center">
          <h3 class="text-sm font-medium text-gray-500 mb-2">紧急客户数量</h3>
          <div class="text-4xl font-bold text-red-500 mb-2">{{ urgentClients }}</div>
          <div class="text-sm text-gray-400">Urgent clients</div>
        </div>
      </div>
    </div>



    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 左侧：用户信息和积分 -->
      <div class="space-y-6">
        <!-- 当前账户 -->
        <div class="bg-white rounded-xl shadow-sm p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">当前账户</h2>
          
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-12 h-12 rounded-full bg-primary bg-opacity-10 flex items-center justify-center">
              <span class="text-lg font-semibold text-primary">{{ displayName.charAt(0).toUpperCase() }}</span>
            </div>
            <div>
              <div class="font-medium text-gray-900">{{ displayName }}</div>
              <div class="text-sm text-gray-500">{{ userRole }}</div>
            </div>
          </div>

          <div class="space-y-2 text-sm">
            <div class="flex items-center text-gray-600">
              <span class="material-icons-outlined text-sm mr-2">email</span>
              {{ authStore.user?.email || userStore.userInfo.email }}
            </div>
            <div class="flex items-center text-gray-600">
              <span class="material-icons-outlined text-sm mr-2">
                {{ (authStore.user?.role || userStore.userInfo.role) === 'admin' ? 'admin_panel_settings' : 'person' }}
              </span>
              账户类型：{{ (authStore.user?.role || userStore.userInfo.role) === 'admin' ? '管理员' : '普通用户' }}
            </div>
          </div>
        </div>

        <!-- 近期客户 -->
        <div class="bg-white rounded-xl shadow-sm p-6">
          <div class="flex justify-between items-center mb-4">
            <div>
              <h2 class="text-lg font-semibold text-gray-900">近期客户</h2>
              <p class="text-sm text-gray-500">近期服务的客户</p>
            </div>
            <router-link
              to="/clients"
              class="text-sm text-primary hover:text-primary-dark flex items-center"
            >
              查看全部
              <span class="material-icons-outlined text-sm ml-1">arrow_forward</span>
            </router-link>
          </div>

          <div class="space-y-4">
            <!-- 加载中状态 -->
            <div v-if="isLoading" class="space-y-3">
              <div v-for="i in 3" :key="i" class="flex items-center justify-between p-3 animate-pulse">
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 rounded-full bg-gray-200"></div>
                  <div>
                    <div class="h-4 bg-gray-200 rounded w-24 mb-1"></div>
                    <div class="h-3 bg-gray-200 rounded w-16"></div>
                  </div>
                </div>
                <div class="h-3 bg-gray-200 rounded w-16"></div>
              </div>
            </div>

            <!-- 无数据状态 -->
            <div v-else-if="recentClients.length === 0" class="text-center py-8">
              <p class="text-gray-500">暂无近期客户</p>
            </div>

            <!-- 有数据状态 -->
            <div
              v-else
              v-for="client in recentClients"
              :key="client.id_hashed"
              class="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors cursor-pointer"
              @click="handleViewClient(client.id_hashed)"
            >
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 rounded-full bg-primary bg-opacity-10 flex items-center justify-center text-primary">
                  {{ client.name?.charAt(0)?.toUpperCase() || '?' }}
                </div>
                <div>
                  <div class="font-medium text-sm text-gray-900">{{ client.name }}</div>
                  <div class="text-xs text-gray-500">{{ client.location || '未知地区' }}</div>
                </div>
              </div>
              <div class="text-xs text-gray-500">
                {{ formatDate(client.updated_at || client.updatedAt) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 我的积分 -->
        <div class="bg-white rounded-xl shadow-sm p-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold text-gray-900">我的积分</h2>
            <div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
              <PencilSquareIcon class="w-5 h-5 text-purple-600" />
            </div>
          </div>

          <div class="text-center mb-4">
            <div class="text-4xl font-bold text-gray-900 mb-1">{{ userPoints }}</div>
            <div class="text-sm text-gray-500">点</div>
          </div>

          <p class="text-sm text-gray-600 mb-4 leading-relaxed">
            积分用于大模型业务使用的Token数计费,用于文章生成及查重等功能。
          </p>

          <button class="w-full bg-primary text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-primary-dark transition-colors">
            充值
          </button>
        </div>
      </div>

      <!-- 中间：功能索引 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-2">功能索引</h2>
        <p class="text-sm text-gray-500 mb-6">多种功能助力高效服务客户</p>

        <div class="space-y-4" v-memo="[1]">
          <!-- 智能建档 -->
          <div class="p-4 rounded-lg border border-gray-100 hover:border-primary transition-colors group cursor-pointer" @click="$router.push('/clients')">
            <div class="flex items-start space-x-4">
              <div class="w-10 h-10 rounded-lg bg-primary bg-opacity-10 flex items-center justify-center flex-shrink-0">
                <UserIcon class="w-6 h-6 text-primary" />
              </div>
              <div>
                <h3 class="text-base font-medium text-gray-900">智能建档</h3>
                <p class="text-sm text-gray-500 mt-1">上传学生信息意愿一分钟快速建档</p>
                <span class="text-primary hover:text-primary-dark text-sm mt-2 inline-block group-hover:underline">开始建档 ›</span>
              </div>
            </div>
          </div>

          <!-- 智能选校 -->
          <div class="p-4 rounded-lg border border-gray-100 hover:border-primary transition-colors group cursor-pointer" @click="$router.push('/school-assistant')">
            <div class="flex items-start space-x-4">
              <div class="w-10 h-10 rounded-lg bg-purple-100 flex items-center justify-center flex-shrink-0">
                <AcademicCapIcon class="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <h3 class="text-base font-medium text-gray-900">智能选校</h3>
                <p class="text-sm text-gray-500 mt-1">基于上万录取数据分析，精准推荐冲刺/匹配/保底院校清单</p>
                <span class="text-primary hover:text-primary-dark text-sm mt-2 inline-block group-hover:underline">开始选校 ›</span>
              </div>
            </div>
          </div>

          <!-- 文书撰写 -->
          <div class="p-4 rounded-lg border border-gray-100 hover:border-primary transition-colors group cursor-pointer" @click="handleWritingClick">
            <div class="flex items-start space-x-4">
              <div class="w-10 h-10 rounded-lg bg-green-100 flex items-center justify-center flex-shrink-0">
                <DocumentTextIcon class="w-6 h-6 text-green-600" />
              </div>
              <div>
                <h3 class="text-base font-medium text-gray-900">文书撰写</h3>
                <p class="text-sm text-gray-500 mt-1">可自由控制大纲的文书生成大模型，确保文书不会千篇一律</p>
                <span class="text-primary hover:text-primary-dark text-sm mt-2 inline-block group-hover:underline">开始撰写 ›</span>
              </div>
            </div>
          </div>

          <!-- 降AI率 -->
          <div class="p-4 rounded-lg border border-gray-100 hover:border-primary transition-colors group cursor-pointer" @click="$router.push('/ai-reducer')">
            <div class="flex items-start space-x-4">
              <div class="w-10 h-10 rounded-lg bg-red-100 flex items-center justify-center flex-shrink-0">
                <PencilSquareIcon class="w-6 h-6 text-red-600" />
              </div>
              <div>
                <h3 class="text-base font-medium text-gray-900">降AI率</h3>
                <p class="text-sm text-gray-500 mt-1">检测并降低文书AI痕迹，让AI查重率瞬间跳水的拟人化转写神器</p>
                <span class="text-primary hover:text-primary-dark text-sm mt-2 inline-block group-hover:underline">开始使用 ›</span>
              </div>
            </div>
          </div>

          <!-- CRM 客户管理 -->
          <div class="p-4 rounded-lg border border-gray-100 hover:border-primary transition-colors group cursor-pointer" @click="$router.push('/crm')">
            <div class="flex items-start space-x-4">
              <div class="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center flex-shrink-0">
                <ChartBarIcon class="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h3 class="text-base font-medium text-gray-900">CRM 客户管理</h3>
                <p class="text-sm text-gray-500 mt-1">跟踪潜在客户与签约流程，提升团队协作效率</p>
                <span class="text-primary hover:text-primary-dark text-sm mt-2 inline-block group-hover:underline">进入 CRM ›</span>
              </div>
            </div>
          </div>


        </div>
      </div>

      <!-- 右侧：更新动态 -->
      <div>
        <!-- 更新动态 -->
        <div class="bg-white rounded-xl shadow-sm p-4">
          <div class="flex justify-between items-center mb-3">
            <h2 class="text-base font-semibold text-gray-900">更新</h2>
            <span class="text-xs text-gray-500">What's new</span>
          </div>
          <div class="space-y-3" v-memo="[updates.length]">
            <div v-for="update in updates" :key="update.id" class="border-l-2 border-primary pl-3 py-2">
              <div class="flex items-center space-x-2 text-xs text-gray-500">
                <span>📅</span>
                <span>{{ update.date }}</span>
              </div>
              <h3 class="text-sm font-medium text-gray-900 mt-1">{{ update.title }}</h3>
              <p class="text-xs text-gray-500 mt-1 leading-relaxed">{{ update.content }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import { getClientList } from '@/api/client'
import {
  DocumentTextIcon,
  DocumentIcon,
  PencilSquareIcon,
  UserIcon,
  AcademicCapIcon,
  ChartBarIcon
} from '@heroicons/vue/24/outline'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

dayjs.locale('zh-cn')

const userStore = useUserStore()
const authStore = useAuthStore()
const router = useRouter()

// 用户显示名称
const displayName = computed(() => {
  return authStore.user?.nickname ||
         authStore.user?.username ||
         userStore.userInfo.nickname ||
         userStore.userInfo.username ||
         'Pocky'
})

// 用户角色显示
const userRole = computed(() => {
  return '开发者'
})

// 统计数据
const totalClients = ref(573)
const currentClients = ref(47)
const urgentClients = ref(5)
const userPoints = ref(389)

// 近期客户数据
const recentClients = ref([])
const isLoading = ref(true)

const updates = ref([
  {
    id: 1,
    title: 'TunshuEdu v1.0内测版发布',
    content: '我们很高兴地宣布TunshuEdu v1.0内测版正式上线！本次测试版包含完整的留学申请AI辅助功能，包括智能定校推荐、文书生成与优化、申请材料管理等核心模块。测试期间，所有功能均可免费使用，欢迎提出宝贵意见以帮助我们进一步优化产品体验。',
    date: '2025-08-01'
  }
])

// 添加日期格式化函数
const formatDate = (date) => {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD')
}

// 获取近期客户列表
const fetchRecentClients = async () => {
  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 5000)

    const response = await getClientList({
      limit: 5,
      sort: 'updated_at:desc',
      signal: controller.signal,
      _t: new Date().getTime()
    })

    clearTimeout(timeoutId)

    let clients = []
    if (Array.isArray(response)) {
      clients = response
    } else if (response?.items && Array.isArray(response.items)) {
      clients = response.items
    } else if (response?.data && Array.isArray(response.data)) {
      clients = response.data
    } else {
      console.warn('获取客户列表响应格式不符合预期:', response)
    }
    
    clients.sort((a, b) => {
      const dateA = new Date(a.updated_at || a.updatedAt || 0)
      const dateB = new Date(b.updated_at || b.updatedAt || 0)
      return dateB - dateA
    })
    
    recentClients.value = clients.slice(0, 5)
  } catch (error) {
    if (error.name === 'AbortError') {
      console.warn('获取客户列表请求超时')
    } else {
      console.error('获取近期客户失败:', error)
    }
    recentClients.value = []
  }
}

// 查看客户详情
const handleViewClient = (clientId) => {
  router.push(`/clients/${clientId}`)
}



// 文书撰写点击事件
const handleWritingClick = () => {
  // 可以跳转到文书选择页面，或者显示文书类型选择菜单
  router.push('/write/ps')
}

// 在组件挂载时获取数据
onMounted(() => {
  const minLoadingTimer = setTimeout(() => {
    if (isLoading.value) {
      isLoading.value = false
    }
  }, 800)

  Promise.allSettled([
    userStore.fetchUserInfo(true),
    fetchRecentClients()
  ])
  .then(results => {
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        const apis = ['用户信息', '客户列表']
        console.warn(`${apis[index]}加载失败:`, result.reason)
      }
    })
  })
  .catch(error => {
    console.error('加载数据时出错:', error)
  })
  .finally(() => {
    clearTimeout(minLoadingTimer)
    isLoading.value = false
  })
})
</script>

<style scoped>
/* Material Icons 支持 */
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.grid {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

@media (min-width: 1024px) {
  .grid {
    grid-template-columns: 1fr 1.5fr 1fr;
  }
}

/* 添加渐变动画 */
.bg-gradient-to-br {
  background-size: 200% 200%;
  animation: gradient 6s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 添加卡片悬停效果 */
.rounded-lg {
  transition: all 0.3s ease;
}

.rounded-lg:hover {
  transform: translateY(0px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px 0px rgba(0, 0, 0, 0.05);
}

/* 添加内容加载动画 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(0px); }
  to { opacity: 1; transform: translateY(0); }
}

.bg-white {
  animation: fadeIn 0.3s ease-out forwards;
}
</style>