<template>
  <div class="space-y-4">
    <!-- 任务项列表 -->
    <div
      v-for="task in tasks"
      :key="task.id"
      class="bg-white border border-gray-100 rounded-xl hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-[1.02]"
      :class="getTaskBorderClass(task)"
      @click="handleTaskClick(task)"
    >
      <!-- 任务卡片头部 -->
      <div class="p-4 border-b border-gray-100">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <div class="flex items-center gap-3 mb-2">
              <!-- 紧急程度指示器 -->
              <div 
                class="w-3 h-3 rounded-full"
                :class="getPriorityIndicatorClass(task.priority)"
              ></div>
              
              <!-- 任务标题 -->
              <h3 class="text-lg font-semibold text-gray-900">{{ task.title }}</h3>
              
              <!-- 状态标签 -->
              <el-tag 
                :type="getStatusTagType(task.status)" 
                size="small"
                effect="plain"
              >
                {{ getStatusLabel(task.status) }}
              </el-tag>
            </div>
            
            <!-- 客户信息 -->
            <div class="flex items-center text-sm text-gray-600 mb-2">
              <el-icon class="mr-1"><User /></el-icon>
              <span class="font-medium">{{ task.client_name }}</span>
            </div>
            
            <!-- 任务描述 -->
            <p class="text-sm text-gray-600 line-clamp-2">{{ task.description }}</p>
          </div>
          
          <!-- 操作按钮 -->
          <el-dropdown @command="handleTaskAction" trigger="click">
            <el-button 
              type="text" 
              size="small" 
              class="text-[#4F46E5] hover:text-[#4338CA] hover:bg-[#4F46E5]/10 rounded-lg p-2"
            >
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="{action: 'edit', task}">
                  <span class="text-[#4F46E5]">编辑任务</span>
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'complete', task}" v-if="task.status !== 'completed'">
                  <span class="text-green-600">标记完成</span>
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'reopen', task}" v-if="task.status === 'completed'">
                  <span class="text-[#4F46E5]">重新打开</span>
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'delete', task}" divided>
                  <span class="text-red-600">删除任务</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      
      <!-- 任务卡片底部信息 -->
      <div class="p-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- 截止日期 -->
          <div class="flex items-center">
            <el-icon class="text-gray-400 mr-2"><Calendar /></el-icon>
            <div>
              <p class="text-xs text-gray-500">截止日期</p>
              <p 
                class="text-sm font-medium"
                :class="getDueDateClass(task.due_date)"
              >
                {{ formatDueDate(task.due_date) }}
              </p>
            </div>
          </div>
          
          <!-- 优先级 -->
          <div class="flex items-center">
            <el-icon class="text-gray-400 mr-2"><Flag /></el-icon>
            <div>
              <p class="text-xs text-gray-500">优先级</p>
              <div class="flex items-center">
                <span 
                  class="text-sm font-medium"
                  :class="getPriorityTextClass(task.priority)"
                >
                  {{ getPriorityLabel(task.priority) }}
                </span>
              </div>
            </div>
          </div>
          
          <!-- 创建时间 -->
          <div class="flex items-center">
            <el-icon class="text-gray-400 mr-2"><Clock /></el-icon>
            <div>
              <p class="text-xs text-gray-500">创建时间</p>
              <p class="text-sm text-gray-700">{{ formatDate(task.created_at) }}</p>
            </div>
          </div>
        </div>
        
        <!-- 进度条（针对进行中的任务） -->
        <div v-if="task.status === 'in_progress' && task.progress !== undefined" class="mt-4">
          <div class="flex justify-between items-center mb-2">
            <span class="text-xs text-gray-500">任务进度</span>
            <span class="text-xs font-medium text-gray-700">{{ task.progress || 0 }}%</span>
          </div>
          <el-progress 
            :percentage="task.progress || 0" 
            :stroke-width="4"
            :show-text="false"
            :color="getProgressColor(task.progress || 0)"
          />
        </div>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-if="tasks.length === 0" class="text-center py-12">
      <div class="text-gray-400 mb-4">
        <el-icon size="48"><DocumentCopy /></el-icon>
      </div>
      <p class="text-gray-500">该阶段暂无任务</p>
    </div>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  User, MoreFilled, Calendar, Flag, Clock, DocumentCopy 
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  tasks: {
    type: Array,
    required: true,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['task-click', 'task-update'])

// 方法
const handleTaskClick = (task) => {
  emit('task-click', task)
}

const handleTaskAction = async ({ action, task }) => {
  if (action === 'edit') {
    // 编辑任务逻辑
    emit('task-click', task)
  } else if (action === 'complete') {
    try {
      await ElMessageBox.confirm('确定要标记该任务为已完成吗？', '确认操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'success'
      })
      
      const updatedTask = { ...task, status: 'completed' }
      emit('task-update', updatedTask)
      ElMessage.success('任务已标记为完成')
    } catch (error) {
      // 用户取消操作
    }
  } else if (action === 'reopen') {
    const updatedTask = { ...task, status: 'pending' }
    emit('task-update', updatedTask)
    ElMessage.success('任务已重新打开')
  } else if (action === 'delete') {
    try {
      await ElMessageBox.confirm('确定要删除该任务吗？此操作不可恢复。', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      emit('task-update', { ...task, _action: 'delete' })
      ElMessage.success('任务删除成功')
    } catch (error) {
      // 用户取消操作
    }
  }
}

// 样式相关方法
const getTaskBorderClass = (task) => {
  if (task.priority === 'urgent') {
    return 'border-l-4 border-l-red-500 hover:border-l-red-600'
  } else if (task.priority === 'important') {
    return 'border-l-4 border-l-orange-500 hover:border-l-orange-600'
  } else if (isOverdue(task.due_date)) {
    return 'border-l-4 border-l-red-400 hover:border-l-red-500'
  }
  return 'hover:border-gray-300'
}

const getPriorityIndicatorClass = (priority) => {
  const classMap = {
    urgent: 'bg-red-500 animate-pulse',
    important: 'bg-orange-500',
    normal: 'bg-[#4F46E5]',
    low: 'bg-gray-400'
  }
  return classMap[priority] || 'bg-gray-400'
}

const getPriorityTextClass = (priority) => {
  const classMap = {
    urgent: 'text-red-600',
    important: 'text-orange-600',
    normal: 'text-[#4F46E5]',
    low: 'text-gray-600'
  }
  return classMap[priority] || 'text-gray-600'
}

const getPriorityLabel = (priority) => {
  const labelMap = {
    urgent: '紧急',
    important: '重要',
    normal: '普通',
    low: '低优先级'
  }
  return labelMap[priority] || '未知'
}

const getStatusTagType = (status) => {
  const typeMap = {
    pending: 'warning',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusLabel = (status) => {
  const labelMap = {
    pending: '待处理',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return labelMap[status] || '未知状态'
}

const getDueDateClass = (dueDate) => {
  if (isOverdue(dueDate)) {
    return 'text-red-600'
  } else if (isDueSoon(dueDate)) {
    return 'text-orange-600'
  }
  return 'text-gray-700'
}

const getProgressColor = (progress) => {
  if (progress >= 80) return '#67C23A'
  if (progress >= 60) return '#E6A23C'
  if (progress >= 30) return '#F56C6C'
  return '#909399'
}

// 工具方法
const isOverdue = (dueDate) => {
  return new Date(dueDate) < new Date()
}

const isDueSoon = (dueDate) => {
  const due = new Date(dueDate)
  const now = new Date()
  const diffInDays = (due - now) / (1000 * 60 * 60 * 24)
  return diffInDays <= 3 && diffInDays > 0
}

const formatDueDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInDays = Math.ceil((date - now) / (1000 * 60 * 60 * 24))
  
  if (diffInDays < 0) {
    return `逾期 ${Math.abs(diffInDays)} 天`
  } else if (diffInDays === 0) {
    return '今天到期'
  } else if (diffInDays === 1) {
    return '明天到期'
  } else if (diffInDays <= 7) {
    return `${diffInDays} 天后到期`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style> 