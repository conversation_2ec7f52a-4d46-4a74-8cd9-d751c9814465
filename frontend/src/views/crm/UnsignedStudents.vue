<template>
  <div class="crm-container p-6">
    <!-- 页面标题和操作区域 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">潜在客户</h1>
        <div class="flex items-center mt-2 text-sm text-gray-500">
          <span class="mr-4">客户列表</span>
          <span class="text-gray-300">/</span>
          <span class="ml-4 text-[#4F46E5] font-medium">潜在客户</span>
        </div>
      </div>
      <div class="flex items-center space-x-3">
                  <el-button 
            @click="handleBatchSignContract"
            :disabled="selectedStudents.length === 0"
            size="large"
            class="border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-white transition-all duration-200"
          >
            <el-icon class="mr-2"><Document /></el-icon>
            批量签约
          </el-button>
          <el-button 
            @click="handleBatchDelete"
            :disabled="selectedStudents.length === 0"
            size="large"
            class="border-red-500 text-red-500 hover:bg-red-500 hover:text-white transition-all duration-200"
          >
            <el-icon class="mr-2"><Delete /></el-icon>
            批量删除
          </el-button>
          <el-button 
            type="primary" 
            @click="handleAddStudent" 
            class="bg-[#4F46E5] hover:bg-[#4338CA] border-[#4F46E5] hover:border-[#4338CA] shadow-lg hover:shadow-xl transition-all duration-200"
            size="large"
          >
            <el-icon class="mr-2"><Plus /></el-icon>
            添加客户
          </el-button>
      </div>
    </div>

    <!-- 统计概览卡片 -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
      <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 font-medium">总潜在客户</p>
            <p class="text-3xl font-bold text-gray-900 mt-2">{{ statistics.total }}</p>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-[#4F46E5] to-[#6366F1] rounded-xl flex items-center justify-center shadow-lg">
            <el-icon class="text-white text-xl"><User /></el-icon>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 font-medium">初次咨询</p>
            <p class="text-3xl font-bold text-blue-600 mt-2">{{ statistics.initial }}</p>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
            <el-icon class="text-white text-xl"><Plus /></el-icon>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 font-medium">跟进中</p>
            <p class="text-3xl font-bold text-yellow-600 mt-2">{{ statistics.following }}</p>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center shadow-lg">
            <el-icon class="text-white text-xl"><Search /></el-icon>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 font-medium">意向强烈</p>
            <p class="text-3xl font-bold text-green-600 mt-2">{{ statistics.interested }}</p>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
            <el-icon class="text-white text-xl"><Flag /></el-icon>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 font-medium">待签约</p>
            <p class="text-3xl font-bold text-red-600 mt-2">{{ statistics.pending }}</p>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg">
            <el-icon class="text-white text-xl"><Document /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 mb-6">
      <div class="flex items-center gap-4">
        <div class="flex-1">
                      <el-input
              v-model="searchQuery"
              placeholder="搜索客户姓名、院校、专业"
              clearable
              @input="handleSearch"
              class="w-full"
              size="large"
            >
            <template #prefix>
              <el-icon class="text-[#4F46E5]"><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div class="flex-1">
          <el-select 
            v-model="statusFilter" 
            placeholder="客户状态" 
            clearable 
            class="w-full"
            size="large"
          >
            <el-option label="全部" value="" />
            <el-option label="初次咨询" value="initial" />
            <el-option label="跟进中" value="following" />
            <el-option label="意向强烈" value="interested" />
            <el-option label="待签约" value="pending" />
          </el-select>
        </div>
        <div class="flex-1">
          <el-select 
            v-model="consultantFilter" 
            placeholder="规划顾问" 
            clearable 
            class="w-full"
            size="large"
          >
            <el-option label="全部" value="" />
            <el-option label="李老师" value="李老师" />
            <el-option label="王老师" value="王老师" />
            <el-option label="张老师" value="张老师" />
            <el-option label="刘老师" value="刘老师" />
          </el-select>
        </div>
        <el-button 
          type="primary" 
          @click="handleSearch"
          size="large"
          class="bg-[#4F46E5] hover:bg-[#4338CA] border-[#4F46E5] hover:border-[#4338CA] shadow-lg hover:shadow-xl transition-all duration-200"
        >
          <el-icon class="mr-1"><Search /></el-icon>
          搜索
        </el-button>
        <el-button 
          @click="handleResetFilter"
          size="large"
        >
          重置筛选
        </el-button>
      </div>
    </div>

    <!-- 学生列表 -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
      <!-- 批量操作栏 -->
      <div v-if="selectedStudents.length > 0" class="bg-[#4F46E5] bg-opacity-10 p-4 border-b">
        <div class="flex items-center justify-between">
          <span class="text-[#4F46E5] font-medium">
            已选择 {{ selectedStudents.length }} 个客户
          </span>
          <div class="flex space-x-2">
            <el-button 
              size="small"
              @click="handleBatchSignContract"
              class="bg-yellow-500 hover:bg-yellow-600 border-yellow-500 text-white"
            >
              批量签约
            </el-button>
            <el-button 
              size="small"
              @click="handleBatchDelete"
              class="bg-red-500 hover:bg-red-600 border-red-500 text-white"
            >
              批量删除
            </el-button>
          </div>
        </div>
      </div>

      <el-table
        :data="filteredStudents"
        style="width: 100%"
        :header-cell-style="{
          background: 'linear-gradient(135deg, #4F46E5 0%, #6366F1 100%)',
          color: '#ffffff',
          fontWeight: 600,
          fontSize: '14px',
          height: '64px',
          borderBottom: 'none'
        }"
        :cell-style="{
          fontSize: '14px',
          color: '#374151',
          height: '64px',
          borderBottom: '1px solid #F3F4F6'
        }"
        @row-click="handleStudentClick"
        @selection-change="handleStudentSelectionChange"
        class="student-table"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="客户姓名" min-width="100">
          <template #default="{ row }">
            <div class="flex items-center">
              <div class="w-10 h-10 rounded-full bg-[#4F46E5] bg-opacity-10 flex items-center justify-center text-[#4F46E5] mr-3 text-sm font-bold">
                {{ getStudentInitials(row.name) }}
              </div>
              <span class="font-semibold text-gray-900">{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="university" label="在读院校" min-width="120" />
        <el-table-column prop="schoolType" label="学校类型" width="100" />
        <el-table-column prop="major" label="在读专业" min-width="120" />
        <el-table-column prop="gpa" label="均分绩点" width="90" />
        <el-table-column prop="grade" label="在读年级" width="90">
          <template #default="{ row }">
            {{ getGradeLabel(row.grade) }}
          </template>
        </el-table-column>
        <el-table-column prop="targetCountry" label="申请国家(地区)" width="110">
          <template #default="{ row }">
            {{ getCountryLabel(row.targetCountry) }}
          </template>
        </el-table-column>
        <el-table-column prop="applicationYear" label="申请入读年份" width="110" />
        
        <el-table-column label="客户状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="consultant" label="规划顾问" min-width="80" />
        <el-table-column prop="contact" label="备注" min-width="150" />
        
        <el-table-column label="操作" width="140" fixed="right">
          <template #default="{ row }">
            <div class="flex items-center space-x-2" @click.stop>
              <el-button 
                type="primary" 
                size="small" 
                @click="handleEditStudent(row)"
                class="bg-[#4F46E5] hover:bg-[#4338CA] border-[#4F46E5] text-white"
                plain
              >
                编辑
              </el-button>
              <el-dropdown trigger="click">
                <el-button 
                  type="text" 
                  size="small" 
                  class="text-[#4F46E5] hover:text-[#4338CA] hover:bg-[#4F46E5]/10"
                >
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleSignContract(row)">
                      <el-icon class="mr-1 text-yellow-500"><Document /></el-icon>
                      <span class="text-yellow-600">签约</span>
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleDeleteStudent(row)" divided>
                      <el-icon class="mr-1 text-red-500"><Delete /></el-icon>
                      <span class="text-red-600">删除</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex justify-between items-center py-4 px-6">
        <div class="text-sm text-gray-500">
          共 {{ total }} 条记录
        </div>
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="total"
          layout="sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          background
          class="pagination-custom"
        />
      </div>
    </div>



    <!-- 空状态 -->
    <div v-if="filteredStudents.length === 0" class="text-center py-12">
      <el-empty description="暂无潜在客户">
        <el-button type="primary" @click="handleAddStudent">添加第一个客户</el-button>
      </el-empty>
    </div>

    <!-- 学生表单对话框 -->
    <el-dialog
      v-model="studentDialogVisible"
      :title="isViewMode ? '客户详情' : (isEditMode ? '编辑客户' : '添加客户')"
      width="900px"
      destroy-on-close
      top="5vh"
    >
      <el-form
        ref="studentFormRef"
        :model="studentFormData"
        :rules="studentFormRules"
        label-width="120px"
        size="default"
        :disabled="isViewMode"
      >
        <!-- 基本信息 -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <el-icon class="mr-2 text-primary"><User /></el-icon>
            基本信息
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="客户姓名" prop="name" required>
              <el-input v-model="studentFormData.name" placeholder="请输入客户姓名" clearable />
            </el-form-item>
            <el-form-item label="性别" prop="gender">
              <el-radio-group v-model="studentFormData.gender">
                <el-radio value="male">男</el-radio>
                <el-radio value="female">女</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="studentFormData.phone" placeholder="请输入联系电话" clearable />
            </el-form-item>
            <el-form-item label="邮箱地址" prop="email">
              <el-input v-model="studentFormData.email" placeholder="请输入邮箱地址" clearable />
            </el-form-item>
            <el-form-item label="出生日期" prop="birthDate">
              <el-date-picker v-model="studentFormData.birthDate" type="date" placeholder="请选择出生日期" class="w-full" />
            </el-form-item>
            <el-form-item label="家长联系方式" prop="parentContact">
              <el-input v-model="studentFormData.parentContact" placeholder="请输入家长联系方式" clearable />
            </el-form-item>
          </div>
        </div>

        <!-- 教育背景 -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <el-icon class="mr-2 text-primary"><School /></el-icon>
            教育背景
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="当前院校" prop="university">
              <el-input v-model="studentFormData.university" placeholder="请输入当前就读院校" clearable />
            </el-form-item>
            <el-form-item label="学校类型" prop="schoolType">
              <el-select v-model="studentFormData.schoolType" placeholder="请选择学校类型" class="w-full">
                <el-option label="985/211" value="985/211" />
                <el-option label="985" value="985" />
                <el-option label="211" value="211" />
                <el-option label="一本" value="一本" />
                <el-option label="二本" value="二本" />
                <el-option label="三本" value="三本" />
                <el-option label="专科" value="专科" />
                <el-option label="海外院校" value="海外院校" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
            <el-form-item label="专业" prop="major">
              <el-input v-model="studentFormData.major" placeholder="请输入专业" clearable />
            </el-form-item>
            <el-form-item label="年级" prop="grade">
              <el-select v-model="studentFormData.grade" placeholder="请选择年级" class="w-full">
                <el-option label="高一" value="grade10" />
                <el-option label="高二" value="grade11" />
                <el-option label="高三" value="grade12" />
                <el-option label="大一" value="freshman" />
                <el-option label="大二" value="sophomore" />
                <el-option label="大三" value="junior" />
                <el-option label="大四" value="senior" />
                <el-option label="研一" value="graduate1" />
                <el-option label="研二" value="graduate2" />
              </el-select>
            </el-form-item>
            <el-form-item label="GPA" prop="gpa">
              <el-input v-model="studentFormData.gpa" placeholder="请输入GPA" clearable />
            </el-form-item>
          </div>
        </div>

        <!-- 申请意向 -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <el-icon class="mr-2 text-primary"><Flag /></el-icon>
            申请意向
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="目标国家" prop="targetCountry">
              <el-select v-model="studentFormData.targetCountry" placeholder="请选择目标国家" class="w-full">
                <el-option label="美国" value="US" />
                <el-option label="英国" value="UK" />
                <el-option label="加拿大" value="CA" />
                <el-option label="澳大利亚" value="AU" />
                <el-option label="新加坡" value="SG" />
                <el-option label="中国香港" value="HK" />
                <el-option label="其他" value="OTHER" />
              </el-select>
            </el-form-item>
            <el-form-item label="目标学位" prop="targetDegree">
              <el-select v-model="studentFormData.targetDegree" placeholder="请选择目标学位" class="w-full">
                <el-option label="本科" value="bachelor" />
                <el-option label="硕士" value="master" />
                <el-option label="博士" value="phd" />
              </el-select>
            </el-form-item>
            <el-form-item label="目标专业" prop="targetMajor">
              <el-input v-model="studentFormData.targetMajor" placeholder="请输入目标申请专业" clearable />
            </el-form-item>
            <el-form-item label="申请入读年份" prop="applicationYear">
              <el-select v-model="studentFormData.applicationYear" placeholder="请选择申请入读年份" class="w-full">
                <el-option label="2024" value="2024" />
                <el-option label="2025" value="2025" />
                <el-option label="2026" value="2026" />
                <el-option label="2027" value="2027" />
                <el-option label="2028" value="2028" />
                <el-option label="2029" value="2029" />
                <el-option label="2030" value="2030" />
              </el-select>
            </el-form-item>
            <el-form-item label="语言成绩" prop="languageScores">
              <el-input v-model="studentFormData.languageScores" placeholder="如：TOEFL 100, IELTS 7.0" clearable />
            </el-form-item>
          </div>
        </div>

        <!-- 服务信息 -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <el-icon class="mr-2 text-primary"><Setting /></el-icon>
            服务信息
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="客户状态" prop="status">
              <el-select v-model="studentFormData.status" placeholder="请选择客户状态" class="w-full">
                <el-option label="初次咨询" value="initial" />
                <el-option label="跟进中" value="following" />
                <el-option label="意向强烈" value="interested" />
                <el-option label="待签约" value="pending" />
              </el-select>
            </el-form-item>
            <el-form-item label="负责顾问" prop="consultant">
              <el-select v-model="studentFormData.consultant" placeholder="请选择负责顾问" class="w-full">
                <el-option label="李老师" value="李老师" />
                <el-option label="王老师" value="王老师" />
                <el-option label="张老师" value="张老师" />
                <el-option label="刘老师" value="刘老师" />
              </el-select>
            </el-form-item>
            <el-form-item label="来源渠道" prop="source" v-if="!isEditMode">
              <el-select v-model="studentFormData.source" placeholder="请选择来源渠道" class="w-full">
                <el-option label="网络咨询" value="online" />
                <el-option label="朋友推荐" value="referral" />
                <el-option label="线下活动" value="event" />
                <el-option label="广告投放" value="advertisement" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 备注信息 -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <el-icon class="mr-2 text-primary"><Document /></el-icon>
            备注信息
          </h3>
          <el-form-item label="备注说明" prop="notes">
            <el-input
              v-model="studentFormData.notes"
              type="textarea"
              :rows="4"
              placeholder="请输入备注信息，如学生特殊需求、家庭背景等"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-form>

      <template #footer v-if="!isViewMode">
        <span class="dialog-footer flex justify-end space-x-3 pt-6">
          <el-button 
            @click="handleStudentFormCancel"
            size="large"
            class="border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            取消
          </el-button>
          <el-button 
            type="primary" 
            @click="handleStudentFormSubmit"
            :loading="studentFormSubmitting"
            size="large"
            class="bg-[#4F46E5] hover:bg-[#4338CA] border-[#4F46E5] shadow-lg hover:shadow-xl transition-all duration-200"
          >
            {{ isEditMode ? '更新客户信息' : '创建客户档案' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 签约对话框 -->
    <el-dialog
      v-model="signContractDialogVisible"
      title="客户签约"
      width="500px"
      destroy-on-close
    >
      <div v-if="selectedStudent">
        <div class="mb-4">
          <p class="text-gray-600">确认与客户 <strong>{{ selectedStudent.name }}</strong> 签订服务合同？</p>
        </div>
        <el-form :model="contractForm" label-width="100px">
          <el-form-item label="合同类型">
            <el-select v-model="contractForm.type" class="w-full">
              <el-option label="标准服务包" value="standard" />
              <el-option label="高端服务包" value="premium" />
              <el-option label="定制服务包" value="custom" />
            </el-select>
          </el-form-item>
          <el-form-item label="服务期限">
            <el-date-picker
              v-model="contractForm.duration"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="w-full"
            />
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="contractForm.notes"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer flex justify-end space-x-3 pt-6">
          <el-button 
            @click="signContractDialogVisible = false"
            size="large"
            class="border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            取消
          </el-button>
          <el-button 
            type="primary" 
            @click="handleConfirmSign" 
            :loading="signing"
            size="large"
            class="bg-[#4F46E5] hover:bg-[#4338CA] border-[#4F46E5] shadow-lg hover:shadow-xl transition-all duration-200"
          >
            确认签约
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量签约对话框 -->
    <el-dialog
      v-model="batchSignContractDialogVisible"
      title="批量签约"
      width="600px"
      destroy-on-close
    >
      <div class="mb-4">
        <p class="text-gray-600 mb-4">
          将为选中的 <strong>{{ selectedStudents.length }}</strong> 个客户进行批量签约：
        </p>
        <div class="bg-gray-50 p-3 rounded-lg mb-4 max-h-32 overflow-y-auto">
          <div v-for="student in selectedStudents" :key="student.id" class="text-sm text-gray-700 mb-1">
            {{ student.name }} - {{ student.university }}
          </div>
        </div>
      </div>
      
      <el-form :model="batchContractForm" label-width="100px">
        <el-form-item label="合同类型" required>
          <el-select v-model="batchContractForm.type" class="w-full">
            <el-option label="标准服务包" value="standard" />
            <el-option label="高端服务包" value="premium" />
            <el-option label="定制服务包" value="custom" />
          </el-select>
        </el-form-item>
        <el-form-item label="服务期限">
          <el-date-picker
            v-model="batchContractForm.duration"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="w-full"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="batchContractForm.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer flex justify-end space-x-3 pt-6">
          <el-button 
            @click="batchSignContractDialogVisible = false"
            size="large"
          >
            取消
          </el-button>
          <el-button 
            type="primary" 
            @click="handleConfirmBatchSign"
            :disabled="!batchContractForm.type"
            size="large"
            class="bg-[#4F46E5] hover:bg-[#4338CA] border-[#4F46E5] shadow-lg hover:shadow-xl transition-all duration-200"
          >
            确认批量签约
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Search, MoreFilled, Document, Delete, User, School, Flag, Setting
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const searchQuery = ref('')
const statusFilter = ref('')
const consultantFilter = ref('')
const students = ref([])
const selectedStudents = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 统计数据
const statistics = ref({
  total: 0,
  initial: 0,
  following: 0,
  interested: 0,
  pending: 0
})

// 签约相关
const signContractDialogVisible = ref(false)
const batchSignContractDialogVisible = ref(false)
const selectedStudent = ref(null)
const signing = ref(false)
const contractForm = ref({
  type: 'standard',
  duration: [],
  notes: ''
})
const batchContractForm = ref({
  type: 'standard',
  duration: [],
  notes: ''
})

// 学生表单对话框相关
const studentDialogVisible = ref(false)
const studentFormData = ref({
  // 基本信息
  name: '',
  gender: 'male',
  phone: '',
  email: '',
  birthDate: null,
  parentContact: '',
  
  // 教育背景
  university: '',
  schoolType: '',
  major: '',
  grade: '',
  gpa: '',
  enrollmentDate: null,
  graduationDate: null,
  
  // 申请意向
  targetCountry: '',
  targetDegree: '',
  targetMajor: '',
  applicationYear: '',
  expectedApplicationDate: null,
  languageScores: '',
  standardizedTests: '',
  
  // 服务信息
  status: 'initial',
  consultant: '',
  source: '',
  
  // 备注
  notes: ''
})
const isEditMode = ref(false)
const isViewMode = ref(false)
const studentFormSubmitting = ref(false)
const studentFormRef = ref()

// 表单验证规则
const studentFormRules = {
  name: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择客户状态', trigger: 'change' }
  ],
  consultant: [
    { required: true, message: '请选择负责顾问', trigger: 'change' }
  ]
}

// 计算属性
const filteredStudents = computed(() => {
  let filtered = students.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(student => 
      student.name.toLowerCase().includes(query) ||
      student.university.toLowerCase().includes(query) ||
      student.major.toLowerCase().includes(query)
    )
  }

  if (statusFilter.value) {
    filtered = filtered.filter(student => student.status === statusFilter.value)
  }

  if (consultantFilter.value) {
    filtered = filtered.filter(student => student.consultant === consultantFilter.value)
  }

  return filtered
})

// 计算统计数据
const calculateStatistics = () => {
  const totalCount = students.value.length
  const initialCount = students.value.filter(s => s.status === 'initial').length
  const followingCount = students.value.filter(s => s.status === 'following').length
  const interestedCount = students.value.filter(s => s.status === 'interested').length
  const pendingCount = students.value.filter(s => s.status === 'pending').length

  statistics.value = {
    total: totalCount,
    initial: initialCount,
    following: followingCount,
    interested: interestedCount,
    pending: pendingCount
  }
}

// 方法
const getStudentInitials = (name) => {
  if (!name) return 'N'
  const names = name.split(' ')
  if (names.length === 1) {
    return names[0].charAt(0).toUpperCase()
  }
  return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase()
}

const getStatusTagType = (status) => {
  const statusMap = {
    initial: '',
    following: 'warning',
    interested: 'success',
    pending: 'danger'
  }
  return statusMap[status] || ''
}

const getStatusLabel = (status) => {
  const statusMap = {
    initial: '初次咨询',
    following: '跟进中',
    interested: '意向强烈',
    pending: '待签约'
  }
  return statusMap[status] || '未知'
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN')
}

const getGradeLabel = (grade) => {
  const gradeMap = {
    grade10: '高一',
    grade11: '高二',
    grade12: '高三',
    freshman: '大一',
    sophomore: '大二',
    junior: '大三',
    senior: '大四',
    graduate1: '研一',
    graduate2: '研二'
  }
  return gradeMap[grade] || '未知'
}

const getCountryLabel = (country) => {
  const countryMap = {
    US: '美国',
    UK: '英国',
    CA: '加拿大',
    AU: '澳大利亚',
    SG: '新加坡',
    HK: '中国香港',
    OTHER: '其他'
  }
  return countryMap[country] || '未知'
}

const handleSearch = () => {
  // 搜索逻辑
  currentPage.value = 1
  selectedStudents.value = [] // 清空选择
  loadStudents()
}

const handleResetFilter = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  consultantFilter.value = ''
  currentPage.value = 1
  selectedStudents.value = [] // 清空选择
  loadStudents()
}

const handleAddStudent = () => {
  // 打开添加学生对话框
  openStudentDialog()
}

const handleStudentClick = (student) => {
  // 打开学生详情对话框
  openStudentDialog(student, true) // 第二个参数表示只读模式
}

const handleEditStudent = (student) => {
  // 打开编辑学生对话框
  openStudentDialog(student)
}

const handleSignContract = (student) => {
  selectedStudent.value = student
  signContractDialogVisible.value = true
  contractForm.value = {
    type: 'standard',
    duration: [],
    notes: ''
  }
}

const handleConfirmSign = async () => {
  try {
    signing.value = true
    // TODO: 调用签约API
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
    
    ElMessage.success('签约成功！客户已转移到我的客户列表')
    signContractDialogVisible.value = false
    selectedStudents.value = [] // 清空选择
    loadStudents() // 刷新列表
  } catch (error) {
    ElMessage.error('签约失败，请重试')
  } finally {
    signing.value = false
  }
}

const handleDeleteStudent = async (student) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除客户 ${student.name} 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用删除API
    ElMessage.success('删除成功')
    selectedStudents.value = [] // 清空选择
    loadStudents()
  } catch (error) {
    // 用户取消删除
  }
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  selectedStudents.value = [] // 清空选择
  loadStudents()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  selectedStudents.value = [] // 清空选择
  loadStudents()
}

// 选择相关处理函数
const handleStudentSelectionChange = (selection) => {
  selectedStudents.value = selection
}

const handleBatchSignContract = () => {
  if (selectedStudents.value.length === 0) {
    ElMessage.warning('请先选择要签约的客户')
    return
  }
  
  batchContractForm.value = {
    type: 'standard',
    duration: [],
    notes: ''
  }
  batchSignContractDialogVisible.value = true
}

const handleConfirmBatchSign = async () => {
  try {
    // TODO: 调用批量签约API
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
    
    ElMessage.success('批量签约成功！客户已转移到我的客户列表')
    batchSignContractDialogVisible.value = false
    selectedStudents.value = []
    loadStudents()
  } catch (error) {
    ElMessage.error('批量签约失败，请重试')
  }
}

const handleBatchDelete = async () => {
  if (selectedStudents.value.length === 0) {
    ElMessage.warning('请先选择要删除的客户')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedStudents.value.length} 个客户吗？此操作不可撤销！`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用批量删除API
    ElMessage.success('批量删除成功')
    selectedStudents.value = []
    loadStudents()
  } catch (error) {
    // 用户取消
  }
}

// 学生表单对话框处理函数
const openStudentDialog = (student = null, viewOnly = false) => {
  isViewMode.value = viewOnly
  isEditMode.value = student !== null && !viewOnly
  
  if (student) {
    // 编辑或查看模式，填充数据
    Object.keys(studentFormData.value).forEach(key => {
      if (student.hasOwnProperty(key)) {
        studentFormData.value[key] = student[key]
      }
    })
  } else {
    // 新增模式，重置表单
    resetStudentForm()
  }
  
  studentDialogVisible.value = true
}

const resetStudentForm = () => {
  studentFormData.value = {
    name: '',
    gender: 'male',
    phone: '',
    email: '',
    birthDate: null,
    parentContact: '',
    university: '',
    schoolType: '',
    major: '',
    grade: '',
    gpa: '',
    enrollmentDate: null,
    graduationDate: null,
    targetCountry: '',
    targetDegree: '',
    targetMajor: '',
    applicationYear: '',
    expectedApplicationDate: null,
    languageScores: '',
    standardizedTests: '',
    status: 'initial',
    consultant: '',
    source: '',
    notes: ''
  }
}

const handleStudentFormSubmit = async () => {
  try {
    // 验证表单
    await studentFormRef.value.validate()
    
    studentFormSubmitting.value = true

    // TODO: 调用API保存数据
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用

    ElMessage.success(isEditMode.value ? '客户信息更新成功' : '客户档案创建成功')
    
    studentDialogVisible.value = false
    selectedStudents.value = [] // 清空选择
    loadStudents() // 刷新列表

  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(isEditMode.value ? '更新失败，请重试' : '创建失败，请重试')
    }
  } finally {
    studentFormSubmitting.value = false
  }
}

const handleStudentFormCancel = () => {
  studentDialogVisible.value = false
}

const loadStudents = async () => {
  try {
    loading.value = true
    // TODO: 实际API调用
    // 模拟数据
    students.value = [
      {
        id: 1,
        name: '张三',
        university: '北京大学',
        schoolType: '985/211',
        major: '计算机科学',
        gpa: '3.8',
        grade: 'senior',
        targetCountry: 'US',
        applicationYear: '2025',
        status: 'interested',
        consultant: '李老师',
        contact: '有留学意向',
        created_at: '2024-01-15'
      },
      {
        id: 2,
        name: '李四',
        university: '清华大学',
        schoolType: '985/211',
        major: '电子工程',
        gpa: '3.9',
        grade: 'senior',
        targetCountry: 'US',
        applicationYear: '2025',
        status: 'pending',
        consultant: '王老师',
        contact: '准备申请美国研究生',
        created_at: '2024-01-10'
      },
      {
        id: 3,
        name: '王小明',
        university: '复旦大学',
        schoolType: '985/211',
        major: '金融学',
        gpa: '3.7',
        grade: 'junior',
        targetCountry: 'UK',
        applicationYear: '2026',
        status: 'following',
        consultant: '张老师',
        contact: '正在了解申请流程',
        created_at: '2024-01-20'
      },
      {
        id: 4,
        name: '刘小红',
        university: '上海交通大学',
        schoolType: '985',
        major: '机械工程',
        gpa: '3.6',
        grade: 'sophomore',
        targetCountry: 'CA',
        applicationYear: '2027',
        status: 'initial',
        consultant: '刘老师',
        contact: '初次咨询',
        created_at: '2024-01-25'
      }
    ]
    total.value = students.value.length
    calculateStatistics() // 计算统计数据
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadStudents()
})
</script>

<style>
@import './styles/crm-theme.css';
</style>

<style scoped>
.student-table :deep(.el-table__row) {
  cursor: pointer;
}

.student-table :deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

.pagination-custom :deep(.el-pagination) {
  justify-content: flex-end;
}
</style> 