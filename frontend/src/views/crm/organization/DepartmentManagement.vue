<template>
  <div class="crm-container p-6">
    <!-- 页面标题和操作区域 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">成员与部门</h1>
        <div class="flex items-center mt-2 text-sm text-gray-500">
          <span class="mr-4">组织管理</span>
          <span class="text-gray-300">/</span>
          <span class="ml-4 text-[#4F46E5] font-medium">成员与部门</span>
        </div>
      </div>
      <div class="flex items-center space-x-3">
        <el-button 
          @click="handleBatchDisable"
          :disabled="selectedMembers.length === 0"
          size="large"
          class="border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white transition-all duration-200"
        >
          <el-icon class="mr-2"><Lock /></el-icon>
          批量停用
        </el-button>
        <el-button 
          @click="handleBatchChangeDepartment"
          :disabled="selectedMembers.length === 0"
          size="large"
          class="border-[#4F46E5] text-[#4F46E5] hover:bg-[#4F46E5] hover:text-white transition-all duration-200"
        >
          <el-icon class="mr-2"><Switch /></el-icon>
          变更部门
        </el-button>
        <el-button 
          type="primary" 
          @click="handleAddMember"
          size="large"
          class="bg-[#4F46E5] hover:bg-[#4338CA] border-[#4F46E5] hover:border-[#4338CA] shadow-lg hover:shadow-xl transition-all duration-200"
        >
          <el-icon class="mr-2"><Plus /></el-icon>
          添加成员
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
      <!-- 左侧部门树 -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-4">
          <div class="mb-4">
            <div class="flex items-center justify-between mb-3">
              <div>
                <h4 class="text-sm text-gray-500 mb-1">组织架构</h4>
                <h3 class="text-lg font-medium text-gray-900">{{ companyName }}</h3>
              </div>
              <el-button 
                type="primary" 
                size="small"
                @click="handleAddDepartment"
                class="bg-[#4F46E5] hover:bg-[#4338CA]"
              >
                <el-icon class="mr-1"><Plus /></el-icon>
                新增部门
              </el-button>
            </div>
            <div class="text-xs text-gray-400 mb-4">部门结构</div>
          </div>
          
          <el-input
            v-model="deptSearchQuery"
            placeholder="搜索部门名或成员名"
            clearable
            class="mb-4"
            size="default"
            @input="handleDeptSearch"
          >
            <template #prefix>
              <el-icon class="text-[#4F46E5]"><Search /></el-icon>
            </template>
          </el-input>
          
          <el-tree
            :data="departmentTree"
            :props="{ children: 'children', label: 'name' }"
            :filter-node-method="filterDepartmentNode"
            :expand-on-click-node="false"
            :default-expand-all="false"
            ref="deptTreeRef"
            @node-click="handleDeptNodeClick"
            class="dept-tree"
            node-key="id"
            :highlight-current="true"
          >
            <template #default="{ node, data }">
              <div class="flex items-center justify-between w-full">
                <div class="flex items-center flex-1">
                  <el-icon class="mr-2 text-[#4F46E5]"><OfficeBuilding /></el-icon>
                  <span class="flex-1 text-sm">{{ data.name }}</span>
                  <span class="text-xs text-gray-500 ml-1">({{ data.memberCount || 0 }})</span>
                </div>
                <el-dropdown 
                  @command="handleDeptAction"
                  trigger="click"
                  @click.stop
                >
                  <el-button 
                    type="text" 
                    size="small"
                    class="text-gray-400 hover:text-[#4F46E5] ml-2"
                  >
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{action: 'edit', dept: data}">
                        <el-icon class="mr-1 text-[#4F46E5]"><Edit /></el-icon>
                        <span class="text-[#4F46E5]">编辑部门</span>
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'addSub', dept: data}">
                        <el-icon class="mr-1 text-green-500"><Plus /></el-icon>
                        <span class="text-green-600">添加子部门</span>
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'delete', dept: data}" divided>
                        <el-icon class="mr-1 text-red-500"><Delete /></el-icon>
                        <span class="text-red-600">删除部门</span>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 右侧成员列表 -->
      <div class="lg:col-span-3">
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
          <!-- 搜索和筛选区域 -->
          <div class="p-4 border-b border-gray-100">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <el-input
                v-model="memberSearchQuery"
                placeholder="搜索成员姓名、手机号"
                clearable
                @input="handleMemberSearch"
                size="default"
              >
                <template #prefix>
                  <el-icon class="text-[#4F46E5]"><Search /></el-icon>
                </template>
              </el-input>
              
              <el-select 
                v-model="memberStatusFilter" 
                placeholder="账号状态" 
                clearable 
                class="w-full"
                size="default"
                @change="handleMemberSearch"
              >
                <el-option label="全部" value="" />
                <el-option label="正常" value="normal" />
                <el-option label="停用" value="disabled" />
              </el-select>
              
              <div class="flex justify-end">
                <el-button 
                  @click="handleResetMemberFilter"
                  size="default"
                >
                  重置筛选
                </el-button>
              </div>
            </div>
          </div>

          <!-- 批量操作栏 -->
          <div v-if="selectedMembers.length > 0" class="bg-[#4F46E5] bg-opacity-10 p-4 border-b">
            <div class="flex items-center justify-between">
              <span class="text-[#4F46E5] font-medium">
                已选择 <span class="completed-badge">{{ selectedMembers.length }}</span> 个成员
              </span>
              <div class="flex space-x-2">
                <el-button 
                  size="small"
                  @click="handleBatchDisable"
                  class="bg-orange-500 hover:bg-orange-600 border-orange-500 text-white"
                >
                  批量停用
                </el-button>
                <el-button 
                  size="small"
                  @click="handleBatchChangeDepartment"
                  class="bg-[#4F46E5] hover:bg-[#4338CA] border-[#4F46E5] text-white"
                >
                  变更部门
                </el-button>
              </div>
            </div>
          </div>

          <!-- 成员列表表格 -->
          <el-table
            :data="paginatedMembers"
            style="width: 100%"
            :header-cell-style="{
              background: 'linear-gradient(135deg, #4F46E5 0%, #6366F1 100%)',
              color: '#ffffff',
              fontWeight: 600,
              fontSize: '14px',
              height: '64px',
              borderBottom: 'none'
            }"
            :cell-style="{
              fontSize: '14px',
              color: '#374151',
              height: '64px',
              borderBottom: '1px solid #F3F4F6'
            }"
            @selection-change="handleMemberSelectionChange"
            v-loading="loading"
          >
            <el-table-column type="selection" width="55" />
            
            <el-table-column label="姓名" min-width="250" fixed="left">
              <template #default="{ row }">
                <div class="flex items-center">
                  <div class="w-10 h-10 rounded-full bg-primary bg-opacity-10 flex items-center justify-center text-primary font-medium mr-3 text-sm">
                    {{ getMemberInitials(row.name) }}
                  </div>
                  <div>
                    <div class="font-semibold text-gray-900">{{ row.name }}</div>
                    <div class="text-sm text-gray-500">{{ row.position || '无职位' }}</div>
                  </div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="状态" width="100">
              <template #default="{ row }">
                <el-tag 
                  :type="getMemberStatusTagType(row.status)" 
                  size="small"
                  effect="plain"
                >
                  {{ getMemberStatusLabel(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column prop="phone" label="手机号" width="180" />
            
            <el-table-column label="所属部门" min-width="180">
              <template #default="{ row }">
                <div class="flex flex-wrap gap-1">
                  <el-tag 
                    v-for="dept in row.departments" 
                    :key="dept.id"
                    size="small"
                    type="info"
                    effect="plain"
                  >
                    {{ dept.name }}
                  </el-tag>
                  <span v-if="!row.departments || row.departments.length === 0" class="text-gray-400">
                    未分配
                  </span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <div class="flex items-center space-x-2" @click.stop>
                  <el-button 
                    type="primary" 
                    size="small" 
                    @click="handleViewMemberDetail(row)"
                    plain
                  >
                    详情
                  </el-button>
                  <el-dropdown trigger="click">
                    <el-button 
                      type="text" 
                      size="small" 
                      class="text-[#4F46E5] hover:text-[#4338CA] hover:bg-[#4F46E5]/10"
                    >
                      <el-icon><MoreFilled /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click="handleEditMember(row)">
                          <el-icon class="mr-1 text-[#4F46E5]"><Edit /></el-icon>
                          <span class="text-[#4F46E5]">编辑信息</span>
                        </el-dropdown-item>
                        <el-dropdown-item @click="handleToggleMemberStatus(row)">
                          <el-icon class="mr-1 text-orange-500">
                            <component :is="row.status === 'normal' ? Lock : Unlock" />
                          </el-icon>
                          <span class="text-orange-600">
                            {{ row.status === 'normal' ? '停用账号' : '启用账号' }}
                          </span>
                        </el-dropdown-item>
                        <el-dropdown-item @click="handleDeleteMember(row)" divided>
                          <el-icon class="mr-1 text-red-500"><Delete /></el-icon>
                          <span class="text-red-600">删除成员</span>
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="flex justify-between items-center py-4 px-6">
            <div class="text-sm text-gray-500">
              共 {{ total }} 条记录，当前显示 {{ currentDepartment ? `${currentDepartment.name}${currentDepartment.children && currentDepartment.children.length > 0 ? '（含子部门）' : ''}` : '全部' }} 部门
            </div>
            <el-pagination
              v-model:current-page="currentPage"
              :page-size="pageSize"
              :total="total"
              layout="sizes, prev, pager, next, jumper"
              :page-sizes="[10, 20, 50, 100]"
              @current-change="handleCurrentChange"
              @size-change="handleSizeChange"
              background
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredMembers.length === 0 && !loading" class="text-center py-12">
      <el-empty 
        :description="currentDepartment ? `${currentDepartment.name}${currentDepartment.children && currentDepartment.children.length > 0 ? '（含子部门）' : ''} 暂无成员` : '暂无成员数据'"
      >
        <el-button type="primary" @click="handleAddMember">添加第一个成员</el-button>
      </el-empty>
    </div>

    <!-- 部门表单对话框 -->
    <el-dialog
      v-model="departmentDialogVisible"
      :title="isDeptEditMode ? '编辑部门' : '新增部门'"
      width="600px"
      destroy-on-close
    >
      <el-form
        ref="departmentFormRef"
        :model="departmentFormData"
        :rules="departmentFormRules"
        label-width="100px"
        size="default"
      >
        <el-form-item label="部门名称" prop="name" required>
          <el-input v-model="departmentFormData.name" placeholder="请输入部门名称" clearable />
        </el-form-item>
        <el-form-item label="部门编码" prop="code" required>
          <el-input v-model="departmentFormData.code" placeholder="请输入部门编码" clearable />
        </el-form-item>
        <el-form-item label="上级部门" prop="parentId">
          <el-tree-select
            v-model="departmentFormData.parentId"
            :data="departmentTree"
            :props="{ children: 'children', label: 'name', value: 'id' }"
            placeholder="请选择上级部门"
            clearable
            class="w-full"
          />
        </el-form-item>
        <el-form-item label="部门负责人" prop="manager">
          <el-input v-model="departmentFormData.manager" placeholder="请输入负责人姓名" clearable />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="departmentFormData.sort" :min="1" :max="999" class="w-full" />
        </el-form-item>
        <el-form-item label="部门描述" prop="description">
          <el-input
            v-model="departmentFormData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入部门描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="departmentDialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleDepartmentFormSubmit"
            :loading="departmentFormSubmitting"
          >
            {{ isDeptEditMode ? '更新部门' : '创建部门' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 成员表单对话框 -->
    <el-dialog
      v-model="memberDialogVisible"
      :title="isMemberEditMode ? '编辑成员' : '添加成员'"
      width="700px"
      destroy-on-close
    >
      <el-form
        ref="memberFormRef"
        :model="memberFormData"
        :rules="memberFormRules"
        label-width="100px"
        size="default"
      >
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <el-form-item label="姓名" prop="name" required>
            <el-input v-model="memberFormData.name" placeholder="请输入成员姓名" clearable />
          </el-form-item>
          <el-form-item label="手机号码" prop="phone" required>
            <el-input v-model="memberFormData.phone" placeholder="请输入手机号码" clearable />
          </el-form-item>
          <el-form-item label="邮箱地址" prop="email">
            <el-input v-model="memberFormData.email" placeholder="请输入邮箱地址" clearable />
          </el-form-item>
          <el-form-item label="岗位/职位" prop="position">
            <el-input v-model="memberFormData.position" placeholder="请输入岗位职位" clearable />
          </el-form-item>
        </div>
        <el-form-item label="所属部门" prop="departments">
          <el-tree-select
            v-model="memberFormData.departments"
            :data="departmentTree"
            :props="{ children: 'children', label: 'name', value: 'id' }"
            multiple
            placeholder="请选择所属部门（可多选）"
            clearable
            class="w-full"
          />
        </el-form-item>
        <el-form-item label="账号状态" prop="status">
          <el-radio-group v-model="memberFormData.status">
            <el-radio value="normal">正常</el-radio>
            <el-radio value="disabled">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="memberDialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleMemberFormSubmit"
            :loading="memberFormSubmitting"
          >
            {{ isMemberEditMode ? '更新成员' : '添加成员' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量变更部门对话框 -->
    <el-dialog
      v-model="batchChangeDeptDialogVisible"
      title="批量变更部门"
      width="500px"
      destroy-on-close
    >
      <div class="mb-4">
        <p class="text-gray-600 mb-4">
          将为选中的 <span class="completed-badge">{{ selectedMembers.length }}</span> 个成员变更部门：
        </p>
        <div class="bg-gray-50 p-3 rounded-lg mb-4 max-h-32 overflow-y-auto">
          <div v-for="member in selectedMembers" :key="member.id" class="text-sm text-gray-700 mb-1">
            {{ member.name }}
          </div>
        </div>
      </div>
      
      <el-form label-width="100px">
        <el-form-item label="目标部门" required>
          <el-tree-select
            v-model="batchChangeDeptForm.targetDepartment"
            :data="departmentTree"
            :props="{ children: 'children', label: 'name', value: 'id' }"
            placeholder="请选择目标部门"
            clearable
            class="w-full"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchChangeDeptDialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleConfirmBatchChangeDept"
            :disabled="!batchChangeDeptForm.targetDepartment"
          >
            确认变更
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Search, OfficeBuilding, Edit, Delete, MoreFilled, Lock, Unlock, Switch
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)

// 公司信息
const companyName = ref('英派瑞克') // 以后从数据库获取

// 部门相关
const deptSearchQuery = ref('')
const departmentTree = ref([])
const currentDepartment = ref(null)

// 成员相关
const memberSearchQuery = ref('')
const memberStatusFilter = ref('')
const members = ref([])
const selectedMembers = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 表单对话框相关
const departmentDialogVisible = ref(false)
const memberDialogVisible = ref(false)
const batchChangeDeptDialogVisible = ref(false)

const departmentFormData = ref({
  name: '',
  code: '',
  parentId: null,
  manager: '',
  description: '',
  sort: 1
})

const memberFormData = ref({
  name: '',
  phone: '',
  email: '',
  position: '',
  departments: [],
  status: 'normal'
})

const batchChangeDeptForm = ref({
  targetDepartment: null
})

const isDeptEditMode = ref(false)
const isMemberEditMode = ref(false)
const departmentFormSubmitting = ref(false)
const memberFormSubmitting = ref(false)
const departmentFormRef = ref()
const memberFormRef = ref()

// 工具函数：获取部门及其所有子部门的ID
const getAllDepartmentIds = (dept, allDepts = []) => {
  const ids = [dept.id]
  
  // 递归查找所有子部门
  const findChildrenIds = (deptList) => {
    deptList.forEach(d => {
      if (d.children && d.children.length > 0) {
        d.children.forEach(child => {
          ids.push(child.id)
          if (child.children && child.children.length > 0) {
            findChildrenIds([child])
          }
        })
      }
    })
  }
  
  // 从当前部门开始查找子部门
  if (dept.children && dept.children.length > 0) {
    findChildrenIds([dept])
  }
  
  return ids
}

// 计算属性
const filteredMembers = computed(() => {
  let filtered = members.value

  // 按部门筛选
  if (currentDepartment.value) {
    // 获取当前部门及其所有子部门的ID
    const departmentIds = getAllDepartmentIds(currentDepartment.value)
    
    filtered = filtered.filter(member => 
      member.departments && member.departments.some(dept => departmentIds.includes(dept.id))
    )
  }

  // 按关键词搜索
  if (memberSearchQuery.value) {
    const query = memberSearchQuery.value.toLowerCase()
    filtered = filtered.filter(member => 
      member.name.toLowerCase().includes(query) ||
      member.phone.includes(query) ||
      (member.email && member.email.toLowerCase().includes(query))
    )
  }

  // 按状态筛选
  if (memberStatusFilter.value) {
    filtered = filtered.filter(member => member.status === memberStatusFilter.value)
  }

  // 排除已删除的成员
  filtered = filtered.filter(member => member.status !== 'deleted')

  total.value = filtered.length
  return filtered
})

const paginatedMembers = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredMembers.value.slice(start, end)
})

// 表单验证规则
const departmentFormRules = {
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { min: 2, max: 50, message: '部门名称长度应在2-50个字符之间', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入部门编码', trigger: 'blur' },
    { pattern: /^[A-Za-z0-9_-]+$/, message: '部门编码只能包含字母、数字、下划线和横线', trigger: 'blur' }
  ]
}

const memberFormRules = {
  name: [
    { required: true, message: '请输入成员姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 部门树相关方法
const filterDepartmentNode = (value, data) => {
  if (!value) return true
  return data.name.includes(value)
}

const handleDeptSearch = () => {
  // TODO: 实现部门搜索逻辑
}

const handleDeptNodeClick = (dept) => {
  currentDepartment.value = dept
  currentPage.value = 1
}

const handleDeptAction = async ({ action, dept }) => {
  if (action === 'edit') {
    openDepartmentDialog(dept)
  } else if (action === 'addSub') {
    openDepartmentDialog(null, dept)
  } else if (action === 'delete') {
    try {
      await ElMessageBox.confirm(
        `确定要删除部门 ${dept.name} 吗？删除后该部门下的成员将转移到上级部门。`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      // TODO: 调用删除API
      ElMessage.success('删除成功')
      loadDepartments()
    } catch (error) {
      // 用户取消删除
    }
  }
}

// 成员相关方法
const getMemberInitials = (name) => {
  if (!name) return 'N'
  const names = name.split(' ')
  if (names.length === 1) {
    return names[0].charAt(0).toUpperCase()
  }
  return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase()
}

const getMemberStatusTagType = (status) => {
  const statusMap = {
    normal: 'success',
    disabled: 'warning'
  }
  return statusMap[status] || 'info'
}

const getMemberStatusLabel = (status) => {
  const statusMap = {
    normal: '正常',
    disabled: '停用'
  }
  return statusMap[status] || '未知'
}

const handleMemberSearch = () => {
  currentPage.value = 1
}

const handleResetMemberFilter = () => {
  memberSearchQuery.value = ''
  memberStatusFilter.value = ''
  currentDepartment.value = null
}

const handleMemberSelectionChange = (selection) => {
  selectedMembers.value = selection
}

// 操作方法
const handleAddDepartment = () => {
  openDepartmentDialog()
}

const handleAddMember = () => {
  openMemberDialog()
}

const handleViewMemberDetail = (member) => {
  openMemberDialog(member, true)
}

const handleEditMember = (member) => {
  openMemberDialog(member)
}

const handleToggleMemberStatus = async (member) => {
  const action = member.status === 'normal' ? '停用' : '启用'
  try {
    await ElMessageBox.confirm(
      `确定要${action}成员 ${member.name} 的账号吗？`,
      `${action}确认`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用API
    ElMessage.success(`${action}成功`)
    loadMembers()
  } catch (error) {
    // 用户取消
  }
}

const handleDeleteMember = async (member) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除成员 ${member.name} 吗？此操作将永久移除成员，请确认！`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用删除API
    ElMessage.success('删除成功')
    loadMembers()
  } catch (error) {
    // 用户取消
  }
}

const handleBatchDisable = async () => {
  if (selectedMembers.value.length === 0) {
    ElMessage.warning('请先选择要停用的成员')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要停用选中的 ${selectedMembers.value.length} 个成员吗？`,
      '批量停用确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用批量停用API
    ElMessage.success('批量停用成功')
    selectedMembers.value = []
    loadMembers()
  } catch (error) {
    // 用户取消
  }
}

const handleBatchChangeDepartment = () => {
  if (selectedMembers.value.length === 0) {
    ElMessage.warning('请先选择要变更部门的成员')
    return
  }
  
  batchChangeDeptForm.value.targetDepartment = null
  batchChangeDeptDialogVisible.value = true
}

const handleConfirmBatchChangeDept = async () => {
  try {
    // TODO: 调用批量变更部门API
    ElMessage.success('部门变更成功')
    batchChangeDeptDialogVisible.value = false
    selectedMembers.value = []
    loadMembers()
  } catch (error) {
    ElMessage.error('变更失败，请重试')
  }
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

// 对话框处理函数
const openDepartmentDialog = (department = null, parentDepartment = null) => {
  isDeptEditMode.value = department !== null
  
  if (department) {
    Object.keys(departmentFormData.value).forEach(key => {
      if (department.hasOwnProperty(key)) {
        departmentFormData.value[key] = department[key]
      }
    })
  } else {
    resetDepartmentForm()
    if (parentDepartment) {
      departmentFormData.value.parentId = parentDepartment.id
    }
  }
  
  departmentDialogVisible.value = true
}

const openMemberDialog = (member = null, viewOnly = false) => {
  isMemberEditMode.value = member !== null && !viewOnly
  
  if (member) {
    Object.keys(memberFormData.value).forEach(key => {
      if (member.hasOwnProperty(key)) {
        memberFormData.value[key] = member[key]
      }
    })
    // 处理部门数据
    if (member.departments) {
      memberFormData.value.departments = member.departments.map(dept => dept.id)
    }
  } else {
    resetMemberForm()
  }
  
  memberDialogVisible.value = true
}

const resetDepartmentForm = () => {
  departmentFormData.value = {
    name: '',
    code: '',
    parentId: null,
    manager: '',
    description: '',
    sort: 1
  }
}

const resetMemberForm = () => {
  memberFormData.value = {
    name: '',
    phone: '',
    email: '',
    position: '',
    departments: [],
    status: 'normal'
  }
}

const handleDepartmentFormSubmit = async () => {
  try {
    await departmentFormRef.value.validate()
    departmentFormSubmitting.value = true

    // TODO: 调用API保存数据
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success(isDeptEditMode.value ? '部门更新成功' : '部门创建成功')
    departmentDialogVisible.value = false
    loadDepartments()
    loadMembers()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(isDeptEditMode.value ? '更新失败，请重试' : '创建失败，请重试')
    }
  } finally {
    departmentFormSubmitting.value = false
  }
}

const handleMemberFormSubmit = async () => {
  try {
    await memberFormRef.value.validate()
    memberFormSubmitting.value = true

    // TODO: 调用API保存数据
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success(isMemberEditMode.value ? '成员更新成功' : '成员添加成功')
    memberDialogVisible.value = false
    loadMembers()
    loadDepartments() // 刷新部门成员数量
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(isMemberEditMode.value ? '更新失败，请重试' : '添加失败，请重试')
    }
  } finally {
    memberFormSubmitting.value = false
  }
}

// 数据加载
// 加载公司信息
const loadCompanyInfo = async () => {
  try {
    // TODO: 从API获取公司信息
    // const response = await api.getCompanyInfo()
    // companyName.value = response.data.name
  } catch (error) {
    ElMessage.error('加载公司信息失败')
  }
}

const loadDepartments = async () => {
  try {
    // TODO: 实际API调用
    departmentTree.value = [
      {
        id: 1,
        name: '顾问部',
        code: 'CONSULTANT',
        manager: 'Dex',
        memberCount: 1,
        children: [
          {
            id: 2,
            name: '中期规划',
            code: 'MID_PLANNING',
            manager: 'Dex',
            memberCount: 1,
            children: []
          }
        ]
      },
      {
        id: 3,
        name: '交付部',
        code: 'DELIVERY',
        manager: 'Tessa',
        memberCount: 8,
        children: [
          {
            id: 4,
            name: '文书部',
            code: 'WRITING',
            manager: 'Tessa',
            memberCount: 5,
            children: []
          },
          {
            id: 5,
            name: '网申递交部',
            code: 'APPLICATION',
            manager: 'Zoe',
            memberCount: 3,
            children: []
          }
        ]
      },
      {
        id: 6,
        name: '市场部',
        code: 'MARKETING',
        manager: '',
        memberCount: 0,
        children: []
      }
    ]
  } catch (error) {
    ElMessage.error('加载部门数据失败')
  }
}

const loadMembers = async () => {
  try {
    loading.value = true
    // TODO: 实际API调用
    members.value = [
      {
        id: 1,
        name: 'Dex',
        phone: '13812345678',
        email: '<EMAIL>',
        position: '中期规划顾问',
        status: 'normal',
        departments: [{ id: 2, name: '中期规划' }]
      },
      {
        id: 2,
        name: 'Tessa',
        phone: '13887654321',
        email: '<EMAIL>',
        position: '文书部经理',
        status: 'normal',
        departments: [{ id: 4, name: '文书部' }]
      },
      {
        id: 3,
        name: 'Klaudia',
        phone: '13698765432',
        email: '<EMAIL>',
        position: '文书顾问',
        status: 'normal',
        departments: [{ id: 4, name: '文书部' }]
      },
      {
        id: 4,
        name: 'Zoe',
        phone: '13712345678',
        email: '<EMAIL>',
        position: '网申递交部经理',
        status: 'normal',
        departments: [{ id: 5, name: '网申递交部' }]
      },
      {
        id: 5,
        name: 'Serana',
        phone: '13798765432',
        email: '<EMAIL>',
        position: '网申顾问',
        status: 'normal',
        departments: [{ id: 5, name: '网申递交部' }]
      },
      {
        id: 6,
        name: 'Neroli',
        phone: '13756789012',
        email: '<EMAIL>',
        position: '文书顾问',
        status: 'normal',
        departments: [{ id: 4, name: '文书部' }]
      },
      {
        id: 7,
        name: 'Kylian',
        phone: '13634567890',
        email: '<EMAIL>',
        position: '文书顾问',
        status: 'normal',
        departments: [{ id: 4, name: '文书部' }]
      },
      {
        id: 8,
        name: 'Kiki',
        phone: '13612345678',
        email: '<EMAIL>',
        position: '网申顾问',
        status: 'normal',
        departments: [{ id: 5, name: '网申递交部' }]
      },
      {
        id: 9,
        name: 'Gary',
        phone: '13587654321',
        email: '<EMAIL>',
        position: '文书顾问',
        status: 'normal',
        departments: [{ id: 4, name: '文书部' }]
      }
    ]
  } catch (error) {
    ElMessage.error('加载成员数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadCompanyInfo()
  loadDepartments()
  loadMembers()
})
</script>

<style>
@import '../styles/crm-theme.css';
</style>

<style scoped>
.dept-tree :deep(.el-tree-node__content) {
  height: 36px;
  padding: 0 8px;
}

.dept-tree :deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

.dept-tree :deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #eef2ff;
  color: #4F46E5;
}

/* 浅绿色主题 - 用于已完成状态 */
.completed-count {
  color: #065F46;
  background: rgba(134, 239, 172, 0.15);
  padding: 2px 6px;
  border-radius: 6px;
  font-weight: 500;
  border: 1px solid rgba(134, 239, 172, 0.3);
}

.completed-status {
  color: #064E3B;
  background: linear-gradient(135deg, #86EFAC 0%, #4ADE80 100%);
  padding: 4px 8px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 12px;
  border: 1px solid #86EFAC;
}

.completed-number {
  color: #065F46;
  font-weight: 600;
  background: rgba(134, 239, 172, 0.1);
  padding: 2px 8px;
  border-radius: 6px;
  border: 1px solid rgba(134, 239, 172, 0.4);
}

.completed-badge {
  background: linear-gradient(135deg, #86EFAC 0%, #4ADE80 100%);
  color: #064E3B;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid #86EFAC;
  box-shadow: 0 2px 4px rgba(134, 239, 172, 0.2);
}
</style> 