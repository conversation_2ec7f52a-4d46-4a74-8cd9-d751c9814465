<template>
  <div class="crm-container p-6">
    <!-- 页面标题和操作区域 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">签约客户</h1>
        <div class="flex items-center mt-2 text-sm text-gray-500">
          <span class="mr-4">客户列表</span>
          <span class="text-gray-300">/</span>
          <span class="ml-4 text-[#4F46E5] font-medium">签约客户</span>
        </div>
      </div>
      <div class="flex items-center space-x-3">
        <el-button 
          @click="handleExportData"
          size="large"
          class="border-[#4F46E5] text-[#4F46E5] hover:bg-[#4F46E5] hover:text-white transition-all duration-200"
        >
          <el-icon class="mr-2"><Download /></el-icon>
          导出数据
        </el-button>
                  <el-button 
            type="primary" 
            @click="handleAddStudent"
            size="large"
            class="bg-[#4F46E5] hover:bg-[#4338CA] border-[#4F46E5] hover:border-[#4338CA] shadow-lg hover:shadow-xl transition-all duration-200"
          >
            <el-icon class="mr-2"><Plus /></el-icon>
            添加客户
          </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-200">
        <div class="flex items-center justify-between">
          <div>
                            <p class="text-sm text-gray-500 font-medium">总签约客户</p>
            <p class="text-3xl font-bold text-gray-900 mt-2">{{ statistics.total }}</p>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-[#4F46E5] to-[#6366F1] rounded-xl flex items-center justify-center shadow-lg">
            <el-icon class="text-white text-xl"><User /></el-icon>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 font-medium">服务中</p>
            <p class="text-3xl font-bold text-yellow-600 mt-2">{{ statistics.active }}</p>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center shadow-lg">
            <el-icon class="text-white text-xl"><Checked /></el-icon>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 font-medium">已完成</p>
            <p class="text-3xl font-bold text-green-600 mt-2">{{ statistics.completed }}</p>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
            <el-icon class="text-white text-xl"><Trophy /></el-icon>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 font-medium">本月新增</p>
            <p class="text-3xl font-bold text-orange-600 mt-2">{{ statistics.thisMonth }}</p>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
            <el-icon class="text-white text-xl"><TrendCharts /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <el-input
          v-model="searchQuery"
                        placeholder="搜索客户姓名、大学、专业"
          clearable
          @input="handleSearch"
          size="large"
        >
          <template #prefix>
            <el-icon class="text-[#4F46E5]"><Search /></el-icon>
          </template>
        </el-input>
        <el-select 
          v-model="serviceStatusFilter" 
          placeholder="服务状态" 
          clearable 
          class="w-full"
          size="large"
        >
          <el-option label="全部" value="" />
          <el-option label="服务中" value="active" />
          <el-option label="已完成" value="completed" />
          <el-option label="暂停" value="paused" />
        </el-select>
        <el-select 
          v-model="contractTypeFilter" 
          placeholder="合同类型" 
          clearable 
          class="w-full"
          size="large"
        >
          <el-option label="全部" value="" />
          <el-option label="标准服务包" value="standard" />
          <el-option label="高端服务包" value="premium" />
          <el-option label="定制服务包" value="custom" />
        </el-select>
        <el-select 
          v-model="consultantFilter" 
          placeholder="负责顾问" 
          clearable 
          class="w-full"
          size="large"
        >
          <el-option label="全部" value="" />
          <el-option label="李老师" value="李老师" />
          <el-option label="王老师" value="王老师" />
          <el-option label="张老师" value="张老师" />
        </el-select>
      </div>
    </div>

    <!-- 客户列表 -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
      <el-table
        :data="filteredStudents"
        style="width: 100%"
        :header-cell-style="{
          background: 'linear-gradient(135deg, #4F46E5 0%, #6366F1 100%)',
          color: '#ffffff',
          fontWeight: 600,
          fontSize: '14px',
          height: '64px',
          borderBottom: 'none'
        }"
        :cell-style="{
          fontSize: '14px',
          color: '#374151',
          height: '64px',
          borderBottom: '1px solid #F3F4F6'
        }"
        @row-click="handleStudentClick"
        class="student-table"
        v-loading="loading"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="客户姓名" min-width="100">
          <template #default="{ row }">
            <div class="flex items-center">
              <div class="w-10 h-10 rounded-full bg-[#4F46E5] bg-opacity-10 flex items-center justify-center text-[#4F46E5] mr-3 text-sm font-bold">
                {{ getStudentInitials(row.name) }}
              </div>
              <span class="font-semibold text-gray-900">{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="university" label="在读院校" min-width="120" />
        <el-table-column prop="schoolType" label="学校类型" width="100" />
        <el-table-column prop="major" label="在读专业" min-width="120" />
        <el-table-column prop="gpa" label="均分绩点" width="90" />
        <el-table-column prop="grade" label="在读年级" width="90">
          <template #default="{ row }">
            {{ getGradeLabel(row.grade) }}
          </template>
        </el-table-column>
        <el-table-column prop="targetCountry" label="申请国家(地区)" width="110">
          <template #default="{ row }">
            {{ getCountryLabel(row.targetCountry) }}
          </template>
        </el-table-column>
        <el-table-column prop="applicationYear" label="申请入读年份" width="110" />
        
        <el-table-column label="服务状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getServiceStatusTagType(row.serviceStatus)" size="small">
              {{ getServiceStatusLabel(row.serviceStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="合同类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getContractTypeTagType(row.contractType)" size="small" effect="plain">
              {{ getContractTypeLabel(row.contractType) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="consultant" label="规划顾问" min-width="80" />
        
        <el-table-column label="签约时间" width="100">
          <template #default="{ row }">
            {{ formatDate(row.signedAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="服务进度" width="100">
          <template #default="{ row }">
            <div class="flex items-center">
              <el-progress 
                :percentage="row.progress" 
                :stroke-width="6"
                :show-text="false"
                class="flex-1 mr-2"
              />
              <span class="text-sm text-gray-600">{{ row.progress }}%</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="notes" label="备注" min-width="150" />
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <div class="flex items-center space-x-1" @click.stop>
              <el-button 
                type="primary" 
                size="small" 
                @click="handleManageService(row)"
                plain
                class="px-2"
              >
                管理
              </el-button>
              <el-dropdown trigger="click" placement="bottom-end">
                <el-button 
                  type="text" 
                  size="small" 
                  class="text-[#4F46E5] hover:text-[#4338CA] hover:bg-[#4F46E5]/10 px-2"
                >
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleEditStudent(row)">
                      <el-icon class="mr-2 text-[#4F46E5]"><Edit /></el-icon>
                      <span class="text-[#4F46E5]">编辑信息</span>
                    </el-dropdown-item>
                    <el-dropdown-item @click="handlePauseService(row)" v-if="row.serviceStatus === 'active'">
                      <el-icon class="mr-2 text-orange-500"><VideoPause /></el-icon>
                      <span class="text-orange-600">暂停服务</span>
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleResumeService(row)" v-if="row.serviceStatus === 'paused'">
                      <el-icon class="mr-2 text-yellow-500"><VideoPlay /></el-icon>
                      <span class="text-yellow-600">恢复服务</span>
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleCompleteService(row)" v-if="row.serviceStatus === 'active'" divided>
                      <el-icon class="mr-2 text-yellow-500"><Check /></el-icon>
                      <span class="text-yellow-600">完成服务</span>
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleDeleteStudent(row)" divided>
                      <el-icon class="mr-2 text-red-500"><Delete /></el-icon>
                      <span class="text-red-600">删除</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex justify-between items-center py-4 px-6">
        <div class="text-sm text-gray-500">
          共 {{ total }} 条记录
        </div>
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="total"
          layout="sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          background
        />
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredStudents.length === 0 && !loading" class="text-center py-12">
      <el-empty description="暂无客户数据">
        <el-button type="primary" @click="handleAddStudent">添加第一个客户</el-button>
      </el-empty>
    </div>

    <!-- 服务管理对话框 -->
    <el-dialog
      v-model="serviceDialogVisible"
      title="服务管理"
      width="800px"
      destroy-on-close
    >
      <div v-if="selectedStudent">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="服务概览" name="overview">
            <div class="space-y-4">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="text-sm font-medium text-gray-700">当前进度</label>
                  <el-progress :percentage="selectedStudent.progress" class="mt-2" />
                </div>
                <div>
                  <label class="text-sm font-medium text-gray-700">服务状态</label>
                  <div class="mt-2">
                    <el-tag :type="getServiceStatusTagType(selectedStudent.serviceStatus)">
                      {{ getServiceStatusLabel(selectedStudent.serviceStatus) }}
                    </el-tag>
                  </div>
                </div>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-700">服务阶段</label>
                <div class="mt-2">
                  <el-steps :active="selectedStudent.currentStage" finish-status="success">
                    <el-step title="资料收集" />
                    <el-step title="申请规划" />
                    <el-step title="文书撰写" />
                    <el-step title="申请提交" />
                    <el-step title="结果跟踪" />
                  </el-steps>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="进度更新" name="progress">
            <el-form :model="progressForm" label-width="100px">
              <el-form-item label="当前阶段">
                <el-select v-model="progressForm.stage" class="w-full">
                  <el-option label="资料收集" :value="0" />
                  <el-option label="申请规划" :value="1" />
                  <el-option label="文书撰写" :value="2" />
                  <el-option label="申请提交" :value="3" />
                  <el-option label="结果跟踪" :value="4" />
                </el-select>
              </el-form-item>
              <el-form-item label="完成度">
                <el-slider v-model="progressForm.percentage" :max="100" />
              </el-form-item>
              <el-form-item label="备注">
                <el-input
                  v-model="progressForm.notes"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入进度更新说明"
                />
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="serviceDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleUpdateProgress" v-if="activeTab === 'progress'">
            更新进度
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 客户详情对话框 -->
    <el-dialog
      v-model="studentDetailDialogVisible"
              title="客户详情"
      width="950px"
      destroy-on-close
      top="5vh"
      class="crm-client-detail-dialog"
    >
      <div v-if="selectedStudent" class="p-1 bg-gray-50/50 rounded-lg">
        <!-- 头部信息卡片 -->
        <div class="bg-white rounded-xl shadow p-6 mb-6 flex items-center space-x-6 relative">
          <div class="w-20 h-20 rounded-full bg-[#4F46E5] bg-opacity-10 flex-shrink-0 flex items-center justify-center text-[#4F46E5] text-3xl font-bold">
            {{ getStudentInitials(selectedStudent.name) }}
          </div>
          <div class="flex-1">
            <div class="flex items-center justify-between">
              <h2 class="text-2xl font-bold text-gray-900">{{ selectedStudent.name }}</h2>
              <div class="flex items-center space-x-2">
                <el-tag :type="getServiceStatusTagType(selectedStudent.serviceStatus)" size="default" effect="light">
                  {{ getServiceStatusLabel(selectedStudent.serviceStatus) }}
                </el-tag>
                <el-tag :type="getContractTypeTagType(selectedStudent.contractType)" size="default" effect="plain">
                  {{ getContractTypeLabel(selectedStudent.contractType) }}
                </el-tag>
              </div>
            </div>
            <p class="text-gray-600 mt-1">
              {{ selectedStudent.university }} - {{ selectedStudent.major }}
            </p>
            <div class="mt-4 flex items-center space-x-8 text-sm text-gray-500">
              <div class="flex items-center">
                <el-icon class="mr-1.5"><Phone /></el-icon>
                <span>{{ selectedStudent.phone || '未提供' }}</span>
              </div>
              <div class="flex items-center">
                <el-icon class="mr-1.5"><ElMessageIcon /></el-icon>
                <span>{{ selectedStudent.email || '未提供' }}</span>
              </div>
              <div class="flex items-center">
                <el-icon class="mr-1.5"><UserFilled /></el-icon>
                <span>负责顾问: <span class="font-medium text-gray-700 ml-1">{{ selectedStudent.consultant || '未分配' }}</span></span>
              </div>
            </div>
          </div>
        </div>

        <el-tabs v-model="studentDetailActiveTab" class="detail-tabs">
          <!-- 基本信息 -->
          <el-tab-pane label="详细资料" name="basic">
            <div class="mt-4 grid grid-cols-1 lg:grid-cols-2 gap-6">
              <!-- 个人信息 -->
              <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <el-icon class="mr-2 text-primary"><User /></el-icon>
                  个人信息
                </h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                  <div class="flex flex-col space-y-1">
                    <span class="text-gray-500">姓名</span>
                    <span class="text-gray-800 font-medium">{{ selectedStudent.name }}</span>
                  </div>
                  <div class="flex flex-col space-y-1">
                    <span class="text-gray-500">性别</span>
                    <span class="text-gray-800 font-medium">{{ selectedStudent.gender === 'male' ? '男' : '女' }}</span>
                  </div>
                  <div class="flex flex-col space-y-1 col-span-2">
                    <span class="text-gray-500">出生日期</span>
                    <span class="text-gray-800 font-medium">{{ formatDate(selectedStudent.birthDate) || '未填写' }}</span>
                  </div>
                </div>
              </div>

              <!-- 教育背景 -->
              <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <el-icon class="mr-2 text-primary"><School /></el-icon>
                  教育背景
                </h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                  <div class="flex flex-col space-y-1 col-span-2">
                    <span class="text-gray-500">院校</span>
                    <span class="text-gray-800 font-medium">{{ selectedStudent.university || '未填写' }}</span>
                  </div>
                  <div class="flex flex-col space-y-1">
                    <span class="text-gray-500">专业</span>
                    <span class="text-gray-800 font-medium">{{ selectedStudent.major || '未填写' }}</span>
                  </div>
                  <div class="flex flex-col space-y-1">
                    <span class="text-gray-500">GPA</span>
                    <span class="text-gray-800 font-medium">{{ selectedStudent.gpa || '未填写' }}</span>
                  </div>
                </div>
              </div>

              <!-- 申请信息 -->
              <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <el-icon class="mr-2 text-primary"><Flag /></el-icon>
                  申请信息
                </h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                  <div class="flex flex-col space-y-1">
                    <span class="text-gray-500">目标国家</span>
                    <span class="text-gray-800 font-medium">{{ getCountryLabel(selectedStudent.targetCountry) }}</span>
                  </div>
                  <div class="flex flex-col space-y-1">
                    <span class="text-gray-500">目标学位</span>
                    <span class="text-gray-800 font-medium">{{ getDegreeLabel(selectedStudent.targetDegree) }}</span>
                  </div>
                  <div class="flex flex-col space-y-1 col-span-2">
                    <span class="text-gray-500">目标专业</span>
                    <span class="text-gray-800 font-medium">{{ selectedStudent.targetMajor || '未填写' }}</span>
                  </div>
                   <div class="flex flex-col space-y-1">
                    <span class="text-gray-500">语言成绩</span>
                    <span class="text-gray-800 font-medium">{{ selectedStudent.languageScores || '未填写' }}</span>
                  </div>
                  <div class="flex flex-col space-y-1">
                    <span class="text-gray-500">标化成绩</span>
                    <span class="text-gray-800 font-medium">{{ selectedStudent.standardizedTests || '未填写' }}</span>
                  </div>
                </div>
              </div>

              <!-- 服务信息 -->
              <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <el-icon class="mr-2 text-primary"><Setting /></el-icon>
                  服务信息
                </h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                  <div class="flex flex-col space-y-1">
                    <span class="text-gray-500">服务状态</span>
                    <el-tag :type="getServiceStatusTagType(selectedStudent.serviceStatus)" size="small">
                      {{ getServiceStatusLabel(selectedStudent.serviceStatus) }}
                    </el-tag>
                  </div>
                  <div class="flex flex-col space-y-1">
                    <span class="text-gray-500">服务类型</span>
                    <span class="text-gray-800 font-medium">{{ getContractTypeLabel(selectedStudent.contractType) }}</span>
                  </div>
                  <div class="flex flex-col space-y-1 col-span-2">
                    <span class="text-gray-500">签约时间</span>
                    <span class="text-gray-800 font-medium">{{ formatDateTime(selectedStudent.signedAt) }}</span>
                  </div>
                </div>
              </div>
              
              <!-- 备注信息 -->
              <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6 lg:col-span-2" v-if="selectedStudent.notes">
                <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <el-icon class="mr-2 text-primary"><Document /></el-icon>
                  备注信息
                </h4>
                <p class="text-gray-700 text-sm leading-relaxed">{{ selectedStudent.notes }}</p>
              </div>
            </div>
          </el-tab-pane>

          <!-- 服务进度 -->
          <el-tab-pane label="服务进度" name="progress">
            <div class="mt-4 bg-white rounded-lg shadow-sm border border-gray-100 p-6">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-center">
                <div class="md:col-span-1 flex flex-col items-center justify-center">
                  <h3 class="text-lg font-medium mb-4 text-gray-800">整体服务进度</h3>
                  <el-progress
                    type="circle"
                    :percentage="selectedStudent.progress"
                    :width="150"
                    :stroke-width="10"
                    class="mb-4"
                  />
                  <p class="text-gray-600">已完成 {{ selectedStudent.progress }}%</p>
                </div>
                
                <div class="md:col-span-2 space-y-4">
                  <div class="flex items-center p-4 rounded-lg transition-all duration-300" 
                       v-for="(stage, index) in serviceStages" 
                       :key="index"
                       :class="{ 
                         'bg-green-50 border-l-4 border-green-400 shadow-sm': index < selectedStudent.currentStage, 
                         'bg-indigo-50 border-l-4 border-indigo-400 shadow-md': index === selectedStudent.currentStage,
                         'bg-gray-50 border-l-4 border-gray-300': index > selectedStudent.currentStage
                       }"
                  >
                    <div class="flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-4"
                         :class="{ 
                           'bg-green-500 text-white': index < selectedStudent.currentStage,
                           'bg-indigo-500 text-white animate-pulse': index === selectedStudent.currentStage,
                           'bg-gray-300 text-gray-600': index > selectedStudent.currentStage
                         }"
                    >
                      <el-icon v-if="index < selectedStudent.currentStage"><Check /></el-icon>
                      <span v-else class="text-lg font-bold">{{ index + 1 }}</span>
                    </div>
                    <div class="flex-1">
                      <h4 class="font-semibold text-gray-800">{{ stage.name }}</h4>
                      <p class="text-sm text-gray-600">{{ stage.description }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="studentDetailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="openStudentEditDialog(selectedStudent)">
            编辑信息
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 客户编辑对话框 -->
    <el-dialog
      v-model="studentEditDialogVisible"
              title="编辑客户信息"
      width="900px"
      destroy-on-close
      top="5vh"
    >
      <el-form
        ref="studentEditFormRef"
        :model="studentEditFormData"
        :rules="studentEditFormRules"
        label-width="120px"
        size="default"
      >
        <!-- 基本信息 -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <el-icon class="mr-2 text-primary"><User /></el-icon>
            基本信息
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="客户姓名" prop="name" required>
              <el-input v-model="studentEditFormData.name" placeholder="请输入客户姓名" clearable />
            </el-form-item>
            <el-form-item label="性别" prop="gender">
              <el-radio-group v-model="studentEditFormData.gender">
                <el-radio value="male">男</el-radio>
                <el-radio value="female">女</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="studentEditFormData.phone" placeholder="请输入联系电话" clearable />
            </el-form-item>
            <el-form-item label="邮箱地址" prop="email">
              <el-input v-model="studentEditFormData.email" placeholder="请输入邮箱地址" clearable />
            </el-form-item>
            <el-form-item label="出生日期" prop="birthDate">
              <el-date-picker v-model="studentEditFormData.birthDate" type="date" placeholder="请选择出生日期" class="w-full" />
            </el-form-item>
          </div>
        </div>

        <!-- 教育背景 -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <el-icon class="mr-2 text-primary"><School /></el-icon>
            教育背景
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="当前院校" prop="university">
              <el-input v-model="studentEditFormData.university" placeholder="请输入当前就读院校" clearable />
            </el-form-item>
            <el-form-item label="专业" prop="major">
              <el-input v-model="studentEditFormData.major" placeholder="请输入专业" clearable />
            </el-form-item>
            <el-form-item label="年级" prop="grade">
              <el-select v-model="studentEditFormData.grade" placeholder="请选择年级" class="w-full">
                <el-option label="高一" value="grade10" />
                <el-option label="高二" value="grade11" />
                <el-option label="高三" value="grade12" />
                <el-option label="大一" value="freshman" />
                <el-option label="大二" value="sophomore" />
                <el-option label="大三" value="junior" />
                <el-option label="大四" value="senior" />
                <el-option label="研一" value="graduate1" />
                <el-option label="研二" value="graduate2" />
              </el-select>
            </el-form-item>
            <el-form-item label="GPA" prop="gpa">
              <el-input v-model="studentEditFormData.gpa" placeholder="请输入GPA" clearable />
            </el-form-item>
          </div>
        </div>

        <!-- 申请意向 -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <el-icon class="mr-2 text-primary"><Flag /></el-icon>
            申请意向
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="目标国家" prop="targetCountry">
              <el-select v-model="studentEditFormData.targetCountry" placeholder="请选择目标国家" class="w-full">
                <el-option label="美国" value="US" />
                <el-option label="英国" value="UK" />
                <el-option label="加拿大" value="CA" />
                <el-option label="澳大利亚" value="AU" />
                <el-option label="新加坡" value="SG" />
                <el-option label="中国香港" value="HK" />
                <el-option label="其他" value="OTHER" />
              </el-select>
            </el-form-item>
            <el-form-item label="目标学位" prop="targetDegree">
              <el-select v-model="studentEditFormData.targetDegree" placeholder="请选择目标学位" class="w-full">
                <el-option label="本科" value="bachelor" />
                <el-option label="硕士" value="master" />
                <el-option label="博士" value="phd" />
              </el-select>
            </el-form-item>
            <el-form-item label="目标专业" prop="targetMajor">
              <el-input v-model="studentEditFormData.targetMajor" placeholder="请输入目标申请专业" clearable />
            </el-form-item>
            <el-form-item label="语言成绩" prop="languageScores">
              <el-input v-model="studentEditFormData.languageScores" placeholder="如：TOEFL 100, IELTS 7.0" clearable />
            </el-form-item>
          </div>
        </div>

        <!-- 服务信息 -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <el-icon class="mr-2 text-primary"><Setting /></el-icon>
            服务信息
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="负责顾问" prop="consultant">
              <el-select v-model="studentEditFormData.consultant" placeholder="请选择负责顾问" class="w-full">
                <el-option label="李老师" value="李老师" />
                <el-option label="王老师" value="王老师" />
                <el-option label="张老师" value="张老师" />
                <el-option label="刘老师" value="刘老师" />
              </el-select>
            </el-form-item>
            <el-form-item label="服务类型" prop="serviceType">
              <el-select v-model="studentEditFormData.serviceType" placeholder="请选择服务类型" class="w-full">
                <el-option label="标准服务包" value="standard" />
                <el-option label="高端服务包" value="premium" />
                <el-option label="定制服务包" value="custom" />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 备注信息 -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <el-icon class="mr-2 text-primary"><Document /></el-icon>
            备注信息
          </h3>
          <el-form-item label="备注说明" prop="notes">
            <el-input
              v-model="studentEditFormData.notes"
              type="textarea"
              :rows="4"
              placeholder="请输入备注信息，如客户特殊需求、家庭背景等"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleStudentEditCancel">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleStudentEditSubmit"
            :loading="studentEditSubmitting"
          >
            更新客户信息
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Download, Search, MoreFilled, Edit, Delete, Check,
  VideoPause, VideoPlay, User, Checked, Trophy, TrendCharts,
  School, Flag, Setting, Document, Phone, Message as ElMessageIcon, UserFilled
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const searchQuery = ref('')
const serviceStatusFilter = ref('')
const contractTypeFilter = ref('')
const consultantFilter = ref('')
const students = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 统计数据
const statistics = ref({
  total: 0,
  active: 0,
  completed: 0,
  thisMonth: 0
})

// 服务阶段定义
const serviceStages = ref([
      { name: '资料收集', description: '收集客户申请材料' },
  { name: '申请规划', description: '制定申请方案和时间表' },
  { name: '文书撰写', description: '撰写个人陈述和推荐信' },
  { name: '申请提交', description: '提交申请材料' },
  { name: '结果跟踪', description: '跟踪申请结果' }
])

// 服务管理对话框
const serviceDialogVisible = ref(false)
const selectedStudent = ref(null)
const activeTab = ref('overview')
const progressForm = ref({
  stage: 0,
  percentage: 0,
  notes: ''
})

// 客户详情对话框相关
const studentDetailDialogVisible = ref(false)
const studentDetailActiveTab = ref('basic')

// 客户编辑对话框相关
const studentEditDialogVisible = ref(false)
const studentEditFormData = ref({
  // 基本信息
  name: '',
  gender: 'male',
  phone: '',
  email: '',
  birthDate: null,
  parentContact: '',
  
  // 教育背景
  university: '',
  major: '',
  grade: '',
  gpa: '',
  enrollmentDate: null,
  graduationDate: null,
  
  // 申请意向
  targetCountry: '',
  targetDegree: '',
  targetMajor: '',
  expectedApplicationDate: null,
  languageScores: '',
  standardizedTests: '',
  
  // 服务信息
  status: 'signed',
  consultant: '',
  serviceType: '',
  
  // 备注
  notes: ''
})
const studentEditSubmitting = ref(false)
const studentEditFormRef = ref()

// 表单验证规则
const studentEditFormRules = {
  name: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  consultant: [
    { required: true, message: '请选择负责顾问', trigger: 'change' }
  ]
}

// 计算属性
const filteredStudents = computed(() => {
  let filtered = students.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(student => 
      student.name.toLowerCase().includes(query) ||
      student.university.toLowerCase().includes(query) ||
      student.major.toLowerCase().includes(query)
    )
  }

  if (serviceStatusFilter.value) {
    filtered = filtered.filter(student => student.serviceStatus === serviceStatusFilter.value)
  }

  if (contractTypeFilter.value) {
    filtered = filtered.filter(student => student.contractType === contractTypeFilter.value)
  }

  if (consultantFilter.value) {
    filtered = filtered.filter(student => student.consultant === consultantFilter.value)
  }

  return filtered
})

// 方法
const getStudentInitials = (name) => {
  if (!name) return 'N'
  const names = name.split(' ')
  if (names.length === 1) {
    return names[0].charAt(0).toUpperCase()
  }
  return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase()
}

const getServiceStatusTagType = (status) => {
  const statusMap = {
    active: 'success',
    completed: 'info',
    paused: 'warning'
  }
  return statusMap[status] || ''
}

const getServiceStatusLabel = (status) => {
  const statusMap = {
    active: '服务中',
    completed: '已完成',
    paused: '暂停中'
  }
  return statusMap[status] || '未知'
}

const getContractTypeTagType = (type) => {
  const typeMap = {
    standard: 'primary',
    premium: 'warning',
    custom: 'danger'
  }
  return typeMap[type] || ''
}

const getContractTypeLabel = (type) => {
  const typeMap = {
    standard: '标准版',
    premium: '高端版',
    custom: '定制版'
  }
  return typeMap[type] || '未知'
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN')
}

const getGradeLabel = (grade) => {
  const gradeMap = {
    grade10: '高一',
    grade11: '高二',
    grade12: '高三',
    freshman: '大一',
    sophomore: '大二',
    junior: '大三',
    senior: '大四',
    graduate1: '研一',
    graduate2: '研二'
  }
  return gradeMap[grade] || '未知'
}

const getCountryLabel = (country) => {
  const countryMap = {
    US: '美国',
    UK: '英国',
    CA: '加拿大',
    AU: '澳大利亚',
    SG: '新加坡',
    HK: '中国香港',
    OTHER: '其他'
  }
  return countryMap[country] || '未知'
}

const handleSearch = () => {
  loadStudents()
}

const handleAddStudent = () => {
  // 跳转到潜在客户页面
  router.push('/crm/students/unsigned')
}

const handleExportData = () => {
  ElMessage.info('导出功能开发中')
}

const handleStudentClick = (student) => {
  // 打开客户详情对话框
  openStudentDetailDialog(student)
}

const handleViewDetail = (student) => {
  // 打开客户详情对话框
  openStudentDetailDialog(student)
}

const handleEditStudent = (student) => {
  // 打开编辑客户对话框
  openStudentEditDialog(student)
}

const handleManageService = (student) => {
  selectedStudent.value = student
  progressForm.value = {
    stage: student.currentStage || 0,
    percentage: student.progress || 0,
    notes: ''
  }
  serviceDialogVisible.value = true
  activeTab.value = 'overview'
}

const handleUpdateProgress = async () => {
  try {
    // TODO: 调用更新进度API
    ElMessage.success('进度更新成功')
    serviceDialogVisible.value = false
    loadStudents()
  } catch (error) {
    ElMessage.error('更新失败，请重试')
  }
}

const handlePauseService = async (student) => {
  try {
    await ElMessageBox.confirm(
      `确定要暂停 ${student.name} 的服务吗？`,
      '暂停服务',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用暂停服务API
    ElMessage.success('服务已暂停')
    loadStudents()
  } catch (error) {
    // 用户取消
  }
}

const handleResumeService = async (student) => {
  try {
    // TODO: 调用恢复服务API
    ElMessage.success('服务已恢复')
    loadStudents()
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  }
}

const handleCompleteService = async (student) => {
  try {
    await ElMessageBox.confirm(
      `确定要完成 ${student.name} 的服务吗？`,
      '完成服务',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用完成服务API
    ElMessage.success('服务已完成')
    loadStudents()
  } catch (error) {
    // 用户取消
  }
}

const handleDeleteStudent = async (student) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除客户 ${student.name} 吗？此操作不可恢复！`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用删除API
    ElMessage.success('删除成功')
    loadStudents()
  } catch (error) {
    // 用户取消删除
  }
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadStudents()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadStudents()
}

// 客户详情和编辑对话框处理函数
const openStudentDetailDialog = (student) => {
  selectedStudent.value = student
  studentDetailDialogVisible.value = true
  studentDetailActiveTab.value = 'basic'
}

const openStudentEditDialog = (student) => {
  // 填充编辑表单数据
  Object.keys(studentEditFormData.value).forEach(key => {
    if (student.hasOwnProperty(key)) {
      studentEditFormData.value[key] = student[key]
    }
  })
  selectedStudent.value = student
  studentEditDialogVisible.value = true
}

const handleStudentEditSubmit = async () => {
  try {
    // 验证表单
    await studentEditFormRef.value.validate()
    
    studentEditSubmitting.value = true

    // TODO: 调用API保存数据
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用

    ElMessage.success('客户信息更新成功')
    
    studentEditDialogVisible.value = false
    loadStudents() // 刷新列表

  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('更新失败，请重试')
    }
  } finally {
    studentEditSubmitting.value = false
  }
}

const handleStudentEditCancel = () => {
  studentEditDialogVisible.value = false
}

// 格式化函数
const getDegreeLabel = (degree) => {
  const degreeMap = {
    bachelor: '本科',
    master: '硕士',
    phd: '博士'
  }
  return degreeMap[degree] || '未知'
}

const formatDateTime = (date) => {
  if (!date) return '未知'
  return new Date(date).toLocaleString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' })
}

const loadStudents = async () => {
  try {
    loading.value = true
    // TODO: 实际API调用
    // 模拟数据
    students.value = [
      {
        id: 1,
        name: '王小明',
        university: '北京大学',
        schoolType: '985/211',
        major: '计算机科学与技术',
        gpa: '3.8',
        grade: 'senior',
        targetCountry: 'US',
        applicationYear: '2025',
        serviceStatus: 'active',
        contractType: 'premium',
        consultant: '李老师',
        signedAt: '2024-01-15',
        progress: 65,
        currentStage: 2,
        phone: '13812345678',
        email: '<EMAIL>',
        gender: 'male',
        birthDate: '2000-01-01',
        targetDegree: 'master',
        targetMajor: '计算机科学',
        languageScores: 'TOEFL 100',
        standardizedTests: 'GRE 320',
        serviceType: 'premium',
        notes: '客户学习能力强，有明确的留学规划'
      },
      {
        id: 2,
        name: '张小红',
        university: '清华大学',
        schoolType: '985/211',
        major: '电子工程',
        gpa: '3.9',
        grade: 'senior',
        targetCountry: 'UK',
        applicationYear: '2024',
        serviceStatus: 'completed',
        contractType: 'standard',
        consultant: '王老师',
        signedAt: '2023-12-01',
        progress: 100,
        currentStage: 4,
        phone: '13887654321',
        email: '<EMAIL>',
        gender: 'female',
        birthDate: '1999-08-15',
        targetDegree: 'master',
        targetMajor: '电子工程',
        languageScores: 'IELTS 7.5',
        standardizedTests: 'GRE 325',
        serviceType: 'standard',
        notes: '已成功申请到目标院校'
      },
      {
        id: 3,
        name: '李小强',
        university: '复旦大学',
        schoolType: '985/211',
        major: '金融学',
        gpa: '3.7',
        grade: 'junior',
        targetCountry: 'CA',
        applicationYear: '2026',
        serviceStatus: 'active',
        contractType: 'custom',
        consultant: '张老师',
        signedAt: '2024-02-10',
        progress: 30,
        currentStage: 1,
        phone: '13698765432',
        email: '<EMAIL>',
        gender: 'male',
        birthDate: '2001-03-20',
        targetDegree: 'master',
        targetMajor: '金融工程',
        languageScores: 'TOEFL 95',
        standardizedTests: 'GMAT 680',
        serviceType: 'custom',
        notes: '需要重点关注文书质量'
      }
    ]
    
    total.value = students.value.length
    
    // 更新统计数据
    statistics.value = {
      total: students.value.length,
      active: students.value.filter(s => s.serviceStatus === 'active').length,
      completed: students.value.filter(s => s.serviceStatus === 'completed').length,
      thisMonth: students.value.filter(s => {
        const signedDate = new Date(s.signedAt)
        const now = new Date()
        return signedDate.getMonth() === now.getMonth() && signedDate.getFullYear() === now.getFullYear()
      }).length
    }
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadStudents()
})
</script>

<style>
@import './styles/crm-theme.css';
</style>

<style scoped>
.student-table :deep(.el-table__row) {
  cursor: pointer;
}

.student-table :deep(.el-table__row:hover) {
  background-color: #f9fafb; /* a slightly different hover for better visual feedback */
}

.crm-client-detail-dialog .el-dialog__body {
  padding: 8px 12px;
  background-color: #f9fafb; /* Light gray background for the dialog body */
}
</style> 