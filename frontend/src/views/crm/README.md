# CRM系统模块说明

## 系统概述

CRM系统采用统一的学生管理架构，支持两种服务角色的切换：**前期规划服务**和**后期服务**，为不同阶段的学生提供专业的留学申请服务。

## 角色说明

### 前期规划服务
- **目标用户**: 未签约和已签约的学生
- **主要功能**: 学生管理、意向跟踪、签约流程
- **核心页面**:
  - 未签约学生 (`/crm/students/unsigned`)
  - 签约学生 (`/crm/students/signed`)

### 后期服务  
- **目标用户**: 已签约的学生（统一概念）
- **主要功能**: 完整的申请流程管理
- **核心页面**:
  - 签约学生 (`/crm/students/signed`) - 统一的学生管理界面
  - 任务管理 (`/crm/tasks`) - 申请进度跟踪

## 系统架构（已优化）

### 路由结构
- **主路由**: `/crm` → 自动重定向到 `/crm/students/unsigned`
- **学生管理**（统一概念）:
  - `/crm/students/unsigned` - 未签约学生列表
  - `/crm/students/signed` - 签约学生列表（前期+后期服务通用）
  - `/crm/students/add` - 添加学生
  - `/crm/students/:id` - 学生详情
  - `/crm/students/:id/edit` - 编辑学生
- **任务管理**:
  - `/crm/tasks` - 任务管理
  - `/crm/tasks/add` - 添加任务
  - `/crm/tasks/:id/edit` - 编辑任务

### 页面组件

#### 1. 未签约学生 (UnsignedStudents.vue)
- **功能**: 管理潜在客户，跟踪签约进度
- **特色功能**:
  - 学生状态跟踪 (初次咨询/跟进中/意向强烈/待签约)
  - 表格和卡片两种视图模式
  - 在线签约功能
  - 搜索和筛选
- **目标用户**: 规划顾问

#### 2. 签约学生 (SignedStudents.vue)  
- **功能**: 统一管理已签约学生（前期+后期服务）
- **特色功能**:
  - 服务状态管理 (服务中/已完成/暂停)
  - 服务进度跟踪
  - 统计概览卡片
  - 服务管理对话框
  - 根据角色显示不同的操作选项
- **目标用户**: 规划顾问和服务顾问

#### 3. 学生表单 (StudentForm.vue)
- **功能**: 统一的学生信息添加和编辑
- **表单分组**:
  - 基本信息 (姓名、联系方式等)
  - 教育背景 (院校、专业、GPA等)
  - 申请意向 (目标国家、专业等)
  - 服务信息 (状态、负责顾问等)
- **验证**: 完整的表单验证和错误处理

#### 4. 学生详情 (StudentDetail.vue)
- **功能**: 展示学生完整档案信息
- **标签页**（根据学生状态动态显示）:
  - 基本信息
  - 教育背景  
  - 申请意向
  - 服务记录
  - 备注管理
  - 申请材料（后期服务）
  - 选校管理（后期服务）
  - 文书管理（后期服务）
  - 网申进度（后期服务）
  - Offer管理（后期服务）
- **操作**: 编辑、签约、导出等

#### 5. 任务管理 (TaskManagement.vue)
- **功能**: 任务分配和进度管理
- **特色**: 紧急程度可视化、阶段管理
- **任务详情**: 集成在任务管理页面中，无需单独页面

#### 6. 任务表单 (TaskForm.vue)
- **功能**: 任务创建和编辑
- **特色**: 完整的任务信息管理

## 侧边栏菜单

### CRM系统分组
- **角色切换**: 前期规划服务 ⇄ 后期服务
- **前期规划服务菜单**:
  - 未签约学生
  - 签约学生
- **后期服务菜单**:
  - 签约学生（同一页面，不同视角）
  - 任务管理

## 文件结构（已优化）

```
crm/
├── 学生管理（统一概念）
│   ├── UnsignedStudents.vue     # 未签约学生列表
│   ├── SignedStudents.vue       # 签约学生列表（前期+后期服务通用）
│   ├── StudentForm.vue          # 学生表单（统一的添加/编辑）
│   └── StudentDetail.vue        # 学生详情（统一的详情页面）
├── 任务管理
│   ├── TaskManagement.vue       # 任务管理主页（包含任务详情）
│   └── TaskForm.vue             # 任务表单
├── 组件
│   └── TaskList.vue             # 任务列表组件
├── 整合方案.md                  # 整合方案文档
└── README.md                    # 本文档
```

## 优化成果

### 删除的重复文件：
- ❌ `ClientList.vue` - 与SignedStudents.vue功能重复
- ❌ `ClientForm.vue` - 与StudentForm.vue功能重复  
- ❌ `ClientDetail.vue` - 与StudentDetail.vue功能重复
- ❌ `TaskDetail.vue` - 任务详情已集成到TaskManagement中
- ❌ `components/ClientProfile.vue` - 未使用的组件

### 路由优化：
- 删除了重复的 `/crm/clients` 系列路由
- 简化了任务管理路由
- 统一使用学生概念

### 优势：
- ✅ 减少代码重复，降低维护成本
- ✅ 统一业务概念，避免混淆
- ✅ 简化路由结构，提高可读性
- ✅ 集中功能管理，增强用户体验

## 🎨 设计规范

### 颜色系统
- **主色调**：Indigo (#6366f1)
- **成功色**：Green (#10b981) 
- **警告色**：Orange (#f59e0b)
- **危险色**：Red (#ef4444)
- **信息色**：Blue (#3b82f6)

### 组件样式
- **卡片阴影**：hover时轻微上浮效果
- **边框颜色**：左侧彩色边框表示优先级
- **渐变背景**：学生头像使用渐变色
- **圆角设计**：统一8px圆角

### 交互动效
- **悬停效果**：卡片阴影变化、颜色过渡
- **加载状态**：skeleton loading
- **过渡动画**：200ms缓动过渡

## 📊 状态管理

使用 Pinia 进行状态管理，位于 `@/stores/crm.js`：

```javascript
// 主要状态
- clients: []           // 学生列表（统一概念）
- currentClient: null   // 当前学生
- tasks: []            // 任务列表  
- taskStats: {}        // 任务统计
- clientNotes: []      // 学生备注

// 主要方法
- fetchClients()       // 获取学生列表
- fetchTasks()         // 获取任务列表
- createClient()       // 创建学生
- updateTask()         // 更新任务
```

## 🌐 API 接口

API 封装位于 `@/api/crm.js`，包含以下模块：

- **clientApi**：学生管理相关接口（统一概念）
- **taskApi**：任务管理相关接口
- **materialApi**：材料管理相关接口
- **schoolApi**：选校管理相关接口
- **documentApi**：文书管理相关接口
- **applicationApi**：网申进度相关接口
- **offerApi**：Offer管理相关接口
- **noteApi**：备注相关接口

## 🛣️ 路由配置

路由配置位于 `@/router/modules/crm.js`：

```
/crm                         # CRM首页（重定向到未签约学生）
├── /students/unsigned       # 未签约学生列表
├── /students/signed         # 签约学生列表（前期+后期服务通用）
├── /students/add            # 添加学生
├── /students/:id            # 学生详情
├── /students/:id/edit       # 编辑学生
├── /tasks                   # 任务管理
├── /tasks/add               # 添加任务
└── /tasks/:id/edit          # 编辑任务
```

## 🔧 开发指南

### 添加新功能

1. **新增页面组件**：
   ```vue
   <template>
     <!-- 页面内容 -->
   </template>
   
   <script setup>
   // 使用组合式API
   </script>
   ```

2. **更新路由配置**：
   ```javascript
   // 在 crm.js 路由文件中添加新路由
   ```

3. **添加API接口**：
   ```javascript
   // 在 @/api/crm.js 中添加相应API方法
   ```

4. **更新状态管理**：
   ```javascript
   // 在 @/stores/crm.js 中添加状态和方法
   ```

### 样式指南

- 使用 TailwindCSS 功能类优先
- 避免自定义CSS，除非必要
- 保持设计系统一致性
- 响应式设计考虑

### 代码规范

- 使用 Vue 3 组合式API
- TypeScript 类型提示
- ESLint 代码检查
- 组件命名采用 PascalCase
- 文件命名采用 kebab-case

## 🚧 待实现功能

以下组件功能可在现有页面中实现：

1. **MaterialsManagement** - 在StudentDetail.vue中作为标签页
2. **SchoolSelection** - 在StudentDetail.vue中作为标签页
3. **DocumentsManagement** - 在StudentDetail.vue中作为标签页
4. **ApplicationProgress** - 在StudentDetail.vue中作为标签页
5. **OffersManagement** - 在StudentDetail.vue中作为标签页

## 📱 移动端适配

系统采用响应式设计，主要断点：

- **手机**：< 768px
- **平板**：768px - 1024px  
- **桌面**：> 1024px

移动端优化：
- 卡片堆叠布局
- 触摸友好的按钮大小
- 简化的导航菜单
- 优化的表单输入

## 🧪 测试说明

目前使用模拟数据进行开发和测试，待后端API完成后需要：

1. 替换模拟数据为真实API调用
2. 添加错误处理和加载状态
3. 完善用户交互反馈
4. 添加单元测试和e2e测试

---

## 📞 联系方式

如有问题或建议，请联系开发团队。 