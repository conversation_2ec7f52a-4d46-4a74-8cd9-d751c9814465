/* CRM系统紫色主题样式 */

/* 覆盖Element Plus主题色 */
:root {
  --el-color-primary: #4F46E5;
  --el-color-primary-light-3: #7C69EF;
  --el-color-primary-light-5: #9B90F2;
  --el-color-primary-light-7: #BAB5F5;
  --el-color-primary-light-8: #CAC6F7;
  --el-color-primary-light-9: #DAD9F9;
  --el-color-primary-dark-2: #4338CA;
}

/* 标签页样式 */
.el-tabs__nav-wrap {
  background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
  padding: 0 24px;
  border-radius: 16px 16px 0 0;
}

.el-tabs__item {
  font-weight: 600;
  color: #64748B;
  padding: 16px 24px;
  height: auto;
  border-radius: 12px 12px 0 0;
  margin-right: 8px;
  transition: all 0.3s ease;
}

.el-tabs__item.is-active {
  background: linear-gradient(135deg, #4F46E5 0%, #6366F1 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
  transform: translateY(-2px);
}

.el-tabs__item:hover:not(.is-active) {
  background: rgba(79, 70, 229, 0.1);
  color: #4F46E5;
}

.el-tabs__active-bar {
  display: none;
}

.el-tabs__content {
  background: white;
  border-radius: 0 0 16px 16px;
}

/* 按钮样式增强 - 限制在CRM模块内 */
.crm-container .el-button--primary {
  background: linear-gradient(135deg, #4F46E5 0%, #6366F1 100%) !important;
  border: none !important;
  border-color: #4F46E5 !important;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
  transition: all 0.3s ease;
  color: white !important;
}

.crm-container .el-button--primary:hover {
  background: linear-gradient(135deg, #4338CA 0%, #5B21B6 100%) !important;
  border-color: #4338CA !important;
  box-shadow: 0 6px 16px rgba(79, 70, 229, 0.4);
  transform: translateY(-2px);
  color: white !important;
}

.crm-container .el-button--primary:focus {
  background: linear-gradient(135deg, #4F46E5 0%, #6366F1 100%) !important;
  border-color: #4F46E5 !important;
  color: white !important;
}

.crm-container .el-button--primary:active {
  background: linear-gradient(135deg, #4338CA 0%, #5B21B6 100%) !important;
  border-color: #4338CA !important;
  color: white !important;
}

.el-button--primary.is-plain {
  background: transparent !important;
  border: 2px solid #4F46E5 !important;
  color: #4F46E5 !important;
}

.el-button--primary.is-plain:hover {
  background: #4F46E5 !important;
  border-color: #4F46E5 !important;
  color: white !important;
}

.el-button--primary.is-plain:focus {
  background: transparent !important;
  border-color: #4F46E5 !important;
  color: #4F46E5 !important;
}

.el-button--primary.is-plain:active {
  background: #4F46E5 !important;
  border-color: #4F46E5 !important;
  color: white !important;
}

/* 确保所有type="primary"的按钮都使用紫色主题 - 限制在CRM模块内 */
.crm-container .el-button[type="primary"],
.crm-container button.el-button--primary,
.crm-container .el-button.el-button--primary,
.crm-container button[type="primary"] {
  background: linear-gradient(135deg, #4F46E5 0%, #6366F1 100%) !important;
  border-color: #4F46E5 !important;
  color: white !important;
}

.crm-container .el-button[type="primary"]:hover,
.crm-container button.el-button--primary:hover,
.crm-container .el-button.el-button--primary:hover,
.crm-container button[type="primary"]:hover {
  background: linear-gradient(135deg, #4338CA 0%, #5B21B6 100%) !important;
  border-color: #4338CA !important;
  color: white !important;
}

/* 覆盖任何可能的Element Plus默认样式 - 限制在CRM模块内 */
.crm-container div .el-button--primary,
.crm-container div button.el-button--primary,
.crm-container .el-dialog .el-button--primary,
.crm-container .el-form .el-button--primary {
  background: linear-gradient(135deg, #4F46E5 0%, #6366F1 100%) !important;
  border-color: #4F46E5 !important;
  color: white !important;
}

.crm-container div .el-button--primary:hover,
.crm-container div button.el-button--primary:hover,
.crm-container .el-dialog .el-button--primary:hover,
.crm-container .el-form .el-button--primary:hover {
  background: linear-gradient(135deg, #4338CA 0%, #5B21B6 100%) !important;
  border-color: #4338CA !important;
  color: white !important;
}

/* 成功按钮样式 - 浅绿色 - 限制在CRM模块内 */
.crm-container .el-button--success {
  background: linear-gradient(135deg, #86EFAC 0%, #4ADE80 100%) !important;
  border-color: #4ADE80 !important;
  color: #064E3B !important;
}

.crm-container .el-button--success:hover {
  background: linear-gradient(135deg, #4ADE80 0%, #22C55E 100%) !important;
  border-color: #22C55E !important;
  color: #064E3B !important;
}

/* 警告按钮样式 - 限制在CRM模块内 */
.crm-container .el-button--warning {
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%) !important;
  border-color: #F59E0B !important;
  color: white !important;
}

.crm-container .el-button--warning:hover {
  background: linear-gradient(135deg, #D97706 0%, #B45309 100%) !important;
  border-color: #D97706 !important;
  color: white !important;
}

/* 危险按钮样式 - 限制在CRM模块内 */
.crm-container .el-button--danger {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%) !important;
  border-color: #EF4444 !important;
  color: white !important;
}

.crm-container .el-button--danger:hover {
  background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%) !important;
  border-color: #DC2626 !important;
  color: white !important;
}

/* 输入框样式 - 限制在CRM模块内 */
.crm-container .el-input__wrapper {
  border-radius: 12px;
  border: 2px solid #E2E8F0;
  transition: all 0.3s ease;
}

.crm-container .el-input__wrapper:hover {
  border-color: #CBD5E1;
}

.crm-container .el-input__wrapper.is-focus {
  border-color: #4F46E5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 选择框样式 */
.el-select .el-input__wrapper {
  border-radius: 12px;
}

.el-select-dropdown {
  border-radius: 12px;
  border: 2px solid #E2E8F0;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.el-select-dropdown__item:hover {
  background: rgba(79, 70, 229, 0.1);
  color: #4F46E5;
}

.el-select-dropdown__item.selected {
  background: #4F46E5;
  color: white;
}

/* 对话框样式 */
.el-dialog {
  border-radius: 16px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.el-dialog__header {
  background: linear-gradient(135deg, #4F46E5 0%, #6366F1 100%);
  border-radius: 16px 16px 0 0;
  padding: 24px;
}

.el-dialog__title {
  color: white;
  font-weight: 600;
  font-size: 20px;
}

.el-dialog__close {
  color: white;
}

.el-dialog__close:hover {
  color: #E2E8F0;
}

.el-dialog__body {
  padding: 24px;
}

/* 表格样式 - 限制在CRM模块内 */
.crm-container .el-table .el-table__header th {
  background: linear-gradient(135deg, #4F46E5 0%, #6366F1 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  border: none !important;
}

.crm-container .el-table .el-table__row:hover > td {
  background-color: rgba(79, 70, 229, 0.05) !important;
}

.crm-container .el-table .el-table__body tr:nth-child(even) td {
  background-color: #FAFBFC;
}

/* 表单样式 */
.el-form-item__label {
  font-weight: 600;
  color: #374151;
}

.el-form-item.is-error .el-input__wrapper {
  border-color: #EF4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* 标签样式 - 限制在CRM模块内 */
.crm-container .el-tag {
  border-radius: 8px;
  font-weight: 500;
}

.crm-container .el-tag--primary {
  background: rgba(79, 70, 229, 0.1);
  border-color: #4F46E5;
  color: #4F46E5;
}

.crm-container .el-tag--success {
  background: rgba(134, 239, 172, 0.2);
  border-color: #86EFAC;
  color: #064E3B;
}

.crm-container .el-tag--warning {
  background: rgba(251, 146, 60, 0.1);
  border-color: #FB923C;
  color: #EA580C;
}

.crm-container .el-tag--danger {
  background: rgba(239, 68, 68, 0.1);
  border-color: #EF4444;
  color: #DC2626;
}

/* 进度条样式 */
.el-progress-bar__outer {
  border-radius: 8px;
  background-color: #F1F5F9;
}

.el-progress-bar__inner {
  border-radius: 8px;
  background: linear-gradient(90deg, #4F46E5 0%, #7C3AED 100%);
}

/* 下拉菜单样式 */
.el-dropdown-menu {
  border-radius: 12px;
  border: none;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.el-dropdown-menu__item:hover {
  background: rgba(79, 70, 229, 0.1);
  color: #4F46E5;
}

/* 日期选择器样式 */
.el-date-editor .el-input__wrapper {
  border-radius: 12px;
}

.el-picker-panel {
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* 消息提示样式 - 统一白底黑字设计 */
.el-message {
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  background: white !important;
  border: 1px solid #e5e7eb !important;
  color: #374151 !important;
}

.el-message--success {
  background: white !important;
  border: 1px solid #10b981 !important;
  color: #374151 !important;
}

.el-message--warning {
  background: white !important;
  border: 1px solid #f59e0b !important;
  color: #374151 !important;
}

.el-message--error {
  background: white !important;
  border: 1px solid #ef4444 !important;
  color: #374151 !important;
}

.el-message--info {
  background: white !important;
  border: 1px solid #3b82f6 !important;
  color: #374151 !important;
}

/* 加载状态样式 */
.el-loading-mask {
  background-color: rgba(79, 70, 229, 0.1);
}

.el-loading-spinner .circular {
  stroke: #4F46E5;
}

/* 分页器样式 */
.el-pagination .el-pager li.active {
  background: #4F46E5;
  color: white;
  border-radius: 8px;
}

.el-pagination .el-pager li:hover {
  background: rgba(79, 70, 229, 0.1);
  color: #4F46E5;
  border-radius: 8px;
}

/* 自定义动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.5s ease-out;
}

/* 卡片悬停效果 */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(79, 70, 229, 0.15);
}

/* 统一的浅绿色主题 - 用于已完成状态 */
.completed-text {
  color: #065F46 !important;
}

.completed-bg {
  background: linear-gradient(135deg, #86EFAC 0%, #4ADE80 100%) !important;
  color: #064E3B !important;
}

.completed-border {
  border-color: #86EFAC !important;
}

.completed-gradient {
  background: linear-gradient(135deg, #86EFAC 0%, #4ADE80 100%) !important;
}

.completed-icon-bg {
  background: linear-gradient(135deg, #4ADE80 0%, #22C55E 100%) !important;
}

/* 覆盖Element Plus的成功状态为浅绿色 - 限制在CRM模块内 */
.crm-container .el-tag.el-tag--success {
  background: rgba(134, 239, 172, 0.2) !important;
  border-color: #86EFAC !important;
  color: #064E3B !important;
}

.crm-container .el-button.el-button--success {
  background: linear-gradient(135deg, #86EFAC 0%, #4ADE80 100%) !important;
  border-color: #4ADE80 !important;
  color: #064E3B !important;
}

.crm-container .el-button.el-button--success:hover {
  background: linear-gradient(135deg, #4ADE80 0%, #22C55E 100%) !important;
  border-color: #22C55E !important;
  color: #064E3B !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-dialog {
    margin: 20px;
    width: calc(100% - 40px);
  }
  
  .el-tabs__item {
    padding: 12px 16px;
  }
} 