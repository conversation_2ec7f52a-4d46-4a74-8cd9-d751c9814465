<template>
  <div class="crm-container p-6">
    <!-- 页面标题和操作区域 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">任务管理</h1>
        <div class="flex items-center mt-2 text-sm text-gray-500">
          <span class="mr-4">任务管理</span>
          <span class="text-gray-300">/</span>
          <span class="ml-4 text-[#4F46E5] font-medium">任务列表</span>
        </div>
      </div>
      <el-button 
        type="primary" 
        @click="handleAddTask"
        size="large"
        class="bg-[#4F46E5] hover:bg-[#4338CA] border-[#4F46E5] shadow-lg hover:shadow-xl transition-all duration-200"
      >
        <el-icon class="mr-2"><Plus /></el-icon>
        新建任务
      </el-button>
    </div>

    <!-- 统计概览卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 font-medium">总任务数</p>
            <p class="text-3xl font-bold text-gray-900 mt-2">{{ totalTasks }}</p>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-[#4F46E5] to-[#6366F1] rounded-xl flex items-center justify-center shadow-lg">
            <el-icon class="text-white text-xl"><DocumentCopy /></el-icon>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 font-medium">紧急任务</p>
            <p class="text-3xl font-bold text-red-600 mt-2">{{ urgentTasks }}</p>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg">
            <el-icon class="text-white text-xl"><Warning /></el-icon>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 font-medium">今日到期</p>
            <p class="text-3xl font-bold text-orange-600 mt-2">{{ todayDueTasks }}</p>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
            <el-icon class="text-white text-xl"><Clock /></el-icon>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 font-medium">已完成</p>
            <p class="text-3xl font-bold text-green-600 mt-2">{{ completedTasks }}</p>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
            <el-icon class="text-white text-xl"><Check /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选和排序区域 -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <el-input
          v-model="searchQuery"
          placeholder="搜索任务或客户..."
          clearable
          class="w-full"
          size="large"
        >
          <template #prefix>
            <el-icon class="text-[#4F46E5]"><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select 
          v-model="stageFilter" 
          placeholder="任务阶段" 
          clearable 
          class="w-full"
          size="large"
        >
          <el-option label="全部" value="" />
          <el-option label="资料收集" value="materials" />
          <el-option label="定校选校" value="school-selection" />
          <el-option label="文书写作" value="writing" />
          <el-option label="网申递交" value="application" />
        </el-select>

        <el-select 
          v-model="priorityFilter" 
          placeholder="优先级" 
          clearable 
          class="w-full"
          size="large"
        >
          <el-option label="全部" value="" />
          <el-option label="紧急" value="urgent" />
          <el-option label="重要" value="important" />
          <el-option label="普通" value="normal" />
          <el-option label="低优先级" value="low" />
        </el-select>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
      <el-table
        :data="filteredAndSortedTasks"
        style="width: 100%"
        :header-cell-style="{
          background: 'linear-gradient(135deg, #4F46E5 0%, #6366F1 100%)',
          color: '#ffffff',
          fontWeight: 600,
          fontSize: '14px',
          height: '64px',
          borderBottom: 'none'
        }"
        :cell-style="{
          fontSize: '14px',
          color: '#374151',
          height: '72px',
          borderBottom: '1px solid #F3F4F6'
        }"
        @row-click="handleTaskClick"
        class="task-table"
        v-loading="loading"
      >
        <el-table-column label="任务信息" min-width="200" fixed="left">
          <template #default="{ row }">
            <div class="flex items-start">
              <div 
                class="w-3 h-3 rounded-full mr-3 mt-2 flex-shrink-0"
                :class="getPriorityIndicatorClass(row.priority)"
              ></div>
              <div class="flex-1">
                <div class="font-semibold text-gray-900 mb-1">{{ row.title }}</div>
                <div class="text-sm text-gray-600 line-clamp-2">{{ row.description }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="关联学生" width="180">
          <template #default="{ row }">
            <div class="flex items-center">
              <div class="w-8 h-8 rounded-full bg-[#4F46E5] bg-opacity-10 flex items-center justify-center text-[#4F46E5] text-xs font-bold mr-2">
                {{ getStudentInitials(row.client_name) }}
              </div>
              <span class="font-medium text-gray-900">{{ row.client_name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="任务阶段" width="180">
          <template #default="{ row }">
            <el-tag :type="getStageTagType(row.stage)" size="small" effect="plain">
              {{ getStageLabel(row.stage) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="优先级" width="180">
          <template #default="{ row }">
            <div class="flex items-center">
              <span 
                class="text-sm font-medium"
                :class="getPriorityTextClass(row.priority)"
              >
                {{ getPriorityLabel(row.priority) }}
              </span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="180">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="截止日期" width="180">
          <template #default="{ row }">
            <div 
              class="text-sm font-medium"
              :class="getDueDateClass(row.due_date)"
            >
              {{ formatDueDate(row.due_date) }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            <span class="text-sm text-gray-600">{{ formatDate(row.created_at) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <div class="flex items-center space-x-2" @click.stop>
              <el-button 
                type="primary" 
                size="small" 
                @click="handleEditTask(row)"
                plain
              >
                编辑
              </el-button>
              <el-dropdown trigger="click">
                <el-button 
                  type="text" 
                  size="small" 
                  class="text-[#4F46E5] hover:text-[#4338CA] hover:bg-[#4F46E5]/10"
                >
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleCompleteTask(row)" v-if="row.status !== 'completed'">
                      <el-icon class="mr-1 text-green-500"><Check /></el-icon>
                      <span class="text-green-600">标记完成</span>
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleReopenTask(row)" v-if="row.status === 'completed'">
                      <el-icon class="mr-1 text-[#4F46E5]"><Refresh /></el-icon>
                      <span class="text-[#4F46E5]">重新打开</span>
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleDeleteTask(row)" divided>
                      <el-icon class="mr-1 text-red-500"><Delete /></el-icon>
                      <span class="text-red-600">删除任务</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex justify-between items-center py-4 px-6">
        <div class="text-sm text-gray-500">
          共 {{ filteredAndSortedTasks.length }} 条任务
        </div>
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="filteredAndSortedTasks.length"
          layout="sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          background
        />
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredAndSortedTasks.length === 0 && !loading" class="text-center py-12">
      <el-empty description="暂无任务数据">
        <el-button type="primary" @click="handleAddTask">创建第一个任务</el-button>
      </el-empty>
    </div>

    <!-- 任务表单对话框 -->
    <el-dialog
      v-model="taskDialogVisible"
      :title="isEditMode ? '编辑任务' : '添加任务'"
      width="700px"
      destroy-on-close
      top="5vh"
    >
      <el-form
        ref="taskFormRef"
        :model="taskFormData"
        :rules="taskFormRules"
        label-width="100px"
        size="default"
      >
        <!-- 基本信息 -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <el-icon class="mr-2 text-primary"><DocumentCopy /></el-icon>
            基本信息
          </h3>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="任务标题" prop="title" required>
              <el-input v-model="taskFormData.title" placeholder="请输入任务标题" clearable />
            </el-form-item>
            <el-form-item label="任务描述" prop="description" required>
              <el-input
                v-model="taskFormData.description"
                type="textarea"
                :rows="3"
                placeholder="请输入任务详细描述"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </div>
        </div>

        <!-- 关联信息 -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <el-icon class="mr-2 text-primary"><User /></el-icon>
            关联信息
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="关联学生" prop="studentName" required>
              <el-select v-model="taskFormData.studentName" placeholder="请选择关联学生" class="w-full" filterable>
                <el-option label="张三" value="张三" />
                <el-option label="李四" value="李四" />
                <el-option label="王五" value="王五" />
                <el-option label="赵六" value="赵六" />
                <el-option label="孙七" value="孙七" />
              </el-select>
            </el-form-item>
            <el-form-item label="负责人" prop="assignedTo" required>
              <el-select v-model="taskFormData.assignedTo" placeholder="请选择负责人" class="w-full">
                <el-option label="李老师" value="李老师" />
                <el-option label="王老师" value="王老师" />
                <el-option label="张老师" value="张老师" />
                <el-option label="刘老师" value="刘老师" />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 任务设置 -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <el-icon class="mr-2 text-primary"><Setting /></el-icon>
            任务设置
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="任务阶段" prop="stage" required>
              <el-select v-model="taskFormData.stage" placeholder="请选择任务阶段" class="w-full">
                <el-option label="资料收集" value="materials" />
                <el-option label="定校选校" value="school-selection" />
                <el-option label="文书写作" value="writing" />
                <el-option label="网申递交" value="application" />
                <el-option label="结果跟踪" value="tracking" />
              </el-select>
            </el-form-item>
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="taskFormData.priority" placeholder="请选择优先级" class="w-full">
                <el-option label="紧急" value="urgent">
                  <div class="flex items-center">
                    <span class="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                    紧急
                  </div>
                </el-option>
                <el-option label="重要" value="important">
                  <div class="flex items-center">
                    <span class="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
                    重要
                  </div>
                </el-option>
                <el-option label="普通" value="normal">
                  <div class="flex items-center">
                    <span class="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    普通
                  </div>
                </el-option>
                <el-option label="较低" value="low">
                  <div class="flex items-center">
                    <span class="w-2 h-2 bg-gray-500 rounded-full mr-2"></span>
                    较低
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="截止日期" prop="dueDate" required>
              <el-date-picker
                v-model="taskFormData.dueDate"
                type="datetime"
                placeholder="请选择截止日期"
                class="w-full"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            <el-form-item label="任务状态" prop="status" v-if="isEditMode">
              <el-select v-model="taskFormData.status" placeholder="请选择任务状态" class="w-full">
                <el-option label="待处理" value="pending" />
                <el-option label="进行中" value="in_progress" />
                <el-option label="已完成" value="completed" />
                <el-option label="已取消" value="cancelled" />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 备注信息 -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <el-icon class="mr-2 text-primary"><Edit /></el-icon>
            备注信息
          </h3>
          <el-form-item label="备注说明" prop="notes">
            <el-input
              v-model="taskFormData.notes"
              type="textarea"
              :rows="4"
              placeholder="请输入备注信息，如任务要求、注意事项等"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleTaskFormCancel">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleTaskFormSubmit"
            :loading="taskFormSubmitting"
          >
            {{ isEditMode ? '更新任务' : '创建任务' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  DocumentCopy, Warning, Clock, Check, Search, Plus, User, Setting, Edit,
  MoreFilled, Delete, Refresh
} from '@element-plus/icons-vue'


const router = useRouter()

// 响应式数据
const searchQuery = ref('')
const stageFilter = ref('')
const priorityFilter = ref('')
const tasks = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)

// 任务表单对话框相关
const taskDialogVisible = ref(false)
const taskFormData = ref({
  title: '',
  description: '',
  studentId: '',
  studentName: '',
  stage: 'materials',
  priority: 'normal',
  dueDate: null,
  assignedTo: '',
  status: 'pending',
  notes: ''
})
const taskFormSubmitting = ref(false)
const taskFormRef = ref()
const isEditMode = ref(false)

// 表单验证规则
const taskFormRules = {
  title: [
    { required: true, message: '请输入任务标题', trigger: 'blur' },
    { min: 2, max: 50, message: '标题长度应在2-50个字符之间', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入任务描述', trigger: 'blur' }
  ],
  studentName: [
    { required: true, message: '请选择关联学生', trigger: 'change' }
  ],
  stage: [
    { required: true, message: '请选择任务阶段', trigger: 'change' }
  ],
  assignedTo: [
    { required: true, message: '请选择负责人', trigger: 'change' }
  ],
  dueDate: [
    { required: true, message: '请选择截止日期', trigger: 'change' }
  ]
}

// 计算属性
const filteredAndSortedTasks = computed(() => {
  let filtered = tasks.value

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(task => 
      task.title.toLowerCase().includes(query) ||
      task.client_name.toLowerCase().includes(query) ||
      task.description.toLowerCase().includes(query)
    )
  }

  // 阶段筛选
  if (stageFilter.value) {
    filtered = filtered.filter(task => task.stage === stageFilter.value)
  }

  // 优先级筛选
  if (priorityFilter.value) {
    filtered = filtered.filter(task => task.priority === priorityFilter.value)
  }

  // 默认排序：优先级 -> 截止日期
  filtered.sort((a, b) => {
    const priorityOrder = { urgent: 0, important: 1, normal: 2, low: 3 }
    const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority]
    if (priorityDiff !== 0) return priorityDiff
    return new Date(a.due_date) - new Date(b.due_date)
  })

  return filtered
})

const totalTasks = computed(() => tasks.value.length)
const urgentTasks = computed(() => tasks.value.filter(task => task.priority === 'urgent').length)
const todayDueTasks = computed(() => {
  const today = new Date().toDateString()
  return tasks.value.filter(task => 
    new Date(task.due_date).toDateString() === today
  ).length
})
const completedTasks = computed(() => tasks.value.filter(task => task.status === 'completed').length)

// 方法
const getStudentInitials = (name) => {
  if (!name) return 'N'
  const names = name.split(' ')
  if (names.length === 1) {
    return names[0].charAt(0).toUpperCase()
  }
  return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase()
}

const getPriorityIndicatorClass = (priority) => {
  const classMap = {
    urgent: 'bg-red-500 animate-pulse',
    important: 'bg-orange-500',
    normal: 'bg-[#4F46E5]',
    low: 'bg-gray-400'
  }
  return classMap[priority] || 'bg-gray-400'
}

const getPriorityTextClass = (priority) => {
  const classMap = {
    urgent: 'text-red-600',
    important: 'text-orange-600',
    normal: 'text-[#4F46E5]',
    low: 'text-gray-600'
  }
  return classMap[priority] || 'text-gray-600'
}

const getPriorityLabel = (priority) => {
  const labelMap = {
    urgent: '紧急',
    important: '重要',
    normal: '普通',
    low: '低优先级'
  }
  return labelMap[priority] || '未知'
}

const getStageTagType = (stage) => {
  const typeMap = {
    materials: 'primary',
    'school-selection': 'success',
    writing: 'warning',
    application: 'danger'
  }
  return typeMap[stage] || 'info'
}

const getStageLabel = (stage) => {
  const labelMap = {
    materials: '资料收集',
    'school-selection': '定校确认',
    writing: '文书撰写',
    application: '网申递交'
  }
  return labelMap[stage] || '未知阶段'
}

const getStatusTagType = (status) => {
  const typeMap = {
    pending: 'warning',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusLabel = (status) => {
  const labelMap = {
    pending: '待处理',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return labelMap[status] || '未知状态'
}

const getDueDateClass = (dueDate) => {
  if (isOverdue(dueDate)) {
    return 'text-red-600'
  } else if (isDueSoon(dueDate)) {
    return 'text-orange-600'
  }
  return 'text-gray-700'
}

const formatDueDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInDays = Math.ceil((date - now) / (1000 * 60 * 60 * 24))
  
  if (diffInDays < 0) {
    return `逾期 ${Math.abs(diffInDays)} 天`
  } else if (diffInDays === 0) {
    return '今天到期'
  } else if (diffInDays === 1) {
    return '明天到期'
  } else if (diffInDays <= 7) {
    return `${diffInDays} 天后到期`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

const isOverdue = (dueDate) => {
  return new Date(dueDate) < new Date()
}

const isDueSoon = (dueDate) => {
  const due = new Date(dueDate)
  const now = new Date()
  const diffInDays = (due - now) / (1000 * 60 * 60 * 24)
  return diffInDays <= 3 && diffInDays > 0
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleTaskClick = (task) => {
  // 跳转到客户详情页相关模块
  router.push(`/crm/clients/${task.client_id}?tab=${task.stage}`)
}

const handleTaskUpdate = async (task) => {
  try {
    // TODO: 调用更新任务API
    ElMessage.success('任务更新成功')
    await fetchTasks()
  } catch (error) {
    ElMessage.error('更新失败：' + error.message)
  }
}

const handleAddTask = () => {
  // 打开添加任务对话框
  openTaskDialog()
}

const handleEditTask = (task) => {
  // 打开编辑任务对话框
  openTaskDialog(task)
}

const handleCompleteTask = async (task) => {
  try {
    await ElMessageBox.confirm(`确定要标记任务"${task.title}"为已完成吗？`, '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'success'
    })
    
    // TODO: 调用完成任务API
    ElMessage.success('任务已标记为完成')
    await fetchTasks()
  } catch (error) {
    // 用户取消操作
  }
}

const handleReopenTask = async (task) => {
  try {
    // TODO: 调用重新打开任务API
    ElMessage.success('任务已重新打开')
    await fetchTasks()
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  }
}

const handleDeleteTask = async (task) => {
  try {
    await ElMessageBox.confirm(`确定要删除任务"${task.title}"吗？此操作不可恢复！`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // TODO: 调用删除任务API
    ElMessage.success('任务删除成功')
    await fetchTasks()
  } catch (error) {
    // 用户取消操作
  }
}

// 任务表单对话框处理函数
const openTaskDialog = (task = null) => {
  isEditMode.value = task !== null
  
  if (task) {
    // 编辑模式，填充数据
    taskFormData.value = {
      id: task.id,
      title: task.title,
      description: task.description,
      studentId: task.client_id,
      studentName: task.client_name,
      stage: task.stage,
      priority: task.priority,
      dueDate: task.due_date ? new Date(task.due_date) : null,
      assignedTo: task.assignedTo || '',
      status: task.status,
      notes: task.notes || ''
    }
  } else {
    // 新增模式，重置表单
    resetTaskForm()
  }
  
  taskDialogVisible.value = true
}

const resetTaskForm = () => {
  taskFormData.value = {
    title: '',
    description: '',
    studentId: '',
    studentName: '',
    stage: 'materials',
    priority: 'normal',
    dueDate: null,
    assignedTo: '',
    status: 'pending',
    notes: ''
  }
}

const handleTaskFormSubmit = async () => {
  try {
    // 验证表单
    await taskFormRef.value.validate()
    
    taskFormSubmitting.value = true

    // TODO: 调用API保存数据
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用

    ElMessage.success(isEditMode.value ? '任务更新成功' : '任务创建成功')
    
    taskDialogVisible.value = false
    fetchTasks() // 刷新列表

  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(isEditMode.value ? '更新失败，请重试' : '创建失败，请重试')
    }
  } finally {
    taskFormSubmitting.value = false
  }
}

const handleTaskFormCancel = () => {
  taskDialogVisible.value = false
}

const fetchTasks = async () => {
  try {
    loading.value = true
    // TODO: 调用获取任务列表API
    // 临时模拟数据
    const now = new Date()
    const tomorrow = new Date(now)
    tomorrow.setDate(tomorrow.getDate() + 1)
    const nextWeek = new Date(now)
    nextWeek.setDate(nextWeek.getDate() + 7)
    
    tasks.value = [
      {
        id: 1,
        title: '收集成绩单',
        description: '收集张三的本科成绩单及英文翻译件',
        client_id: 1,
        client_name: '张三',
        stage: 'materials',
        priority: 'urgent',
        status: 'pending',
        due_date: now.toISOString(),
        created_at: '2024-01-15T10:30:00Z',
        updated_at: '2024-01-20T15:20:00Z'
      },
      {
        id: 2,
        title: '完成PS初稿',
        description: '撰写李四的个人陈述初稿',
        client_id: 2,
        client_name: '李四',
        stage: 'writing',
        priority: 'important',
        status: 'in_progress',
        due_date: tomorrow.toISOString(),
        created_at: '2024-01-10T09:00:00Z',
        updated_at: '2024-01-22T11:45:00Z'
      },
      {
        id: 3,
        title: '提交哈佛申请',
        description: '完成王五哈佛大学网申系统提交',
        client_id: 3,
        client_name: '王五',
        stage: 'application',
        priority: 'urgent',
        status: 'pending',
        due_date: tomorrow.toISOString(),
        created_at: '2024-01-05T14:20:00Z',
        updated_at: '2024-01-21T16:30:00Z'
      },
      {
        id: 4,
        title: '确认定校书',
        description: '与赵六确认最终定校清单',
        client_id: 4,
        client_name: '赵六',
        stage: 'school-selection',
        priority: 'normal',
        status: 'pending',
        due_date: nextWeek.toISOString(),
        created_at: '2024-01-12T16:00:00Z',
        updated_at: '2024-01-23T10:15:00Z'
      },
      {
        id: 5,
        title: '语言成绩单认证',
        description: '协助孙七完成托福成绩单WES认证',
        client_id: 5,
        client_name: '孙七',
        stage: 'materials',
        priority: 'normal',
        status: 'completed',
        due_date: '2024-01-20T00:00:00Z',
        created_at: '2024-01-08T14:30:00Z',
        updated_at: '2024-01-19T09:20:00Z'
      }
    ]
  } catch (error) {
    ElMessage.error('获取任务列表失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchTasks()
})
</script>

<style>
@import './styles/crm-theme.css';
</style>

<style scoped>
.task-table :deep(.el-table__row) {
  cursor: pointer;
}

.task-table :deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style> 