<template>
  <div class="ai-reducer-page bg-gray-50">
    <div class="max-w-7xl mx-auto py-8">
      <!-- 页面标题区域 -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">TunshuEdu降AI工具</h1>
        <p class="text-gray-600 text-lg">
          TunshuEdu独家自主研发的拟人化AI痕迹清理神器，将你的文章的留学申请学院校检查优化。
        </p>
      </div>

      <!-- 主要内容区域 -->
      <div class="bg-white rounded-2xl shadow-lg p-8">
        <!-- 输入区域 -->
        <div class="mb-8">
          <!-- 输入标题和使用技巧 -->
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold text-gray-800">输入内容</h2>
            <button class="text-blue-500 hover:text-blue-600 text-sm flex items-center">
              <span class="material-icons-outlined text-sm mr-1">help_outline</span>
              使用技巧
            </button>
          </div>

          <!-- 提示信息 -->
          <div class="text-sm text-gray-500 mb-4">
            请输入要清理AI痕迹的文本内容（仅支持英文）
          </div>

          <!-- 文本输入框 -->
          <div class="relative">
            <textarea
              v-model="inputText"
              placeholder="请在此输入您需要降低AI率的英文内容..."
              class="w-full h-96 p-4 border border-gray-200 rounded-xl focus:border-blue-500 resize-none text-base leading-relaxed"
              :disabled="isProcessing"
            ></textarea>
            
            <!-- 字数统计 -->
            <div class="absolute bottom-3 right-3 text-xs text-gray-400">
              {{ inputText.length }} 字符
            </div>
          </div>
        </div>

        <!-- 降AI率结果显示 -->
        <div v-if="reducedText" class="bg-green-50 rounded-xl p-6 mb-8">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">降AI率结果</h3>
          <div class="space-y-3">
            <div class="bg-white rounded-lg p-4 max-h-64 overflow-y-auto">
              <div class="text-sm text-gray-700 leading-relaxed whitespace-pre-wrap">{{ reducedText }}</div>
            </div>
            <button
              @click="handleCopyResult"
              class="w-full px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg text-sm transition-colors"
            >
              复制结果
            </button>
          </div>
        </div>

        <!-- AI检测结果看板 -->
        <div v-if="detectionResult" class="mt-8 pt-6 border-t border-gray-200">
          <!-- 结果头部 -->
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-semibold text-gray-800">AI率检测结果</h2>
            <button
              @click="detectionResult = null"
              class="text-gray-400 hover:text-gray-600"
            >
              &times; 关闭
            </button>
          </div>

          <!-- 仪表盘和高亮文本 -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- 左侧仪表盘 -->
            <div class="md:col-span-1 flex flex-col items-center justify-center bg-gray-50 rounded-xl p-6">
              <svg width="250" height="160" viewBox="0 0 250 160" class="mx-auto">
                <path d="M 25 140 A 100 100 0 0 1 225 140" stroke="#e5e7eb" stroke-width="18" fill="none" />
                <path :d="getArcPath(detectionResult.aiRate)" :stroke="getArcColor(detectionResult.aiRate)" stroke-width="18" fill="none" stroke-linecap="round" />
              </svg>
              <div class="text-center -mt-8">
                <div class="text-5xl font-bold" :class="getAiRateColor(detectionResult.aiRate)">
                  {{ detectionResult.aiRate }}%
                </div>
                <div class="text-lg text-gray-600">AI Generated</div>
              </div>
              <p class="text-center text-sm text-gray-500 mt-4" :class="getAiRateColor(detectionResult.aiRate)">
                {{ getAiRateMessage(detectionResult.aiRate) }}
              </p>
            </div>

            <!-- 右侧高亮文本 -->
            <div class="md:col-span-2">
              <div class="border rounded-lg overflow-hidden h-96">
                <div class="bg-gray-50 px-4 py-2 border-b">
                  <h3 class="font-medium text-gray-700">文本分析 (AI内容已高亮)</h3>
                </div>
                <div class="p-4 overflow-y-auto h-full bg-white" v-html="detectionResult.highlighted_html"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部操作区域 -->
        <div class="mt-8 pt-6 border-t border-gray-200">
          <div class="flex justify-between items-center">
            <!-- 左侧信息 -->
            <div class="text-sm text-gray-500">
              <span class="material-icons-outlined text-sm mr-1">info</span>
              建议单次处理文本不超过3000字符以获得最佳效果
            </div>

            <!-- 右侧操作按钮 -->
            <div class="flex items-center space-x-4">
              <!-- 模型版本选择 -->
              <div class="text-sm text-gray-600">
                TunshuEdu消重模型 V4.0.0
              </div>

              <!-- 操作按钮 -->
              <div class="flex space-x-3">
                <button
                  @click="handleDetectAI"
                  :disabled="!inputText.trim() || isProcessing"
                  class="px-6 py-2 border border-blue-500 text-blue-500 hover:bg-blue-50 rounded-lg text-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span v-if="isDetecting" class="flex items-center">
                    <span class="material-icons-outlined text-sm mr-1 animate-spin">refresh</span>
                    检测中...
                  </span>
                  <span v-else>检测AI率</span>
                </button>

                <button
                  @click="handleReduceAI"
                  :disabled="!inputText.trim() || isProcessing"
                  class="px-6 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg text-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  <span v-if="isReducing" class="flex items-center">
                    <span class="material-icons-outlined text-sm mr-1 animate-spin">refresh</span>
                    降AI率处理中...
                  </span>
                  <span v-else class="flex items-center">
                    <span class="material-icons-outlined text-sm mr-1">auto_awesome</span>
                    降AI率
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能说明区域 -->
      <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- AI检测功能说明 -->
        <div class="bg-white rounded-xl shadow-sm p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <span class="material-icons-outlined text-blue-500 mr-2">search</span>
            AI率检测功能
          </h3>
          <div class="space-y-3 text-sm text-gray-600">
            <div class="flex items-start">
              <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
              <div>多模型AI检测引擎，提供精确的AI生成内容识别</div>
            </div>
            <div class="flex items-start">
              <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
              <div>段落级别分析，精确定位需要修改的部分</div>
            </div>
            <div class="flex items-start">
              <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
              <div>实时检测结果，快速评估文本原创性</div>
            </div>
          </div>
        </div>

        <!-- AI降低功能说明 -->
        <div class="bg-white rounded-xl shadow-sm p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <span class="material-icons-outlined text-primary mr-2">auto_awesome</span>
            AI率降低功能
          </h3>
          <div class="space-y-3 text-sm text-gray-600">
            <div class="flex items-start">
              <span class="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
              <div>智能改写引擎，保留原意的同时降低AI痕迹</div>
            </div>
            <div class="flex items-start">
              <span class="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
              <div>多种写作风格选项，适应不同申请需求</div>
            </div>
            <div class="flex items-start">
              <span class="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
              <div>前后对比分析，直观展示优化效果</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { detectAIContent } from '@/api/aidetection'
import { ElMessage } from 'element-plus'

// 响应式数据
const inputText = ref('')
const detectionResult = ref(null)
const reducedText = ref('')
const isDetecting = ref(false)
const isReducing = ref(false)

// 检测详情数据
const detectionDetails = ref([])

// 计算属性
const isProcessing = computed(() => isDetecting.value || isReducing.value)

// AI率颜色判断
const getAiRateColor = (rate) => {
  if (rate >= 70) return 'text-red-500'
  if (rate >= 40) return 'text-yellow-500'
  return 'text-green-500'
}

const getAiRateColorBg = (rate) => {
  if (rate >= 70) return 'bg-red-500'
  if (rate >= 40) return 'bg-yellow-500'
  return 'bg-green-500'
}

// SVG仪表盘相关方法
const getArcPath = (rate) => {
  const angle = (rate / 100) * 180;
  const radian = (angle * Math.PI) / 180;
  const centerX = 125;
  const centerY = 140;
  const radius = 100;
  const x = centerX + radius * Math.cos(Math.PI - radian);
  const y = centerY - radius * Math.sin(Math.PI - radian);
  const largeArc = angle > 90 ? 1 : 0;
  return `M 25 140 A 100 100 0 ${largeArc} 1 ${x} ${y}`;
};

const getArcColor = (rate) => {
  if (rate >= 70) return '#ef4444' // 红色
  if (rate >= 40) return '#f59e0b' // 黄色
  return '#10b981' // 绿色
}

const getPointerTransform = (rate) => {
  const angle = (rate / 100) * 180 - 90 // 指针角度
  return `rotate(${angle} 175 180)`
}

const getAiRateMessage = (rate) => {
  if (rate >= 70) return 'GPTZERO显示AI痕迹较明显，建议进行降AI率处理。'
  if (rate >= 40) return 'GPTZERO显示可能有AI痕迹。'
  return 'GPTZERO显示内容原创性较高。'
}

// 检测AI率
const handleDetectAI = async () => {
  if (!inputText.value.trim()) {
    ElMessage.warning('请输入要检测的文本内容');
    return;
  }
  
  isDetecting.value = true;
  try {
    const response = await detectAIContent(inputText.value.trim());
    if (response && response.result) {
      const result = response.result;
      detectionResult.value = {
        aiRate: Math.round(result.fake_percentage),
        highlighted_html: result.highlighted_html,
        // 其他需要的数据...
      };
      ElMessage.success(`检测完成！AI率: ${detectionResult.value.aiRate}%`);
    } else {
      throw new Error('API响应格式异常');
    }
  } catch (error) {
    console.error('AI检测失败:', error);
    ElMessage.error('AI检测失败，请稍后重试');
  } finally {
    isDetecting.value = false;
  }
};

// 降低AI率
const handleReduceAI = async () => {
  if (!inputText.value.trim()) {
    ElMessage.warning('请输入要处理的文本内容');
    return;
  }
  
  isReducing.value = true;
  try {
    // 清除上一次的AI检测结果
    detectionResult.value = null;

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 模拟降AI率结果
    reducedText.value = `This is a refined version of your text with reduced AI detection markers. The content has been restructured to maintain original meaning while adopting a more natural, human writing style...

${inputText.value.substring(0, 200)}...

(This is a demo result. The actual AI reduction will provide comprehensive text optimization.)`;
    
    ElMessage.success('降AI率处理完成！');
    
  } catch (error) {
    console.error('降AI率失败:', error);
    ElMessage.error('处理失败，请稍后重试');
  } finally {
    isReducing.value = false;
  }
};

// 复制结果
const handleCopyResult = async () => {
  try {
    await navigator.clipboard.writeText(reducedText.value)
    // 这里可以添加成功提示
    console.log('复制成功')
  } catch (error) {
    console.error('复制失败:', error)
  }
}
</script>

<style scoped>
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: 'liga';
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 浮窗动画 */
.fixed.inset-0 {
  animation: fadeIn 0.3s ease-out;
}

.fixed.inset-0 > div {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { 
    opacity: 0; 
    transform: scale(0.9) translateY(-20px); 
  }
  to { 
    opacity: 1; 
    transform: scale(1) translateY(0); 
  }
}

/* 新增高亮样式 */
:deep(.ai-highlight) {
  background-color: #fef08a; /* 黄色高亮 */
  padding: 0.1em 0.2em;
  border-radius: 3px;
}
</style> 