<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="/logo.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-eval' 'unsafe-inline'; object-src 'none';" />
    <title>TunshuEdu - 留学行业的AI工具箱</title>

    <!-- 预连接常用字体域名，减少加载时间 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- 预加载核心字体 - 优先加载常用字重和字符 -->
    <link rel="preload" as="style" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap&text=abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789.,;:?!(){}[]">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap&text=abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789.,;:?!(){}[]">

    <!-- Material Icons (用于UI图标) - 移除预加载，直接使用样式表 -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined&display=swap">

    <!-- 使用font-display策略 -->
    <style>
      /* 定义字体显示策略 */
      @font-face {
        font-family: 'Inter';
        font-style: normal;
        font-weight: 400;
        font-display: swap; /* 在字体加载前使用系统字体 */
        src: local('Inter Regular'), local('Inter-Regular');
      }
    </style>

    <!-- 添加自定义样式以防止闪烁 -->
    <style>
      body {
        font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        font-size: 16px;
        line-height: 1.5;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      /* 预定义字体类，减少运行时计算 */
      .font-sans {
        font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      }

      /* 加载动画，在应用加载时显示 */
      .app-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #f9fafb;
        z-index: 9999;
        transition: opacity 0.5s, visibility 0.5s;
      }
      .app-loading.hidden {
        opacity: 0;
        visibility: hidden;
      }
      .app-loading-logo {
        width: 80px;
        height: 80px;
        background: linear-gradient(to right, #4F46E5, #818CF8);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 24px;
        margin-bottom: 20px;
        box-shadow: 0 10px 15px -3px rgba(79, 70, 229, 0.2);
        animation: pulse 2s infinite;
      }
      .app-loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #e6e8eb;
        border-top-color: #4F46E5;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
      }
    </style>
  </head>
  <body class="bg-white">
    <!-- 应用加载中的占位UI -->
    <div id="app-loading" class="app-loading">
      <div class="app-loading-logo">
        <span>TS</span>
      </div>
      <div class="app-loading-spinner"></div>
    </div>

    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>

    <!-- 在应用加载完成后隐藏加载界面 -->
    <script>
      window.addEventListener('load', function() {
        // 减少延迟时间，更快地显示应用内容
        setTimeout(function() {
          var loader = document.getElementById('app-loading');
          if (loader) {
            loader.classList.add('hidden');
            // 完全移除加载元素
            setTimeout(function() {
              loader.parentNode.removeChild(loader);
            }, 300); // 减少过渡时间
          }
        }, 200); // 减少延迟时间
      });
    </script>
  </body>
</html>
