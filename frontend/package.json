{"name": "tunshuedu-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@heroicons/vue": "^2.0.18", "@tiptap/extension-placeholder": "^2.24.0", "@tiptap/extension-text-align": "^2.24.0", "@tiptap/extension-typography": "^2.24.0", "@tiptap/extension-underline": "^2.24.0", "@tiptap/starter-kit": "^2.24.0", "@tiptap/vue-3": "^2.24.0", "@types/sortablejs": "^1.15.8", "@vueuse/core": "^10.7.0", "axios": "^1.6.2", "docx": "^9.5.1", "docx-preview": "^0.3.5", "element-plus": "^2.4.3", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1", "marked": "^16.0.0", "pinia": "^2.1.7", "pizzip": "^3.2.0", "sass": "^1.89.2", "sortablejs": "^1.15.6", "turndown": "^7.2.0", "vue": "^3.5.16", "vue-router": "^4.5.1"}, "devDependencies": {"@stagewise-plugins/vue": "^0.4.6", "@stagewise/toolbar-vue": "^0.4.8", "@vitejs/plugin-vue": "^6.0.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.25.2", "vite": "^7.0.3"}}